"""
Script para listar os spiders disponíveis no Scrapy
"""

import sys
import os
import logging
from scrapy.crawler import CrawlerProcess
from scrapy.utils.project import get_project_settings

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def list_spiders():
    """Lista os spiders disponíveis no Scrapy"""
    try:
        # Obtém as configurações do projeto
        settings = get_project_settings()
        
        # Cria o processo
        process = CrawlerProcess(settings)
        
        # Lista os spiders disponíveis
        spiders = process.spider_loader.list()
        
        logger.info(f"Spiders disponíveis: {spiders}")
        
        # Exibe informações detalhadas sobre cada spider
        for spider_name in spiders:
            spider_cls = process.spider_loader.load(spider_name)
            logger.info(f"Spider: {spider_name}")
            logger.info(f"  Classe: {spider_cls.__name__}")
            logger.info(f"  Módulo: {spider_cls.__module__}")
            logger.info(f"  Domínios permitidos: {getattr(spider_cls, 'allowed_domains', [])}")
            logger.info(f"  Start URLs: {getattr(spider_cls, 'start_urls', [])}")
            logger.info("---")
        
        return spiders
    except Exception as e:
        logger.error(f"Erro ao listar spiders: {e}")
        return []

if __name__ == "__main__":
    # Exibe o diretório atual
    logger.info(f"Diretório atual: {os.getcwd()}")
    
    # Exibe o PYTHONPATH
    logger.info(f"PYTHONPATH: {sys.path}")
    
    # Lista os spiders
    spiders = list_spiders()
    
    if not spiders:
        logger.error("Nenhum spider encontrado!")
        sys.exit(1)
    
    logger.info(f"Total de spiders encontrados: {len(spiders)}")
