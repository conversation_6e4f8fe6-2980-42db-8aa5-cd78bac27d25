"""
Módulo de modelos para a API do Promoserver.
Contém as classes de modelo utilizadas para validação de dados e serialização.
"""

from typing import List, Optional
from pydantic import BaseModel

from src.models.product_supabase import ProdutoSupabase
from src.models.product_supabase_get import ProdutoSupabaseGet

# --- Modelos de Autenticação ---


class LoginInput(BaseModel):
    """Modelo para dados de login."""

    email: str
    password: str


# --- Modelos de Seletores ---


class SelectorData(BaseModel):
    """Modelo para dados de seletores."""

    store_id: str
    type: str
    selector: str
    description: Optional[str] = ""
    active: bool


class SelectorOutput(SelectorData):
    """Modelo para resposta de seletores."""

    id: int
    store: str


# --- Modelos de Produtos ---


class ScrapedProductResponse(BaseModel):
    """Modelo para resposta de produtos raspados."""

    platform: str
    productId: Optional[str] = None
    affiliateUrl: str
    productUrl: Optional[str] = None
    imageUrl: Optional[str] = None
    title: str
    description: Optional[str] = None
    price: Optional[float] = None
    oldPrice: Optional[float] = None
    installments: Optional[str] = None
    couponInfo: Optional[str] = None
    categoryKey: Optional[str] = None
    subcategoryIndex: Optional[str] = None
    category: Optional[str] = None
    subcategory: Optional[str] = None
    shipping: Optional[str] = None
    error: Optional[bool] = False

    class Config:
        # Isso permite que o modelo aceite tanto camelCase quanto snake_case
        populate_by_name = True

        # Define aliases para compatibilidade com código existente
        field_aliases = {
            "productId": "product_id",
            "affiliateUrl": "urlAfiliado",
            "productUrl": "url_produto",
            "imageUrl": "image_url",
            "oldPrice": "old_price",
            "couponInfo": "coupon_info",
            "categoryKey": "category_key",
            "subcategoryIndex": "subcategory_idx",
        }


class ProductSaveInput(BaseModel):
    """Modelo para entrada de salvamento de produtos."""

    plataforma: str
    url_produto: str
    url_afiliado: str
    url_imagem: Optional[str] = None
    image_id: Optional[str] = None  # ID da imagem no Digital Ocean
    titulo: str
    categoria: Optional[str] = None
    subcategoria: Optional[str] = None
    descricao: Optional[str] = ""
    preco_atual: Optional[float] = 0.0
    preco_antigo: Optional[float] = 0.0
    cupom: Optional[str] = None
    menor_preco: bool = False
    indicamos: bool = False
    disparar_whatsapp: bool = False
    grupo_whatsapp: Optional[str] = None
    frete: bool = False
    ativo: bool = True
    preco_alternativo: Optional[float] = 0.0
    isStory: bool = False
    invalidProduct: bool = False

    def to_dict(self):
        """Converte o modelo para um dicionário."""
        # Compatibilidade com Pydantic v1 e v2
        try:
            # Pydantic v2
            return self.model_dump(exclude_unset=True)
        except AttributeError:
            # Pydantic v1
            return self.dict(exclude_unset=True)

    def to_supabase_model(self) -> ProdutoSupabase:
        """Converte o modelo de entrada para o modelo do Supabase."""
        # Compatibilidade com Pydantic v1 e v2
        try:
            # Pydantic v2
            data = self.model_dump(exclude_unset=True)
        except AttributeError:
            # Pydantic v1
            data = self.dict(exclude_unset=True)

        # Garantir que os campos numéricos sejam float
        data["preco_atual"] = float(data.get("preco_atual", 0.0) or 0.0)
        data["preco_antigo"] = float(data.get("preco_antigo", 0.0) or 0.0)
        data["preco_alternativo"] = float(data.get("preco_alternativo", 0.0) or 0.0)
        return ProdutoSupabase(**data)


class ProductGetResponse(BaseModel):
    """Modelo para resposta de produtos."""

    id: str  # Adicionado campo id
    plataforma: str
    url_produto: Optional[str] = None
    url_afiliado: str
    url_imagem: Optional[str] = None
    image_id: Optional[str] = None  # ID da imagem no Digital Ocean
    titulo: str
    categoria: Optional[str] = None
    subcategoria: Optional[str] = None
    descricao: Optional[str] = None
    preco_atual: float = 0.0
    preco_antigo: float = 0.0
    preco_alternativo: float = 0.0
    ativo: bool = True
    cupom: Optional[str] = None
    menor_preco: bool = False
    indicamos: bool = False
    disparar_whatsapp: bool = False
    grupo_whatsapp: Optional[str] = None
    frete: bool = False
    invalidProduct: bool = False
    isStory: bool = False
    criado_em: Optional[str] = None  # Adicionado campo criado_em

    @classmethod
    def from_supabase_get(cls, db_product: ProdutoSupabaseGet):
        """Converte o modelo do Supabase para o modelo de resposta."""
        # Verificar se o ID está presente e não é vazio
        if not hasattr(db_product, "id") or not db_product.id:
            import logging

            log = logging.getLogger("api.products")
            log.warning(f"Produto sem ID válido encontrado: {db_product.titulo}")

        # Converter a data de criação para string se existir
        criado_em_str = None
        if hasattr(db_product, "criado_em") and db_product.criado_em:
            try:
                criado_em_str = (
                    db_product.criado_em.isoformat()
                    if hasattr(db_product.criado_em, "isoformat")
                    else str(db_product.criado_em)
                )
            except Exception as e:
                import logging

                log = logging.getLogger("api.products")
                log.warning(f"Erro ao converter data de criação para string: {e}")

        return cls(
            id=str(db_product.id),  # Converter o ID para string
            plataforma=db_product.plataforma,
            url_produto=db_product.url_produto,
            url_afiliado=db_product.url_afiliado,
            url_imagem=db_product.url_imagem,
            image_id=db_product.image_id,
            titulo=db_product.titulo,
            categoria=db_product.categoria,
            subcategoria=db_product.subcategoria,
            descricao=db_product.descricao,
            preco_atual=float(db_product.preco_atual or 0.0),
            preco_antigo=float(db_product.preco_antigo or 0.0),
            preco_alternativo=float(db_product.preco_alternativo or 0.0),
            ativo=db_product.ativo,
            cupom=db_product.cupom,
            menor_preco=db_product.menor_preco,
            indicamos=db_product.indicamos,
            disparar_whatsapp=db_product.disparar_whatsapp,
            grupo_whatsapp=db_product.grupo_whatsapp,
            frete=bool(db_product.frete) if db_product.frete is not None else False,
            isStory=getattr(db_product, "isStory", False),
            invalidProduct=getattr(db_product, "invalidProduct", False),
            criado_em=criado_em_str,  # Incluir a data de criação
        )


# --- Modelos de Categorias ---


class CategoryResponse(BaseModel):
    """Modelo para resposta de categorias."""

    key: str
    name: str
    subcategories: List[str]


# --- Modelos do PromoHunter ---


class OfferCategoryResponse(BaseModel):
    """Modelo para resposta de categorias de ofertas."""

    id: Optional[int] = None
    name: str
    store: str
    url_template: str
    max_page: int = 2
    active: bool = True


class OfferScraperResponse(BaseModel):
    """Modelo para resposta do scraper de ofertas."""

    status: str
    message: str
    total_products: int = 0
    processed_categories: int = 0
    processed_pages: int = 0
    errors: List[str] = []
    is_running: bool = False
    is_completed: bool = False
    has_error: bool = False
