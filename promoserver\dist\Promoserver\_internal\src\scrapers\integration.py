"""
Módulo de integração dos scrapers com o Promoserver
"""

import logging
import asyncio
from typing import Dict, Any, Optional, List

# Importa os scrapers
from src.scrapers.runner import scrape_mercadolivre, scrape_magalu, scrape_amazon

logger = logging.getLogger(__name__)

async def scrape_url(url: str) -> Dict[str, Any]:
    """
    Raspa uma URL usando o scraper apropriado
    
    Args:
        url: URL para raspar
        
    Returns:
        Dados do produto raspado
    """
    logger.info(f"Raspando URL: {url}")
    
    # Determina o scraper a ser usado
    if "mercadolivre.com" in url or "mercadolibre.com" in url:
        logger.info(f"Usando scraper do Mercado Livre para URL: {url}")
        try:
            products = await scrape_mercadolivre(url=url, max_pages=1)
            if products:
                return products[0]
            else:
                return {
                    "platform": "Mercado Livre",
                    "product_id": None,
                    "url_produto": url,
                    "url_afiliado": "",
                    "title": "Não disponível",
                    "description": "Nenhum produto encontrado",
                    "price": "N/A",
                    "old_price": "N/A",
                    "image_url": None,
                    "installments": "N/A",
                    "coupon_info": "N/A",
                    "shipping": "N/A",
                    "error": True,
                    "error_message": "Nenhum produto encontrado"
                }
        except Exception as e:
            logger.error(f"Erro ao raspar URL do Mercado Livre: {e}")
            return {
                "platform": "Mercado Livre",
                "product_id": None,
                "url_produto": url,
                "url_afiliado": "",
                "title": "Não disponível",
                "description": f"Erro ao buscar dados: {e}",
                "price": "N/A",
                "old_price": "N/A",
                "image_url": None,
                "installments": "N/A",
                "coupon_info": "N/A",
                "shipping": "N/A",
                "error": True,
                "error_message": str(e)
            }
    
    elif "magazinevoce.com" in url or "magazineluiza.com" in url:
        logger.info(f"Usando scraper do Magalu para URL: {url}")
        try:
            products = await scrape_magalu(url=url, max_pages=1)
            if products:
                return products[0]
            else:
                return {
                    "platform": "Magalu",
                    "product_id": None,
                    "url_produto": url,
                    "url_afiliado": "",
                    "title": "Não disponível",
                    "description": "Nenhum produto encontrado",
                    "price": "N/A",
                    "old_price": "N/A",
                    "image_url": None,
                    "installments": "N/A",
                    "coupon_info": "N/A",
                    "shipping": "N/A",
                    "error": True,
                    "error_message": "Nenhum produto encontrado"
                }
        except Exception as e:
            logger.error(f"Erro ao raspar URL do Magalu: {e}")
            return {
                "platform": "Magalu",
                "product_id": None,
                "url_produto": url,
                "url_afiliado": "",
                "title": "Não disponível",
                "description": f"Erro ao buscar dados: {e}",
                "price": "N/A",
                "old_price": "N/A",
                "image_url": None,
                "installments": "N/A",
                "coupon_info": "N/A",
                "shipping": "N/A",
                "error": True,
                "error_message": str(e)
            }
    
    elif "amazon.com" in url:
        logger.info(f"Usando scraper da Amazon para URL: {url}")
        try:
            products = await scrape_amazon(url=url, max_pages=1)
            if products:
                return products[0]
            else:
                return {
                    "platform": "Amazon",
                    "product_id": None,
                    "url_produto": url,
                    "url_afiliado": "",
                    "title": "Não disponível",
                    "description": "Nenhum produto encontrado",
                    "price": "N/A",
                    "old_price": "N/A",
                    "image_url": None,
                    "installments": "N/A",
                    "coupon_info": "N/A",
                    "shipping": "N/A",
                    "error": True,
                    "error_message": "Nenhum produto encontrado"
                }
        except Exception as e:
            logger.error(f"Erro ao raspar URL da Amazon: {e}")
            return {
                "platform": "Amazon",
                "product_id": None,
                "url_produto": url,
                "url_afiliado": "",
                "title": "Não disponível",
                "description": f"Erro ao buscar dados: {e}",
                "price": "N/A",
                "old_price": "N/A",
                "image_url": None,
                "installments": "N/A",
                "coupon_info": "N/A",
                "shipping": "N/A",
                "error": True,
                "error_message": str(e)
            }
    
    else:
        logger.error(f"URL não suportada: {url}")
        return {
            "platform": "Desconhecida",
            "product_id": None,
            "url_produto": url,
            "url_afiliado": "",
            "title": "URL não suportada",
            "description": "Esta URL não é suportada pelo sistema de raspagem.",
            "price": "N/A",
            "old_price": "N/A",
            "image_url": None,
            "installments": "N/A",
            "coupon_info": "N/A",
            "shipping": "N/A",
            "error": True,
            "error_message": "URL não suportada"
        }
