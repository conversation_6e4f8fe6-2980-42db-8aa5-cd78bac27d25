from .. import Provider as CompanyProvider


class Provider(CompanyProvider):
    formats = (
        "{{last_name}} {{company_suffix}}",
        "{{last_name}} {{last_name}} {{company_suffix}}",
        "{{large_company}}",
    )
    # Source: https://www.capital.com.tr/listeler/capital-500
    large_companies = (
        "Tüpraş",
        "Türk Hava Yolları",
        "Petrol Ofisi",
        "Opet Petrolcülük",
        "Ford Otosan",
        "BİM Birleşik Mağazalar",
        "Arçelik",
        "Shell&Turcas Petrol",
        "Ereğli Demir Çelik",
        "Türk Telekom",
        "Toyota Otomotiv",
        "Tofaş Oto Fabrika",
        "Turkcell",
        "Oyak Renault",
        "Migros",
        "RC Rönesans İnşaat",
        "Doğuş Otomotiv",
        "Anadolu Efes Biracılık",
        "LC Waikiki Mağazacılık",
        "Enerjisa Enerji",
        "Vodafone",
        "Vestel Elektronik",
        "Mercedes-Benz Türk",
        "Şişecam",
        "İÇDAŞ Çelik Enerji",
        "JTI Tütün Ürünleri Pazarlama",
        "Selçuk Ecza",
        "İskenderun Demir Çelik",
        "Enka İnşaat",
        "Kibar Dış Ticaret",
        "Hyundai Assan Otomotiv",
        "Şok Marketler",
        "İstanbul Altın Rafinerisi",
        "Coca-Cola İçecek",
        "Aygaz",
        "İçtaş İnşaat",
        "BSH Ev Aletleri",
        "Petkim",
        "Otokoç",
        "THY Opet",
        "İGDAŞ İstanbul Gaz Dağıtım",
        "Limak İnşaat",
        "Hayat Kimya",
        "Limak Yatırım",
        "Mapa İnşaat",
        "Aytemiz Akaryakıt Dağıtım",
        "Aselsan",
        "Pegasus Hava Taşımacılığı",
        "TUSAŞ-Türk Havacılık ve Uzay",
        "GAP İnşaat",
        "Tosçelik Profil ve Saç",
        "Unilever",
        "Tekfen İnşaat",
        "Ülker Bisküvi",
        "Çalık Enerji",
        "Indeks Bilgisayar",
        "Güneş Ekspres Havacılık",
        "TAV Havalimanları",
        "CarrefourSA",
        "Borçelik Çelik Sanayi",
        "Sarkuysan",
        "Trakya Cam",
        "Türk Traktör",
        "Mey İçki",
        "Kardemir",
        "İpragaz",
        "Zorlu Enerji",
        "Vestel Beyaz Eşya",
        "Aksa Enerji Üretim",
        "TGS Dış Ticaret",
        "TP Petrol Dağıtım",
        "Gübre Fabrikaları",
        "Gediz Elektrik Perakende Satış",
        "BASF Türkiye",
        "Er-Bakır Elektrolitik Bakır",
        "Teknosa İç ve Dış Ticaret",
        "Kroman Çelik",
        "Eren Enerji",
        "Tiryaki Agro Gıda",
        "Acıbadem Sağlık Hizmetleri",
        "Yücel Boru ve Profil",
        "Baştuğ Metalurji",
        "Eti Gıda",
        "Philsa Philip Morris Sabancı Sigara",
        "Netlog Lojistik Hizmetleri",
        "İzmir Demir Çelik",
        "Assan Alüminyum",
        "Tepe İnşaat",
        "Honda Türkiye",
        "Koton",
        "Bizim Toptan Satış Mağazaları",
        "Aksa Akrilik",
        "Aksa Doğalgaz",
        "Borusan Otomotiv",
        "Kastamonu Entegre",
        "Sepaş Enerji Sakarya Elektrik",
        "TAV Tepe Akfen",
        "Konya Şeker Fabrikası",
    )
    company_suffixes = (
        "A.Ş.",
        "Ltd.",
        "Tic.",
        "San.",
        "Şti.",
    )

    def large_company(self) -> str:
        """
        :example: 'Peak Games'
        """
        return self.random_element(self.large_companies)
