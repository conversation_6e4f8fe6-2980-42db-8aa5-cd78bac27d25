import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../../components/custom_dropdown_dialog.dart';
import '../../controllers/tabs_controller.dart';
import '../../models/product_tab.dart';

class PublishSection extends StatelessWidget {
  final TabsController controller = Modular.get<TabsController>();
  final ThemeData theme;
  final VoidCallback onPublish;

  PublishSection({
    super.key,
    required this.theme,
    required this.onPublish,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, _) {
        final ProductTab? tab = controller.currentTab;
        if (tab == null) {
          return const SizedBox.shrink();
        }
        return Row(
          spacing: 40,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Row(
              children: [
                Switch(
                  value: tab.dispatchWhatsapp,
                  onChanged:
                      tab.isFetching || tab.isPublishing
                          ? null
                          : controller.setDispatchWhatsapp,
                ),
                const Text('Enviar no WhatsApp'),
                const SizedBox(width: 16),
                SizedBox(
                  width: 280,
                  child: CustomDropdownDialog<String>(
                    value: tab.selectedWhatsappGroup,
                    items: controller.availableWhatsappGroups,
                    itemLabelBuilder: (group) => group,
                    onChanged:
                        tab.dispatchWhatsapp &&
                                !tab.isFetching &&
                                !tab.isPublishing
                            ? controller.setSelectedWhatsappGroup
                            : null,
                    label: 'Grupo WhatsApp',
                    prefixIcon: Icons.group,
                    isEnabled:
                        tab.dispatchWhatsapp &&
                        !tab.isFetching &&
                        !tab.isPublishing,
                    validator:
                        (v) =>
                            tab.dispatchWhatsapp && v == null
                                ? 'Selecione um grupo'
                                : null,
                    maxDialogHeight:
                        0.5, // Limita a altura a 50% da tela
                    dialogTitle: 'Selecione o Grupo de WhatsApp',
                  ),
                ),
              ],
            ),
            ElevatedButton.icon(
              icon:
                  tab.isPublishing
                      ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                        ),
                      )
                      : const Icon(Icons.publish),
              label: const Text('Publicar Produto'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 16,
                ),
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: theme.colorScheme.onPrimary,
              ),
              onPressed:
                  tab.scrapedProduct == null ||
                          tab.isFetching ||
                          tab.isPublishing
                      ? null
                      : onPublish,
            ),
          ],
        );
      },
    );
  }
}
