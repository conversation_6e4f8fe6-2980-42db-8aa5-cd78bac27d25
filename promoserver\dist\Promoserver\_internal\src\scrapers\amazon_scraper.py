"""
Adaptador para o scraper da Amazon usando Scrapy
"""

import logging
import re
from typing import Dict, Optional
import asyncio # Adicionado para to_thread se necessário, mas _get_html_with_browser_simulation já é async

from scrapy.http import HtmlResponse # Novo
from .spiders.amazon_spider import AmazonSpider as AmazonScrapySpider # Corrigido para importação relativa correta

from .base.base_scraper import BaseScraper

logger = logging.getLogger(__name__)


class AmazonScraper(BaseScraper):
    """
    Scraper para a Amazon usando Scrapy
    """

    def __init__(self):
        super().__init__(store_id="amazon")
        self.scrapy_available = True
        logger.info("AmazonScraper inicializado com Scrapy")

    def extract_product_id(self, url: str) -> Optional[str]:
        """
        Extrai o ID do produto (ASIN) da URL da Amazon
        """
        if not url:
            return None

        patterns = [
            r"/dp/([A-Z0-9]{10})",
            r"/gp/product/([A-Z0-9]{10})",
            r"/dp%2F([A-Z0-9]{10})",
            r"&ASIN=([A-Z0-9]{10})",
            r"/ASIN/([A-Z0-9]{10})",
        ]
        for pattern in patterns:
            match = re.search(pattern, url, re.IGNORECASE)
            if match:
                return match.group(1).upper()
        
        logger.warning(f"Não foi possível extrair o ASIN da URL: {url}")
        return None

    async def scrape(self, url: str) -> Dict:
        """
        Raspa os detalhes do produto da Amazon usando Scrapy
        """
        logger.info(f"Iniciando raspagem para Amazon URL: {url} usando BrowserSimulator + Scrapy parser")

        try:
            html_content = await self._get_html_with_browser_simulation(url)

            if not html_content:
                logger.error(f"Falha ao obter HTML com BrowserSimulator para: {url}")
                return self._error_response("Falha ao carregar conteúdo da página com simulação de navegador.", url)

            # Criar uma HtmlResponse do Scrapy a partir do conteúdo HTML
            scrapy_response = HtmlResponse(url=url, body=html_content, encoding='utf-8')

            # Instanciar o spider Scrapy e chamar seu método de parse
            # Nota: O spider Scrapy é síncrono, mas estamos chamando seu método de parse aqui.
            # Se o parse do spider fizesse chamadas async, isso precisaria de mais adaptação.
            spider_instance = AmazonScrapySpider(url=url) # Passa a URL para o construtor do spider
            
            # O método parse do spider Scrapy geralmente é um gerador.
            # Precisamos iterar sobre ele ou chamar o método de parsing de produto diretamente.
            # No nosso caso, amazon_spider.py tem parse() que chama parse_product() que retorna o dict.
            # Então podemos chamar parse_product diretamente se ele for acessível ou adaptar.
            # Para simplificar, vamos assumir que o método parse() do spider retorna uma lista de itens (ou um gerador).
            # O método parse do AmazonScrapySpider retorna um dicionário diretamente.
            
            product = spider_instance.parse(scrapy_response) # Executa o parse do spider

            if not product or not isinstance(product, dict):
                logger.warning(f"Spider Scrapy não retornou um dicionário válido para: {url} após obter HTML com Selenium. Retornou: {type(product)}")
                return self._error_response("Não foi possível extrair dados do produto do HTML obtido.", url)

            # O spider já deve ter extraído o product_id se possível.
            # Se não, tentamos aqui como fallback.
            # O product.get() agora deve funcionar, pois product é um dict.
            if not product.get("product_id"):
                product_id_extracted = self.extract_product_id(url)
                if product_id_extracted:
                    product["product_id"] = product_id_extracted

            # Garante que todos os campos necessários estão presentes
            product.setdefault("platform", "Amazon")
            product.setdefault("url_produto", url)
            # product.setdefault("url_afiliado", "") # Spider já define
            # product.setdefault("description", "") # Será preenchido pela IA
            product.setdefault("error", False)

            # --- Adicionar Enriquecimento com IA ---
            product_title = product.get("title")
            # Garante que os campos existam mesmo se a IA falhar ou o título não for encontrado
            product["description"] = self._ai_generator.fallback_description if self._ai_generator else ""
            product["categoryKey"] = None
            product["category"] = None
            product["subcategoryIndex"] = None
            product["subcategory"] = None

            if product_title and product_title != "Título não encontrado":
                logger.info(f"Tentando gerar conteúdo com IA para: {product_title}")

                try:
                    # Gerar Descrição
                    generated_description = await self.generate_product_description(product_title)
                    product["description"] = generated_description
                    logger.info(f"Descrição da IA: {generated_description}")

                    # Gerar Categoria
                    category_key = await self.generate_product_category(product_title)
                    product["categoryKey"] = category_key
                    product["category"] = self.get_category_name(category_key) if category_key else None
                    logger.info(f"Chave da Categoria da IA: {category_key}, Nome: {product['category']}")

                    # Gerar Subcategoria (apenas se a categoria foi encontrada)
                    if category_key:
                        subcategory_idx = await self.generate_product_subcategory(product_title, category_key)
                        product["subcategoryIndex"] = subcategory_idx
                        product["subcategory"] = self.get_subcategory_name(category_key, subcategory_idx) if subcategory_idx else None
                        logger.info(f"Índice da Subcategoria da IA: {subcategory_idx}, Nome: {product['subcategory']}")
                    else:
                        product["subcategoryIndex"] = None
                        product["subcategory"] = None
                        logger.info("Subcategoria não gerada pois a categoria não foi encontrada.")
                except Exception as ai_exc:
                    logger.error(f"Erro durante a geração de conteúdo com IA para '{product_title}': {ai_exc}", exc_info=True)
                    # Mantém os valores de fallback/None definidos anteriormente
            else:
                logger.warning("Título do produto não disponível ou inválido para geração de conteúdo com IA.")
                # Fallbacks já estão definidos

            logger.info(f"Raspagem com Scrapy e IA (se aplicável) concluída com sucesso para: {url}")
            return product

        except Exception as e:
            logger.error(f"Erro ao raspar com Scrapy: {e}")
            return self._error_response(f"Erro ao raspar com Scrapy: {str(e)}", url)
