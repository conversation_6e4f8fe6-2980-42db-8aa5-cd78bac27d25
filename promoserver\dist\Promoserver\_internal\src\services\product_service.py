import logging
from typing import Dict, Optional

from src.scrapers.mercado_livre_scraper import MercadoLivreScraper
from src.scrapers.amazon_scraper import AmazonScraper
from src.scrapers.magalu_scraper import MagaluScraper

log = logging.getLogger(__name__)


class ProductService:
    def __init__(self):
        self._scrapers: Dict[str, Optional[type]] = {
            "mercadolivre": MercadoLivreScraper,
            "amazon": AmazonScraper,
            "magalu": MagaluScraper,
        }
        self._instances = {}

    def _get_scraper_instance(self, domain: str):
        scraper_cls = None
        if "mercadolivre.com" in domain:
            scraper_cls = self._scrapers.get("mercadolivre")
        elif "amazon.com" in domain:
            scraper_cls = self._scrapers.get("amazon")
        elif "magazinevoce.com" in domain or "magazineluiza.com" in domain:
            scraper_cls = self._scrapers.get("magalu")

        if scraper_cls:
            if scraper_cls not in self._instances:
                self._instances[scraper_cls] = scraper_cls()
            return self._instances[scraper_cls]
        return None

    async def fetch_product_details(self, url: str) -> Dict:
        log.info(f"Determinando scraper pela url: {url}")
        try:
            from urllib.parse import urlparse

            domain = urlparse(url).netloc.lower()
        except Exception:
            domain = ""

        scraper_instance = self._get_scraper_instance(domain)

        if scraper_instance:
            log.info(f"Usando scraper: {type(scraper_instance).__name__}")
            return await scraper_instance.scrape(url)
        else:
            log.warning(
                f"Nenhum scraper adequado encontrado para o domínio: {domain} (URL: {url})"
            )
            return {
                "platform": "Unknown",
                "url_afiliado": url,
                "url_produto": url,
                "title": "Loja não suportada",
                "error": True,
                "description": "Não foi possível encontrar um scraper para esta URL.",
                "product_id": None,
                "price": None,
                "old_price": None,
                "image_url": None,
                "installments": None,
                "coupon_info": None,
                "category": None,
                "subcategory": None,
                "shipping": None,
            }
