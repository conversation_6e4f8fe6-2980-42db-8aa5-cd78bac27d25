import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:intl/intl.dart';

import '../controllers/register_product_controller.dart';
import '../controllers/tabs_controller.dart';
import 'widgets/product_info_section.dart';
import 'widgets/product_tabs_header.dart';
import 'widgets/publish_section.dart';
import 'widgets/url_input_section.dart';
import 'widgets/values_section.dart';

class RegisterProductTabsPage extends StatefulWidget {
  const RegisterProductTabsPage({super.key});

  @override
  State<RegisterProductTabsPage> createState() =>
      _RegisterProductTabsPageState();
}

class _RegisterProductTabsPageState
    extends State<RegisterProductTabsPage> {
  final tabsController = Modular.get<TabsController>();
  final controller = Modular.get<RegisterProductController>();
  final _formKey = GlobalKey<FormState>(
    debugLabel: 'registerProductForm',
  );
  late final TextInputFormatter _currencyInputFormatter;

  @override
  void initState() {
    super.initState();
    _currencyInputFormatter = CurrencyInputFormatter(
      controller.currencyFormatter,
    );
  }

  Future<void> _publish() async {
    if (tabsController.currentTab == null) return;
    if (!_formKey.currentState!.validate()) return;

    final success = await tabsController.publishProduct(
      currentTitle: tabsController.currentTab!.titleController.text,
      currentDescription:
          tabsController.currentTab!.descriptionController.text,
      currentPriceStr:
          tabsController.currentTab!.priceController.text,
      oldPriceStr: tabsController.currentTab!.oldPriceController.text,
      coupon: tabsController.currentTab!.couponController.text,
    );

    if (mounted) {
      if (success) {
        // Verificar se o produto foi enfileirado (mensagem especial)
        if (tabsController.currentTab?.publishError != null &&
            tabsController.currentTab!.publishError!.contains(
              "enfileirado",
            )) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Produto enfileirado para salvamento. Aguardando processamento.\nO produto será salvo em segundo plano.',
              ),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 8),
              action: SnackBarAction(
                label: 'Entendi',
                textColor: Colors.white,
                onPressed: () {},
              ),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Produto publicado com sucesso!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else if (tabsController.currentTab?.publishError != null) {
        // Mostrar mensagem de erro com mais destaque
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Erro ao publicar: ${tabsController.currentTab!.publishError}',
            ),
            backgroundColor: Theme.of(context).colorScheme.error,
            duration: Duration(seconds: 5),
            action: SnackBarAction(
              label: 'OK',
              textColor: Colors.white,
              onPressed: () {},
            ),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return AnimatedBuilder(
      animation: controller,
      builder: (context, _) {
        return Scaffold(
          appBar: AppBar(title: const Text('Cadastrar Produto')),
          body: Column(
            children: [
              // Cabeçalho de abas
              ProductTabsHeader(),

              // Conteúdo da aba atual
              if (tabsController.currentTab != null)
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16.0),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment:
                            CrossAxisAlignment.stretch,
                        children: [
                          // URL Input Section
                          UrlInputSection(controller: controller),
                          const SizedBox(height: 24),

                          // Product Info Section (if product is being fetched or already fetched)
                          if (tabsController
                                      .currentTab!
                                      .scrapedProduct !=
                                  null ||
                              tabsController.currentTab!.isFetching)
                            ProductInfoSection(theme: theme),

                          const SizedBox(height: 24),

                          // Values Section (if product is being fetched or already fetched)
                          if (tabsController
                                      .currentTab!
                                      .scrapedProduct !=
                                  null ||
                              tabsController.currentTab!.isFetching)
                            ValuesSection(
                              theme: theme,
                              currencyFormatter:
                                  _currencyInputFormatter,
                            ),

                          const SizedBox(height: 24),

                          // Publish Section (if product is being fetched or already fetched)
                          if (tabsController
                                      .currentTab!
                                      .scrapedProduct !=
                                  null ||
                              tabsController.currentTab!.isFetching)
                            PublishSection(
                              theme: theme,
                              onPublish: _publish,
                            ),

                          // Error message
                          if (tabsController
                                  .currentTab!
                                  .publishError !=
                              null)
                            Padding(
                              padding: const EdgeInsets.only(
                                top: 16.0,
                              ),
                              child: Text(
                                tabsController
                                    .currentTab!
                                    .publishError!,
                                style: TextStyle(
                                  color: theme.colorScheme.error,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }
}

// Mantenha a classe CurrencyInputFormatter aqui ou mova para um arquivo separado
class CurrencyInputFormatter extends TextInputFormatter {
  final NumberFormat format;
  CurrencyInputFormatter(this.format);

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    if (newValue.text.isEmpty) {
      return newValue.copyWith(text: '');
    }

    final digitsOnly = newValue.text.replaceAll(RegExp(r'[^\d]'), '');
    if (digitsOnly.isEmpty) {
      return const TextEditingValue(
        text: '',
        selection: TextSelection.collapsed(offset: 0),
      );
    }

    try {
      final number = double.parse(digitsOnly) / 100.0;
      final newString = format.format(number);

      return TextEditingValue(
        text: newString,
        selection: TextSelection.collapsed(offset: newString.length),
      );
    } catch (e) {
      return oldValue;
    }
  }
}
