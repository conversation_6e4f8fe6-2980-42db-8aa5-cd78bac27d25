import logging
import random
import time
import re
from typing import Optional

from bs4 import BeautifulSoup

try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.chrome.service import Service
    from webdriver_manager.chrome import ChromeDriverManager
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import TimeoutException, WebDriverException
    selenium_available = True
    logger = logging.getLogger(__name__)
    logger.info("Selenium e WebDriver Manager importados com sucesso.")
except ImportError as e:
    selenium_available = False
    logging.warning(
        f"Selenium ou WebDriver Manager não instalados ou erro na importação: {e}. "
        "BrowserSimulator não funcionará. Instale com: pip install selenium webdriver-manager"
    )

from .cookie_manager import CookieManager
from .header_manager import HeaderManager

class BrowserSimulator:
    """Simula um navegador usando Selenium para buscar conteúdo de páginas web complexas."""

    def __init__(self, cookie_manager: <PERSON><PERSON><PERSON><PERSON>, header_manager: HeaderManager):
        self.cookie_manager = cookie_manager
        self.header_manager = header_manager
        if not selenium_available:
            logger.error("BrowserSimulator não pode ser inicializado pois Selenium não está disponível.")
            # Você pode querer levantar uma exceção aqui ou ter um modo de falha mais robusto.

    def _setup_driver_options(self) -> Optional[Options]:
        if not selenium_available:
            return None
        
        options = Options()
        options.add_argument("--headless")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_argument("--disable-extensions")
        options.add_argument("--disable-gpu") # Necessário para headless em alguns sistemas
        options.add_argument("--window-size=1920,1080")
        
        user_agent = self.header_manager.get_random_user_agent()
        options.add_argument(f"user-agent={user_agent}")
        logger.debug(f"Usando User-Agent no Selenium: {user_agent}")

        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        # prefs = {"profile.managed_default_content_settings.images": 2} # Desabilitar imagens pode acelerar
        # options.add_experimental_option("prefs", prefs)

        return options

    def _apply_stealth_techniques(self, driver):
        if not selenium_available:
            return
        try:
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            # Outros scripts de stealth podem ser adicionados aqui se necessário, como os do scrapers_old
            logger.debug("Técnica anti-detecção básica (navigator.webdriver) aplicada.")
        except Exception as e:
            logger.warning(f"Erro ao aplicar técnicas de stealth: {e}")

    def get_page_html(self, url: str) -> Optional[str]:
        """
        Busca a URL usando Selenium, aplica técnicas anti-detecção e retorna o HTML da página.
        """
        if not selenium_available:
            logger.error("Selenium não está disponível. Não é possível buscar a página com BrowserSimulator.")
            return None

        options = self._setup_driver_options()
        if not options: # Caso selenium_available seja False, mas a checagem falhou antes
             return None

        driver = None
        try:
            logger.info(f"Iniciando WebDriver para URL: {url}")
            # Usar WebDriverManager para instalar/gerenciar o ChromeDriver
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=options)
            
            self._apply_stealth_techniques(driver)

            domain_match = re.search(r"https?://([^/:]+)", url)
            domain = domain_match.group(1) if domain_match else url

            # Carregar cookies ANTES de ir para a página principal do domínio
            # Para isso, pode ser necessário ir para uma página simples do domínio primeiro, se houver cookies
            # driver.get(f"https://{domain}/favicon.ico") # Exemplo, pode não ser ideal
            # time.sleep(0.5)

            cookies = self.cookie_manager.load_cookies(domain)
            if cookies:
                # Para adicionar cookies, geralmente é preciso estar no domínio.
                # Se a URL já é do domínio, ok. Senão, pode ser preciso navegar para o domínio base primeiro.
                # driver.get(f"https://{domain}/") # Navega para a raiz do domínio para setar cookies
                # time.sleep(1) # Espera carregar
                for cookie in cookies:
                    # Selenium espera 'expiry' como int (timestamp), não string de data
                    if 'expiry' in cookie and isinstance(cookie['expiry'], float):
                        cookie['expiry'] = int(cookie['expiry'])
                    # Remover sameSite=None se secure=False, pois causa erro
                    if 'sameSite' in cookie and cookie['sameSite'] == 'None' and not cookie.get('secure', False):
                        # Ou define secure = True, ou remove sameSite. Remover é mais seguro se não souber.
                        del cookie['sameSite']
                    try:
                        driver.add_cookie(cookie)
                    except Exception as e_cookie:
                        logger.debug(f"Não foi possível adicionar cookie: {cookie}. Erro: {e_cookie}")
                logger.info(f"Cookies carregados para {domain} e adicionados ao driver.")
            
            logger.info(f"Navegando para URL com Selenium: {url}")
            driver.get(url)

            # Espera simples ou por um elemento específico
            time.sleep(random.uniform(5, 8)) # Espera um tempo para renderização de JS

            # Exemplo de espera explícita (descomentar e ajustar se necessário):
            # WebDriverWait(driver, 15).until(
            #     EC.presence_of_element_located((By.CSS_SELECTOR, "body")) # Espera pelo body
            # )
            # logger.info("Elemento <body> encontrado. Página provavelmente carregada.")

            html_content = driver.page_source

            # Salvar cookies da sessão atual
            current_cookies = driver.get_cookies()
            if current_cookies:
                self.cookie_manager.save_cookies(domain, current_cookies)
            
            logger.info(f"HTML obtido com Selenium para {url} (Tamanho: {len(html_content)} bytes)")
            return html_content

        except TimeoutException:
            logger.error(f"Timeout ao buscar URL com Selenium: {url}")
            return None
        except WebDriverException as e:
            logger.error(f"Erro de WebDriver ao buscar URL {url}: {e.msg}") # e.msg para mensagem mais limpa
            return None
        except Exception as e:
            logger.exception(f"Erro inesperado no BrowserSimulator para {url}: {e}")
            return None
        finally:
            if driver:
                try:
                    driver.quit()
                    logger.info("WebDriver finalizado.")
                except Exception as e:
                    logger.error(f"Erro ao finalizar WebDriver: {e}")
