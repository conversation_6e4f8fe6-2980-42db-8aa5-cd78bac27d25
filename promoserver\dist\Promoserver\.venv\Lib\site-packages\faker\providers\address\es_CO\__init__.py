from collections import OrderedDict
from typing import List, Tuple

from ..es import Provider as AddressProvider


class Provider(AddressProvider):
    departments = {
        "05": "Antioquia",
        "08": "Atlántico",
        "11": "Bogotá, D.C.",
        "13": "<PERSON><PERSON><PERSON><PERSON>",
        "15": "Boyacá",
        "17": "<PERSON><PERSON>",
        "18": "Caquetá",
        "19": "Cauca",
        "20": "Cesar",
        "23": "Córdoba",
        "25": "Cundinamarca",
        "27": "Chocó",
        "41": "<PERSON><PERSON>",
        "44": "La Guajira",
        "47": "<PERSON>",
        "50": "Met<PERSON>",
        "52": "<PERSON>ri<PERSON>",
        "54": "Norte de Santander",
        "63": "<PERSON>uindío",
        "66": "R<PERSON>ral<PERSON>",
        "68": "Santander",
        "70": "Sucre",
        "73": "Tolima",
        "76": "Valle del Cauca",
        "81": "Arauca",
        "85": "Casanare",
        "86": "<PERSON><PERSON><PERSON>",
        "88": "Archipiélago de San Andrés, Providencia y Santa Catalina",
        "91": "Amazonas",
        "94": "<PERSON>uainía",
        "95": "Guaviare",
        "97": "Vaupés",
        "99": "Vichada",
    }

    municipalities: List[Tuple[str, str]] = [
        ("05001", "Medellín"),
        ("05002", "Abejorral"),
        ("05004", "Abriaquí"),
        ("05021", "Alejandría"),
        ("05030", "Amagá"),
        ("05031", "Amalfi"),
        ("05034", "Andes"),
        ("05036", "Angelópolis"),
        ("05038", "Angostura"),
        ("05040", "Anorí"),
        ("05042", "Santa Fé de Antioquia"),
        ("05044", "Anzá"),
        ("05045", "Apartadó"),
        ("05051", "Arboletes"),
        ("05055", "Argelia"),
        ("05059", "Armenia"),
        ("05079", "Barbosa"),
        ("05086", "Belmira"),
        ("05088", "Bello"),
        ("05091", "Betania"),
        ("05093", "Betulia"),
        ("05101", "Ciudad Bolívar"),
        ("05107", "Briceño"),
        ("05113", "Buriticá"),
        ("05120", "Cáceres"),
        ("05125", "Caicedo"),
        ("05129", "Caldas"),
        ("05134", "Campamento"),
        ("05138", "Cañasgordas"),
        ("05142", "Caracolí"),
        ("05145", "Caramanta"),
        ("05147", "Carepa"),
        ("05148", "El Carmen de Viboral"),
        ("05150", "Carolina"),
        ("05154", "Caucasia"),
        ("05172", "Chigorodó"),
        ("05190", "Cisneros"),
        ("05197", "Cocorná"),
        ("05206", "Concepción"),
        ("05209", "Concordia"),
        ("05212", "Copacabana"),
        ("05234", "Dabeiba"),
        ("05237", "Donmatías"),
        ("05240", "Ebéjico"),
        ("05250", "El Bagre"),
        ("05264", "Entrerríos"),
        ("05266", "Envigado"),
        ("05282", "Fredonia"),
        ("05284", "Frontino"),
        ("05306", "Giraldo"),
        ("05308", "Girardota"),
        ("05310", "Gómez Plata"),
        ("05313", "Granada"),
        ("05315", "Guadalupe"),
        ("05318", "Guarne"),
        ("05321", "Guatapé"),
        ("05347", "Heliconia"),
        ("05353", "Hispania"),
        ("05360", "Itagüí"),
        ("05361", "Ituango"),
        ("05364", "Jardín"),
        ("05368", "Jericó"),
        ("05376", "La Ceja"),
        ("05380", "La Estrella"),
        ("05390", "La Pintada"),
        ("05400", "La Unión"),
        ("05411", "Liborina"),
        ("05425", "Maceo"),
        ("05440", "Marinilla"),
        ("05467", "Montebello"),
        ("05475", "Murindó"),
        ("05480", "Mutatá"),
        ("05483", "Nariño"),
        ("05490", "Necoclí"),
        ("05495", "Nechí"),
        ("05501", "Olaya"),
        ("05541", "Peñol"),
        ("05543", "Peque"),
        ("05576", "Pueblorrico"),
        ("05579", "Puerto Berrío"),
        ("05585", "Puerto Nare"),
        ("05591", "Puerto Triunfo"),
        ("05604", "Remedios"),
        ("05607", "Retiro"),
        ("05615", "Rionegro"),
        ("05628", "Sabanalarga"),
        ("05631", "Sabaneta"),
        ("05642", "Salgar"),
        ("05647", "San Andrés de Cuerquía"),
        ("05649", "San Carlos"),
        ("05652", "San Francisco"),
        ("05656", "San Jerónimo"),
        ("05658", "San José de la Montaña"),
        ("05659", "San Juan de Urabá"),
        ("05660", "San Luis"),
        ("05664", "San Pedro de los Milagros"),
        ("05665", "San Pedro de Urabá"),
        ("05667", "San Rafael"),
        ("05670", "San Roque"),
        ("05674", "San Vicente Ferrer"),
        ("05679", "Santa Bárbara"),
        ("05686", "Santa Rosa de Osos"),
        ("05690", "Santo Domingo"),
        ("05697", "El Santuario"),
        ("05736", "Segovia"),
        ("05756", "Sonsón"),
        ("05761", "Sopetrán"),
        ("05789", "Támesis"),
        ("05790", "Tarazá"),
        ("05792", "Tarso"),
        ("05809", "Titiribí"),
        ("05819", "Toledo"),
        ("05837", "Turbo"),
        ("05842", "Uramita"),
        ("05847", "Urrao"),
        ("05854", "Valdivia"),
        ("05856", "Valparaíso"),
        ("05858", "Vegachí"),
        ("05861", "Venecia"),
        ("05873", "Vigía del Fuerte"),
        ("05885", "Yalí"),
        ("05887", "Yarumal"),
        ("05890", "Yolombó"),
        ("05893", "Yondó"),
        ("05895", "Zaragoza"),
        ("08001", "Barranquilla"),
        ("08078", "Baranoa"),
        ("08137", "Campo de la Cruz"),
        ("08141", "Candelaria"),
        ("08296", "Galapa"),
        ("08372", "Juan de Acosta"),
        ("08421", "Luruaco"),
        ("08433", "Malambo"),
        ("08436", "Manatí"),
        ("08520", "Palmar de Varela"),
        ("08549", "Piojó"),
        ("08558", "Polonuevo"),
        ("08560", "Ponedera"),
        ("08573", "Puerto Colombia"),
        ("08606", "Repelón"),
        ("08634", "Sabanagrande"),
        ("08638", "Sabanalarga"),
        ("08675", "Santa Lucía"),
        ("08685", "Santo Tomás"),
        ("08758", "Soledad"),
        ("08770", "Suan"),
        ("08832", "Tubará"),
        ("08849", "Usiacurí"),
        ("11001", "Bogotá, D.C."),
        ("13001", "Cartagena de Indias"),
        ("13006", "Achí"),
        ("13030", "Altos del Rosario"),
        ("13042", "Arenal"),
        ("13052", "Arjona"),
        ("13062", "Arroyohondo"),
        ("13074", "Barranco de Loba"),
        ("13140", "Calamar"),
        ("13160", "Cantagallo"),
        ("13188", "Cicuco"),
        ("13212", "Córdoba"),
        ("13222", "Clemencia"),
        ("13244", "El Carmen de Bolívar"),
        ("13248", "El Guamo"),
        ("13268", "El Peñón"),
        ("13300", "Hatillo de Loba"),
        ("13430", "Magangué"),
        ("13433", "Mahates"),
        ("13440", "Margarita"),
        ("13442", "María la Baja"),
        ("13458", "Montecristo"),
        ("13468", "Santa Cruz de Mompox"),
        ("13473", "Morales"),
        ("13490", "Norosí"),
        ("13549", "Pinillos"),
        ("13580", "Regidor"),
        ("13600", "Río Viejo"),
        ("13620", "San Cristóbal"),
        ("13647", "San Estanislao"),
        ("13650", "San Fernando"),
        ("13654", "San Jacinto"),
        ("13655", "San Jacinto del Cauca"),
        ("13657", "San Juan Nepomuceno"),
        ("13667", "San Martín de Loba"),
        ("13670", "San Pablo"),
        ("13673", "Santa Catalina"),
        ("13683", "Santa Rosa"),
        ("13688", "Santa Rosa del Sur"),
        ("13744", "Simití"),
        ("13760", "Soplaviento"),
        ("13780", "Talaigua Nuevo"),
        ("13810", "Tiquisio"),
        ("13836", "Turbaco"),
        ("13838", "Turbaná"),
        ("13873", "Villanueva"),
        ("13894", "Zambrano"),
        ("15001", "Tunja"),
        ("15022", "Almeida"),
        ("15047", "Aquitania"),
        ("15051", "Arcabuco"),
        ("15087", "Belén"),
        ("15090", "Berbeo"),
        ("15092", "Betéitiva"),
        ("15097", "Boavita"),
        ("15104", "Boyacá"),
        ("15106", "Briceño"),
        ("15109", "Buenavista"),
        ("15114", "Busbanzá"),
        ("15131", "Caldas"),
        ("15135", "Campohermoso"),
        ("15162", "Cerinza"),
        ("15172", "Chinavita"),
        ("15176", "Chiquinquirá"),
        ("15180", "Chiscas"),
        ("15183", "Chita"),
        ("15185", "Chitaraque"),
        ("15187", "Chivatá"),
        ("15189", "Ciénega"),
        ("15204", "Cómbita"),
        ("15212", "Coper"),
        ("15215", "Corrales"),
        ("15218", "Covarachía"),
        ("15223", "Cubará"),
        ("15224", "Cucaita"),
        ("15226", "Cuítiva"),
        ("15232", "Chíquiza"),
        ("15236", "Chivor"),
        ("15238", "Duitama"),
        ("15244", "El Cocuy"),
        ("15248", "El Espino"),
        ("15272", "Firavitoba"),
        ("15276", "Floresta"),
        ("15293", "Gachantivá"),
        ("15296", "Gámeza"),
        ("15299", "Garagoa"),
        ("15317", "Guacamayas"),
        ("15322", "Guateque"),
        ("15325", "Guayatá"),
        ("15332", "Güicán de la Sierra"),
        ("15362", "Iza"),
        ("15367", "Jenesano"),
        ("15368", "Jericó"),
        ("15377", "Labranzagrande"),
        ("15380", "La Capilla"),
        ("15401", "La Victoria"),
        ("15403", "La Uvita"),
        ("15407", "Villa de Leyva"),
        ("15425", "Macanal"),
        ("15442", "Maripí"),
        ("15455", "Miraflores"),
        ("15464", "Mongua"),
        ("15466", "Monguí"),
        ("15469", "Moniquirá"),
        ("15476", "Motavita"),
        ("15480", "Muzo"),
        ("15491", "Nobsa"),
        ("15494", "Nuevo Colón"),
        ("15500", "Oicatá"),
        ("15507", "Otanche"),
        ("15511", "Pachavita"),
        ("15514", "Páez"),
        ("15516", "Paipa"),
        ("15518", "Pajarito"),
        ("15522", "Panqueba"),
        ("15531", "Pauna"),
        ("15533", "Paya"),
        ("15537", "Paz de Río"),
        ("15542", "Pesca"),
        ("15550", "Pisba"),
        ("15572", "Puerto Boyacá"),
        ("15580", "Quípama"),
        ("15599", "Ramiriquí"),
        ("15600", "Ráquira"),
        ("15621", "Rondón"),
        ("15632", "Saboyá"),
        ("15638", "Sáchica"),
        ("15646", "Samacá"),
        ("15660", "San Eduardo"),
        ("15664", "San José de Pare"),
        ("15667", "San Luis de Gaceno"),
        ("15673", "San Mateo"),
        ("15676", "San Miguel de Sema"),
        ("15681", "San Pablo de Borbur"),
        ("15686", "Santana"),
        ("15690", "Santa María"),
        ("15693", "Santa Rosa de Viterbo"),
        ("15696", "Santa Sofía"),
        ("15720", "Sativanorte"),
        ("15723", "Sativasur"),
        ("15740", "Siachoque"),
        ("15753", "Soatá"),
        ("15755", "Socotá"),
        ("15757", "Socha"),
        ("15759", "Sogamoso"),
        ("15761", "Somondoco"),
        ("15762", "Sora"),
        ("15763", "Sotaquirá"),
        ("15764", "Soracá"),
        ("15774", "Susacón"),
        ("15776", "Sutamarchán"),
        ("15778", "Sutatenza"),
        ("15790", "Tasco"),
        ("15798", "Tenza"),
        ("15804", "Tibaná"),
        ("15806", "Tibasosa"),
        ("15808", "Tinjacá"),
        ("15810", "Tipacoque"),
        ("15814", "Toca"),
        ("15816", "Togüí"),
        ("15820", "Tópaga"),
        ("15822", "Tota"),
        ("15832", "Tununguá"),
        ("15835", "Turmequé"),
        ("15837", "Tuta"),
        ("15839", "Tutazá"),
        ("15842", "Úmbita"),
        ("15861", "Ventaquemada"),
        ("15879", "Viracachá"),
        ("15897", "Zetaquira"),
        ("17001", "Manizales"),
        ("17013", "Aguadas"),
        ("17042", "Anserma"),
        ("17050", "Aranzazu"),
        ("17088", "Belalcázar"),
        ("17174", "Chinchiná"),
        ("17272", "Filadelfia"),
        ("17380", "La Dorada"),
        ("17388", "La Merced"),
        ("17433", "Manzanares"),
        ("17442", "Marmato"),
        ("17444", "Marquetalia"),
        ("17446", "Marulanda"),
        ("17486", "Neira"),
        ("17495", "Norcasia"),
        ("17513", "Pácora"),
        ("17524", "Palestina"),
        ("17541", "Pensilvania"),
        ("17614", "Riosucio"),
        ("17616", "Risaralda"),
        ("17653", "Salamina"),
        ("17662", "Samaná"),
        ("17665", "San José"),
        ("17777", "Supía"),
        ("17867", "Victoria"),
        ("17873", "Villamaría"),
        ("17877", "Viterbo"),
        ("18001", "Florencia"),
        ("18029", "Albania"),
        ("18094", "Belén de los Andaquíes"),
        ("18150", "Cartagena del Chairá"),
        ("18205", "Curillo"),
        ("18247", "El Doncello"),
        ("18256", "El Paujíl"),
        ("18410", "La Montañita"),
        ("18460", "Milán"),
        ("18479", "Morelia"),
        ("18592", "Puerto Rico"),
        ("18610", "San José del Fragua"),
        ("18753", "San Vicente del Caguán"),
        ("18756", "Solano"),
        ("18785", "Solita"),
        ("18860", "Valparaíso"),
        ("19001", "Popayán"),
        ("19022", "Almaguer"),
        ("19050", "Argelia"),
        ("19075", "Balboa"),
        ("19100", "Bolívar"),
        ("19110", "Buenos Aires"),
        ("19130", "Cajibío"),
        ("19137", "Caldono"),
        ("19142", "Caloto"),
        ("19212", "Corinto"),
        ("19256", "El Tambo"),
        ("19290", "Florencia"),
        ("19300", "Guachené"),
        ("19318", "Guapi"),
        ("19355", "Inzá"),
        ("19364", "Jambaló"),
        ("19392", "La Sierra"),
        ("19397", "La Vega"),
        ("19418", "López de Micay"),
        ("19450", "Mercaderes"),
        ("19455", "Miranda"),
        ("19473", "Morales"),
        ("19513", "Padilla"),
        ("19517", "Páez"),
        ("19532", "Patía"),
        ("19533", "Piamonte"),
        ("19548", "Piendamó - Tunía"),
        ("19573", "Puerto Tejada"),
        ("19585", "Puracé"),
        ("19622", "Rosas"),
        ("19693", "San Sebastián"),
        ("19698", "Santander de Quilichao"),
        ("19701", "Santa Rosa"),
        ("19743", "Silvia"),
        ("19760", "Sotará Paispamba"),
        ("19780", "Suárez"),
        ("19785", "Sucre"),
        ("19807", "Timbío"),
        ("19809", "Timbiquí"),
        ("19821", "Toribío"),
        ("19824", "Totoró"),
        ("19845", "Villa Rica"),
        ("20001", "Valledupar"),
        ("20011", "Aguachica"),
        ("20013", "Agustín Codazzi"),
        ("20032", "Astrea"),
        ("20045", "Becerril"),
        ("20060", "Bosconia"),
        ("20175", "Chimichagua"),
        ("20178", "Chiriguaná"),
        ("20228", "Curumaní"),
        ("20238", "El Copey"),
        ("20250", "El Paso"),
        ("20295", "Gamarra"),
        ("20310", "González"),
        ("20383", "La Gloria"),
        ("20400", "La Jagua de Ibirico"),
        ("20443", "Manaure Balcón del Cesar"),
        ("20517", "Pailitas"),
        ("20550", "Pelaya"),
        ("20570", "Pueblo Bello"),
        ("20614", "Río de Oro"),
        ("20621", "La Paz"),
        ("20710", "San Alberto"),
        ("20750", "San Diego"),
        ("20770", "San Martín"),
        ("20787", "Tamalameque"),
        ("23001", "Montería"),
        ("23068", "Ayapel"),
        ("23079", "Buenavista"),
        ("23090", "Canalete"),
        ("23162", "Cereté"),
        ("23168", "Chimá"),
        ("23182", "Chinú"),
        ("23189", "Ciénaga de Oro"),
        ("23300", "Cotorra"),
        ("23350", "La Apartada"),
        ("23417", "Lorica"),
        ("23419", "Los Córdobas"),
        ("23464", "Momil"),
        ("23466", "Montelíbano"),
        ("23500", "Moñitos"),
        ("23555", "Planeta Rica"),
        ("23570", "Pueblo Nuevo"),
        ("23574", "Puerto Escondido"),
        ("23580", "Puerto Libertador"),
        ("23586", "Purísima de la Concepción"),
        ("23660", "Sahagún"),
        ("23670", "San Andrés de Sotavento"),
        ("23672", "San Antero"),
        ("23675", "San Bernardo del Viento"),
        ("23678", "San Carlos"),
        ("23682", "San José de Uré"),
        ("23686", "San Pelayo"),
        ("23807", "Tierralta"),
        ("23815", "Tuchín"),
        ("23855", "Valencia"),
        ("25001", "Agua de Dios"),
        ("25019", "Albán"),
        ("25035", "Anapoima"),
        ("25040", "Anolaima"),
        ("25053", "Arbeláez"),
        ("25086", "Beltrán"),
        ("25095", "Bituima"),
        ("25099", "Bojacá"),
        ("25120", "Cabrera"),
        ("25123", "Cachipay"),
        ("25126", "Cajicá"),
        ("25148", "Caparrapí"),
        ("25151", "Cáqueza"),
        ("25154", "Carmen de Carupa"),
        ("25168", "Chaguaní"),
        ("25175", "Chía"),
        ("25178", "Chipaque"),
        ("25181", "Choachí"),
        ("25183", "Chocontá"),
        ("25200", "Cogua"),
        ("25214", "Cota"),
        ("25224", "Cucunubá"),
        ("25245", "El Colegio"),
        ("25258", "El Peñón"),
        ("25260", "El Rosal"),
        ("25269", "Facatativá"),
        ("25279", "Fómeque"),
        ("25281", "Fosca"),
        ("25286", "Funza"),
        ("25288", "Fúquene"),
        ("25290", "Fusagasugá"),
        ("25293", "Gachalá"),
        ("25295", "Gachancipá"),
        ("25297", "Gachetá"),
        ("25299", "Gama"),
        ("25307", "Girardot"),
        ("25312", "Granada"),
        ("25317", "Guachetá"),
        ("25320", "Guaduas"),
        ("25322", "Guasca"),
        ("25324", "Guataquí"),
        ("25326", "Guatavita"),
        ("25328", "Guayabal de Síquima"),
        ("25335", "Guayabetal"),
        ("25339", "Gutiérrez"),
        ("25368", "Jerusalén"),
        ("25372", "Junín"),
        ("25377", "La Calera"),
        ("25386", "La Mesa"),
        ("25394", "La Palma"),
        ("25398", "La Peña"),
        ("25402", "La Vega"),
        ("25407", "Lenguazaque"),
        ("25426", "Machetá"),
        ("25430", "Madrid"),
        ("25436", "Manta"),
        ("25438", "Medina"),
        ("25473", "Mosquera"),
        ("25483", "Nariño"),
        ("25486", "Nemocón"),
        ("25488", "Nilo"),
        ("25489", "Nimaima"),
        ("25491", "Nocaima"),
        ("25506", "Venecia"),
        ("25513", "Pacho"),
        ("25518", "Paime"),
        ("25524", "Pandi"),
        ("25530", "Paratebueno"),
        ("25535", "Pasca"),
        ("25572", "Puerto Salgar"),
        ("25580", "Pulí"),
        ("25592", "Quebradanegra"),
        ("25594", "Quetame"),
        ("25596", "Quipile"),
        ("25599", "Apulo"),
        ("25612", "Ricaurte"),
        ("25645", "San Antonio del Tequendama"),
        ("25649", "San Bernardo"),
        ("25653", "San Cayetano"),
        ("25658", "San Francisco"),
        ("25662", "San Juan de Rioseco"),
        ("25718", "Sasaima"),
        ("25736", "Sesquilé"),
        ("25740", "Sibaté"),
        ("25743", "Silvania"),
        ("25745", "Simijaca"),
        ("25754", "Soacha"),
        ("25758", "Sopó"),
        ("25769", "Subachoque"),
        ("25772", "Suesca"),
        ("25777", "Supatá"),
        ("25779", "Susa"),
        ("25781", "Sutatausa"),
        ("25785", "Tabio"),
        ("25793", "Tausa"),
        ("25797", "Tena"),
        ("25799", "Tenjo"),
        ("25805", "Tibacuy"),
        ("25807", "Tibirita"),
        ("25815", "Tocaima"),
        ("25817", "Tocancipá"),
        ("25823", "Topaipí"),
        ("25839", "Ubalá"),
        ("25841", "Ubaque"),
        ("25843", "Villa de San Diego de Ubaté"),
        ("25845", "Une"),
        ("25851", "Útica"),
        ("25862", "Vergara"),
        ("25867", "Vianí"),
        ("25871", "Villagómez"),
        ("25873", "Villapinzón"),
        ("25875", "Villeta"),
        ("25878", "Viotá"),
        ("25885", "Yacopí"),
        ("25898", "Zipacón"),
        ("25899", "Zipaquirá"),
        ("27001", "Quibdó"),
        ("27006", "Acandí"),
        ("27025", "Alto Baudó"),
        ("27050", "Atrato"),
        ("27073", "Bagadó"),
        ("27075", "Bahía Solano"),
        ("27077", "Bajo Baudó"),
        ("27099", "Bojayá"),
        ("27135", "El Cantón del San Pablo"),
        ("27150", "Carmen del Darién"),
        ("27160", "Cértegui"),
        ("27205", "Condoto"),
        ("27245", "El Carmen de Atrato"),
        ("27250", "El Litoral del San Juan"),
        ("27361", "Istmina"),
        ("27372", "Juradó"),
        ("27413", "Lloró"),
        ("27425", "Medio Atrato"),
        ("27430", "Medio Baudó"),
        ("27450", "Medio San Juan"),
        ("27491", "Nóvita"),
        ("27495", "Nuquí"),
        ("27580", "Río Iró"),
        ("27600", "Río Quito"),
        ("27615", "Riosucio"),
        ("27660", "San José del Palmar"),
        ("27745", "Sipí"),
        ("27787", "Tadó"),
        ("27800", "Unguía"),
        ("27810", "Unión Panamericana"),
        ("41001", "Neiva"),
        ("41006", "Acevedo"),
        ("41013", "Agrado"),
        ("41016", "Aipe"),
        ("41020", "Algeciras"),
        ("41026", "Altamira"),
        ("41078", "Baraya"),
        ("41132", "Campoalegre"),
        ("41206", "Colombia"),
        ("41244", "Elías"),
        ("41298", "Garzón"),
        ("41306", "Gigante"),
        ("41319", "Guadalupe"),
        ("41349", "Hobo"),
        ("41357", "Íquira"),
        ("41359", "Isnos"),
        ("41378", "La Argentina"),
        ("41396", "La Plata"),
        ("41483", "Nátaga"),
        ("41503", "Oporapa"),
        ("41518", "Paicol"),
        ("41524", "Palermo"),
        ("41530", "Palestina"),
        ("41548", "Pital"),
        ("41551", "Pitalito"),
        ("41615", "Rivera"),
        ("41660", "Saladoblanco"),
        ("41668", "San Agustín"),
        ("41676", "Santa María"),
        ("41770", "Suaza"),
        ("41791", "Tarqui"),
        ("41797", "Tesalia"),
        ("41799", "Tello"),
        ("41801", "Teruel"),
        ("41807", "Timaná"),
        ("41872", "Villavieja"),
        ("41885", "Yaguará"),
        ("44001", "Riohacha"),
        ("44035", "Albania"),
        ("44078", "Barrancas"),
        ("44090", "Dibulla"),
        ("44098", "Distracción"),
        ("44110", "El Molino"),
        ("44279", "Fonseca"),
        ("44378", "Hatonuevo"),
        ("44420", "La Jagua del Pilar"),
        ("44430", "Maicao"),
        ("44560", "Manaure"),
        ("44650", "San Juan del Cesar"),
        ("44847", "Uribia"),
        ("44855", "Urumita"),
        ("44874", "Villanueva"),
        ("47001", "Santa Marta"),
        ("47030", "Algarrobo"),
        ("47053", "Aracataca"),
        ("47058", "Ariguaní"),
        ("47161", "Cerro de San Antonio"),
        ("47170", "Chivolo"),
        ("47189", "Ciénaga"),
        ("47205", "Concordia"),
        ("47245", "El Banco"),
        ("47258", "El Piñón"),
        ("47268", "El Retén"),
        ("47288", "Fundación"),
        ("47318", "Guamal"),
        ("47460", "Nueva Granada"),
        ("47541", "Pedraza"),
        ("47545", "Pijiño del Carmen"),
        ("47551", "Pivijay"),
        ("47555", "Plato"),
        ("47570", "Puebloviejo"),
        ("47605", "Remolino"),
        ("47660", "Sabanas de San Ángel"),
        ("47675", "Salamina"),
        ("47692", "San Sebastián de Buenavista"),
        ("47703", "San Zenón"),
        ("47707", "Santa Ana"),
        ("47720", "Santa Bárbara de Pinto"),
        ("47745", "Sitionuevo"),
        ("47798", "Tenerife"),
        ("47960", "Zapayán"),
        ("47980", "Zona Bananera"),
        ("50001", "Villavicencio"),
        ("50006", "Acacías"),
        ("50110", "Barranca de Upía"),
        ("50124", "Cabuyaro"),
        ("50150", "Castilla la Nueva"),
        ("50223", "Cubarral"),
        ("50226", "Cumaral"),
        ("50245", "El Calvario"),
        ("50251", "El Castillo"),
        ("50270", "El Dorado"),
        ("50287", "Fuente de Oro"),
        ("50313", "Granada"),
        ("50318", "Guamal"),
        ("50325", "Mapiripán"),
        ("50330", "Mesetas"),
        ("50350", "La Macarena"),
        ("50370", "Uribe"),
        ("50400", "Lejanías"),
        ("50450", "Puerto Concordia"),
        ("50568", "Puerto Gaitán"),
        ("50573", "Puerto López"),
        ("50577", "Puerto Lleras"),
        ("50590", "Puerto Rico"),
        ("50606", "Restrepo"),
        ("50680", "San Carlos de Guaroa"),
        ("50683", "San Juan de Arama"),
        ("50686", "San Juanito"),
        ("50689", "San Martín"),
        ("50711", "Vistahermosa"),
        ("52001", "Pasto"),
        ("52019", "Albán"),
        ("52022", "Aldana"),
        ("52036", "Ancuya"),
        ("52051", "Arboleda"),
        ("52079", "Barbacoas"),
        ("52083", "Belén"),
        ("52110", "Buesaco"),
        ("52203", "Colón"),
        ("52207", "Consacá"),
        ("52210", "Contadero"),
        ("52215", "Córdoba"),
        ("52224", "Cuaspud Carlosama"),
        ("52227", "Cumbal"),
        ("52233", "Cumbitara"),
        ("52240", "Chachagüí"),
        ("52250", "El Charco"),
        ("52254", "El Peñol"),
        ("52256", "El Rosario"),
        ("52258", "El Tablón de Gómez"),
        ("52260", "El Tambo"),
        ("52287", "Funes"),
        ("52317", "Guachucal"),
        ("52320", "Guaitarilla"),
        ("52323", "Gualmatán"),
        ("52352", "Iles"),
        ("52354", "Imués"),
        ("52356", "Ipiales"),
        ("52378", "La Cruz"),
        ("52381", "La Florida"),
        ("52385", "La Llanada"),
        ("52390", "La Tola"),
        ("52399", "La Unión"),
        ("52405", "Leiva"),
        ("52411", "Linares"),
        ("52418", "Los Andes"),
        ("52427", "Magüí"),
        ("52435", "Mallama"),
        ("52473", "Mosquera"),
        ("52480", "Nariño"),
        ("52490", "Olaya Herrera"),
        ("52506", "Ospina"),
        ("52520", "Francisco Pizarro"),
        ("52540", "Policarpa"),
        ("52560", "Potosí"),
        ("52565", "Providencia"),
        ("52573", "Puerres"),
        ("52585", "Pupiales"),
        ("52612", "Ricaurte"),
        ("52621", "Roberto Payán"),
        ("52678", "Samaniego"),
        ("52683", "Sandoná"),
        ("52685", "San Bernardo"),
        ("52687", "San Lorenzo"),
        ("52693", "San Pablo"),
        ("52694", "San Pedro de Cartago"),
        ("52696", "Santa Bárbara"),
        ("52699", "Santacruz"),
        ("52720", "Sapuyes"),
        ("52786", "Taminango"),
        ("52788", "Tangua"),
        ("52835", "San Andrés de Tumaco"),
        ("52838", "Túquerres"),
        ("52885", "Yacuanquer"),
        ("54001", "San José de Cúcuta"),
        ("54003", "Ábrego"),
        ("54051", "Arboledas"),
        ("54099", "Bochalema"),
        ("54109", "Bucarasica"),
        ("54125", "Cácota"),
        ("54128", "Cáchira"),
        ("54172", "Chinácota"),
        ("54174", "Chitagá"),
        ("54206", "Convención"),
        ("54223", "Cucutilla"),
        ("54239", "Durania"),
        ("54245", "El Carmen"),
        ("54250", "El Tarra"),
        ("54261", "El Zulia"),
        ("54313", "Gramalote"),
        ("54344", "Hacarí"),
        ("54347", "Herrán"),
        ("54377", "Labateca"),
        ("54385", "La Esperanza"),
        ("54398", "La Playa"),
        ("54405", "Los Patios"),
        ("54418", "Lourdes"),
        ("54480", "Mutiscua"),
        ("54498", "Ocaña"),
        ("54518", "Pamplona"),
        ("54520", "Pamplonita"),
        ("54553", "Puerto Santander"),
        ("54599", "Ragonvalia"),
        ("54660", "Salazar"),
        ("54670", "San Calixto"),
        ("54673", "San Cayetano"),
        ("54680", "Santiago"),
        ("54720", "Sardinata"),
        ("54743", "Silos"),
        ("54800", "Teorama"),
        ("54810", "Tibú"),
        ("54820", "Toledo"),
        ("54871", "Villa Caro"),
        ("54874", "Villa del Rosario"),
        ("63001", "Armenia"),
        ("63111", "Buenavista"),
        ("63130", "Calarcá"),
        ("63190", "Circasia"),
        ("63212", "Córdoba"),
        ("63272", "Filandia"),
        ("63302", "Génova"),
        ("63401", "La Tebaida"),
        ("63470", "Montenegro"),
        ("63548", "Pijao"),
        ("63594", "Quimbaya"),
        ("63690", "Salento"),
        ("66001", "Pereira"),
        ("66045", "Apía"),
        ("66075", "Balboa"),
        ("66088", "Belén de Umbría"),
        ("66170", "Dosquebradas"),
        ("66318", "Guática"),
        ("66383", "La Celia"),
        ("66400", "La Virginia"),
        ("66440", "Marsella"),
        ("66456", "Mistrató"),
        ("66572", "Pueblo Rico"),
        ("66594", "Quinchía"),
        ("66682", "Santa Rosa de Cabal"),
        ("66687", "Santuario"),
        ("68001", "Bucaramanga"),
        ("68013", "Aguada"),
        ("68020", "Albania"),
        ("68051", "Aratoca"),
        ("68077", "Barbosa"),
        ("68079", "Barichara"),
        ("68081", "Barrancabermeja"),
        ("68092", "Betulia"),
        ("68101", "Bolívar"),
        ("68121", "Cabrera"),
        ("68132", "California"),
        ("68147", "Capitanejo"),
        ("68152", "Carcasí"),
        ("68160", "Cepitá"),
        ("68162", "Cerrito"),
        ("68167", "Charalá"),
        ("68169", "Charta"),
        ("68176", "Chima"),
        ("68179", "Chipatá"),
        ("68190", "Cimitarra"),
        ("68207", "Concepción"),
        ("68209", "Confines"),
        ("68211", "Contratación"),
        ("68217", "Coromoro"),
        ("68229", "Curití"),
        ("68235", "El Carmen de Chucurí"),
        ("68245", "El Guacamayo"),
        ("68250", "El Peñón"),
        ("68255", "El Playón"),
        ("68264", "Encino"),
        ("68266", "Enciso"),
        ("68271", "Florián"),
        ("68276", "Floridablanca"),
        ("68296", "Galán"),
        ("68298", "Gámbita"),
        ("68307", "Girón"),
        ("68318", "Guaca"),
        ("68320", "Guadalupe"),
        ("68322", "Guapotá"),
        ("68324", "Guavatá"),
        ("68327", "Güepsa"),
        ("68344", "Hato"),
        ("68368", "Jesús María"),
        ("68370", "Jordán"),
        ("68377", "La Belleza"),
        ("68385", "Landázuri"),
        ("68397", "La Paz"),
        ("68406", "Lebrija"),
        ("68418", "Los Santos"),
        ("68425", "Macaravita"),
        ("68432", "Málaga"),
        ("68444", "Matanza"),
        ("68464", "Mogotes"),
        ("68468", "Molagavita"),
        ("68498", "Ocamonte"),
        ("68500", "Oiba"),
        ("68502", "Onzaga"),
        ("68522", "Palmar"),
        ("68524", "Palmas del Socorro"),
        ("68533", "Páramo"),
        ("68547", "Piedecuesta"),
        ("68549", "Pinchote"),
        ("68572", "Puente Nacional"),
        ("68573", "Puerto Parra"),
        ("68575", "Puerto Wilches"),
        ("68615", "Rionegro"),
        ("68655", "Sabana de Torres"),
        ("68669", "San Andrés"),
        ("68673", "San Benito"),
        ("68679", "San Gil"),
        ("68682", "San Joaquín"),
        ("68684", "San José de Miranda"),
        ("68686", "San Miguel"),
        ("68689", "San Vicente de Chucurí"),
        ("68705", "Santa Bárbara"),
        ("68720", "Santa Helena del Opón"),
        ("68745", "Simacota"),
        ("68755", "Socorro"),
        ("68770", "Suaita"),
        ("68773", "Sucre"),
        ("68780", "Suratá"),
        ("68820", "Tona"),
        ("68855", "Valle de San José"),
        ("68861", "Vélez"),
        ("68867", "Vetas"),
        ("68872", "Villanueva"),
        ("68895", "Zapatoca"),
        ("70001", "Sincelejo"),
        ("70110", "Buenavista"),
        ("70124", "Caimito"),
        ("70204", "Colosó"),
        ("70215", "Corozal"),
        ("70221", "Coveñas"),
        ("70230", "Chalán"),
        ("70233", "El Roble"),
        ("70235", "Galeras"),
        ("70265", "Guaranda"),
        ("70400", "La Unión"),
        ("70418", "Los Palmitos"),
        ("70429", "Majagual"),
        ("70473", "Morroa"),
        ("70508", "Ovejas"),
        ("70523", "Palmito"),
        ("70670", "Sampués"),
        ("70678", "San Benito Abad"),
        ("70702", "San Juan de Betulia"),
        ("70708", "San Marcos"),
        ("70713", "San Onofre"),
        ("70717", "San Pedro"),
        ("70742", "San Luis de Sincé"),
        ("70771", "Sucre"),
        ("70820", "Santiago de Tolú"),
        ("70823", "San José de Toluviejo"),
        ("73001", "Ibagué"),
        ("73024", "Alpujarra"),
        ("73026", "Alvarado"),
        ("73030", "Ambalema"),
        ("73043", "Anzoátegui"),
        ("73055", "Armero"),
        ("73067", "Ataco"),
        ("73124", "Cajamarca"),
        ("73148", "Carmen de Apicalá"),
        ("73152", "Casabianca"),
        ("73168", "Chaparral"),
        ("73200", "Coello"),
        ("73217", "Coyaima"),
        ("73226", "Cunday"),
        ("73236", "Dolores"),
        ("73268", "Espinal"),
        ("73270", "Falan"),
        ("73275", "Flandes"),
        ("73283", "Fresno"),
        ("73319", "Guamo"),
        ("73347", "Herveo"),
        ("73349", "Honda"),
        ("73352", "Icononzo"),
        ("73408", "Lérida"),
        ("73411", "Líbano"),
        ("73443", "San Sebastián de Mariquita"),
        ("73449", "Melgar"),
        ("73461", "Murillo"),
        ("73483", "Natagaima"),
        ("73504", "Ortega"),
        ("73520", "Palocabildo"),
        ("73547", "Piedras"),
        ("73555", "Planadas"),
        ("73563", "Prado"),
        ("73585", "Purificación"),
        ("73616", "Rioblanco"),
        ("73622", "Roncesvalles"),
        ("73624", "Rovira"),
        ("73671", "Saldaña"),
        ("73675", "San Antonio"),
        ("73678", "San Luis"),
        ("73686", "Santa Isabel"),
        ("73770", "Suárez"),
        ("73854", "Valle de San Juan"),
        ("73861", "Venadillo"),
        ("73870", "Villahermosa"),
        ("73873", "Villarrica"),
        ("76001", "Cali"),
        ("76020", "Alcalá"),
        ("76036", "Andalucía"),
        ("76041", "Ansermanuevo"),
        ("76054", "Argelia"),
        ("76100", "Bolívar"),
        ("76109", "Buenaventura"),
        ("76111", "Guadalajara de Buga"),
        ("76113", "Bugalagrande"),
        ("76122", "Caicedonia"),
        ("76126", "Calima"),
        ("76130", "Candelaria"),
        ("76147", "Cartago"),
        ("76233", "Dagua"),
        ("76243", "El Águila"),
        ("76246", "El Cairo"),
        ("76248", "El Cerrito"),
        ("76250", "El Dovio"),
        ("76275", "Florida"),
        ("76306", "Ginebra"),
        ("76318", "Guacarí"),
        ("76364", "Jamundí"),
        ("76377", "La Cumbre"),
        ("76400", "La Unión"),
        ("76403", "La Victoria"),
        ("76497", "Obando"),
        ("76520", "Palmira"),
        ("76563", "Pradera"),
        ("76606", "Restrepo"),
        ("76616", "Riofrío"),
        ("76622", "Roldanillo"),
        ("76670", "San Pedro"),
        ("76736", "Sevilla"),
        ("76823", "Toro"),
        ("76828", "Trujillo"),
        ("76834", "Tuluá"),
        ("76845", "Ulloa"),
        ("76863", "Versalles"),
        ("76869", "Vijes"),
        ("76890", "Yotoco"),
        ("76892", "Yumbo"),
        ("76895", "Zarzal"),
        ("81001", "Arauca"),
        ("81065", "Arauquita"),
        ("81220", "Cravo Norte"),
        ("81300", "Fortul"),
        ("81591", "Puerto Rondón"),
        ("81736", "Saravena"),
        ("81794", "Tame"),
        ("85001", "Yopal"),
        ("85010", "Aguazul"),
        ("85015", "Chámeza"),
        ("85125", "Hato Corozal"),
        ("85136", "La Salina"),
        ("85139", "Maní"),
        ("85162", "Monterrey"),
        ("85225", "Nunchía"),
        ("85230", "Orocué"),
        ("85250", "Paz de Ariporo"),
        ("85263", "Pore"),
        ("85279", "Recetor"),
        ("85300", "Sabanalarga"),
        ("85315", "Sácama"),
        ("85325", "San Luis de Palenque"),
        ("85400", "Támara"),
        ("85410", "Tauramena"),
        ("85430", "Trinidad"),
        ("85440", "Villanueva"),
        ("86001", "Mocoa"),
        ("86219", "Colón"),
        ("86320", "Orito"),
        ("86568", "Puerto Asís"),
        ("86569", "Puerto Caicedo"),
        ("86571", "Puerto Guzmán"),
        ("86573", "Puerto Leguízamo"),
        ("86749", "Sibundoy"),
        ("86755", "San Francisco"),
        ("86757", "San Miguel"),
        ("86760", "Santiago"),
        ("86865", "Valle del Guamuez"),
        ("86885", "Villagarzón"),
        ("88001", "San Andrés"),
        ("88564", "Providencia"),
        ("91001", "Leticia"),
        ("91263", "El Encanto"),
        ("91405", "La Chorrera"),
        ("91407", "La Pedrera"),
        ("91430", "La Victoria"),
        ("91460", "Mirití - Paraná"),
        ("91530", "Puerto Alegría"),
        ("91536", "Puerto Arica"),
        ("91540", "Puerto Nariño"),
        ("91669", "Puerto Santander"),
        ("91798", "Tarapacá"),
        ("94001", "Inírida"),
        ("94343", "Barrancominas"),
        ("94883", "San Felipe"),
        ("94884", "Puerto Colombia"),
        ("94885", "La Guadalupe"),
        ("94886", "Cacahual"),
        ("94887", "Pana Pana"),
        ("94888", "Morichal"),
        ("95001", "San José del Guaviare"),
        ("95015", "Calamar"),
        ("95025", "El Retorno"),
        ("95200", "Miraflores"),
        ("97001", "Mitú"),
        ("97161", "Carurú"),
        ("97511", "Pacoa"),
        ("97666", "Taraira"),
        ("97777", "Papunahua"),
        ("97889", "Yavaraté"),
        ("99001", "Puerto Carreño"),
        ("99524", "La Primavera"),
        ("99624", "Santa Rosalía"),
        ("99773", "Cumaribo"),
    ]

    street_prefixes = OrderedDict(
        [
            ("Calle", 0.2),
            ("Cl.", 0.2),
            ("Carrera", 0.2),
            ("Cr.", 0.2),
            ("Diagonal", 0.05),
            ("Dg.", 0.05),
            ("Transversal", 0.05),
            ("Tr.", 0.05),
        ]
    )
    street_suffixes = ["Sur", "Este", "Bis", "Bis {{random_uppercase_letter}}"]
    street_name_formats = OrderedDict(
        [
            ("{{street_prefix}} %ª", 0.1),
            ("{{street_prefix}} %#", 0.3),
            ("{{street_prefix}} 1##", 0.1),
            ("{{street_prefix}} % {{street_suffix}}", 0.02),
            ("{{street_prefix}} %# {{street_suffix}}", 0.02),
            ("{{street_prefix}} 1## {{street_suffix}}", 0.02),
            ("{{street_prefix}} %{{random_uppercase_letter}}", 0.06),
            ("{{street_prefix}} %#{{random_uppercase_letter}}", 0.06),
            ("{{street_prefix}} 1##{{random_uppercase_letter}}", 0.06),
            ("{{street_prefix}} %{{random_uppercase_letter}} {{street_suffix}}", 0.02),
            ("{{street_prefix}} %#{{random_uppercase_letter}} {{street_suffix}}", 0.02),
            (
                "{{street_prefix}} 1##{{random_uppercase_letter}} {{street_suffix}}",
                0.02,
            ),
            ("Avenida {{last_name}}", 0.01),
            ("Avenida calle %ª", 0.01),
            ("Avenida calle %#", 0.01),
            ("Avenida calle 1##", 0.01),
            ("Avenida carrera %ª", 0.01),
            ("Avenida carrera %#", 0.01),
            ("Avenida carrera 1##", 0.01),
            ("Avenida {{name}}", 0.01),
            ("Avenida {{city}}", 0.01),
            ("Avenida {{department}}", 0.01),
            ("Av. {{last_name}}", 0.01),
            ("Av. calle %ª", 0.01),
            ("Av. calle %#", 0.01),
            ("Av. calle 1##", 0.01),
            ("Av. carrera %ª", 0.01),
            ("Av. carrera %#", 0.01),
            ("Av. carrera 1##", 0.01),
            ("Av. {{name}}", 0.01),
            ("Av. {{city}}", 0.01),
            ("Av. {{department}}", 0.01),
        ]
    )
    building_number_formats = OrderedDict(
        [
            ("%-%", 0.2),
            ("%-%#", 0.2),
            ("%#-%", 0.2),
            ("%#-%#", 0.2),
            ("%{{random_uppercase_letter}}-%", 0.05),
            ("%{{random_uppercase_letter}}-%#", 0.05),
            ("%#{{random_uppercase_letter}}-%", 0.05),
            ("%#{{random_uppercase_letter}}-%#", 0.05),
        ]
    )
    secondary_address_formats = [
        "Apartamento %!!",
        "Apto. %!!",
        "Casa %!",
        "Torre % apartamento %!!",
        "Etapa % apartamento %!!",
        "Local %!!",
        "Oficina %!!",
        "Bodega %!!",
    ]
    postcode_formats = ["{{department_code}}####"]

    def department_code(self) -> str:
        """
        :example: "11"
        """
        return self.random_element(self.departments.keys())

    def department(self) -> str:
        """
        :example: "Bogotá, D.C."
        """
        return self.random_element(list(self.departments.values()))

    administrative_unit = department

    def municipality_code(self) -> str:
        """
        :example: "11001"
        """
        return self.random_element(self.municipalities)[0]  # type: ignore

    def municipality(self) -> str:
        """
        :example: "Bogotá, D.C."
        """
        return self.random_element(self.municipalities)[1]  # type: ignore

    city = municipality

    def street_prefix(self) -> str:
        """
        :example: "Calle"
        """
        return self.random_element(self.street_prefixes)

    def street_suffix(self) -> str:
        """
        :example: "Sur"
        """
        return self.generator.parse(self.random_element(self.street_suffixes))

    def street_name(self) -> str:
        """
        :example: "Calle 1"
        """
        pattern: str = self.random_element(self.street_name_formats)
        return self.numerify(self.generator.parse(pattern))

    def building_number(self) -> str:
        """
        :example: "2-3"
        """
        return self.numerify(self.generator.parse(self.random_element(self.building_number_formats)))

    def secondary_address(self) -> str:
        """
        :example: "Apartamento 123"
        """
        return self.numerify(self.random_element(self.secondary_address_formats))

    def street_address(self) -> str:
        """
        :example: "Calle 1 # 2-3"
        """
        return self.street_name() + " # " + self.building_number() + self.random_element(("", " Sur", " Este"))

    def postcode(self) -> str:
        """
        :example: "11001"
        """
        return self.numerify(self.generator.parse(self.random_element(self.postcode_formats)))

    def address(self) -> str:
        """
        :example: "Calle 1 # 2-3\n11001\nBogotá D.C."
        """
        municipality: Tuple[str, str] = self.random_element(self.municipalities)
        municipality_code = municipality[0]
        department_code = municipality_code[0:2]
        is_department_capital = municipality_code[-3:] == "001"

        secondary_address: str = self.random_element(
            [
                "\n" + self.secondary_address(),
                "",
            ]
        )
        postcode = "\n" + department_code + self.numerify("####")
        municipality_name = "\n" + municipality[1]
        department_name = ", " + self.departments[department_code] if not is_department_capital else ""

        return self.street_address() + secondary_address + postcode + municipality_name + department_name
