import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../components/log_console.dart';
import '../../../models/category_model.dart';
import '../../../services/category_service.dart';
import '../../../services/product_service.dart';
import '../../../services/scraping_service.dart';
import '../models/product_tab.dart';

class TabsController extends ChangeNotifier {
  final ScrapingService _scrapingService;
  final ProductService _productService;
  final CategoryService _categoryService;

  // Lista de abas
  final List<ProductTab> _tabs = [];
  List<ProductTab> get tabs => _tabs;

  // Índice da aba atual
  int _currentTabIndex = -1;
  int get currentTabIndex => _currentTabIndex;

  // Aba atual
  ProductTab? get currentTab =>
      _currentTabIndex >= 0 && _currentTabIndex < _tabs.length
          ? _tabs[_currentTabIndex]
          : null;

  // Formatador de moeda
  final NumberFormat _currencyFormatter = NumberFormat.currency(
    locale: 'pt_BR',
    symbol: 'R\$',
  );
  NumberFormat get currencyFormatter => _currencyFormatter;

  // Lista de categorias
  List<CategoryModel> _categories = [];
  List<CategoryModel> get categories => _categories;

  // Estado de carregamento das categorias
  bool _isLoadingCategories = false;
  bool get isLoadingCategories => _isLoadingCategories;

  // Erro ao carregar categorias
  String? _categoriesError;
  String? get categoriesError => _categoriesError;

  // Lista de grupos de WhatsApp disponíveis
  List<String> get availableWhatsappGroups => ['Enviar mensagem 01'];

  final ScrollController _scrollController = ScrollController();
  ScrollController get scrollController => _scrollController;

  TabsController(
    this._scrapingService,
    this._productService,
    this._categoryService,
  ) {
    globalLogger.i("Controlador de Abas Inicializado");

    // Carregar categorias ao inicializar
    Future.delayed(const Duration(milliseconds: 100), () {
      _loadCategories();
    });

    // Criar a primeira aba
    addTab();
  }

  /// Rola para a aba especificada
  void scrollToTab(int index) {
    if (_scrollController.hasClients) {
      // Calcula a largura aproximada de uma aba para rolagem
      // Isso pode precisar de ajuste fino dependendo do layout real das abas
      final double tabWidth =
          150.0; // Largura média de uma aba, ajuste conforme necessário
      _scrollController.animateTo(
        index * tabWidth,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    for (final tab in _tabs) {
      tab.dispose();
    }
    super.dispose();
  }

  /// Adiciona uma nova aba
  void addTab() {
    // Encontrar o próximo número de aba disponível
    int nextTabNumber = 1;
    final existingTabNumbers =
        _tabs.map((tab) {
          final match = RegExp(
            r'Produto (\d+)',
          ).firstMatch(tab.title);
          return match != null ? int.parse(match.group(1)!) : 0;
        }).toList();

    // Ordenar os números existentes
    existingTabNumbers.sort();

    // Encontrar o próximo número disponível
    for (int i = 0; i < existingTabNumbers.length; i++) {
      if (existingTabNumbers[i] != i + 1) {
        nextTabNumber = i + 1;
        break;
      }
      nextTabNumber = i + 2; // Próximo após o último
    }

    final tabId = nextTabNumber.toString();
    final newTab = ProductTab(
      id: tabId,
      initialTitle: 'Produto $nextTabNumber',
    );
    _tabs.add(newTab);
    _currentTabIndex = _tabs.length - 1;
    notifyListeners();
    globalLogger.i("Nova aba adicionada: ${newTab.title}");
  }

  /// Remove uma aba pelo índice
  void removeTab(int index) {
    if (index < 0 || index >= _tabs.length) return;

    // Liberar recursos da aba
    final tab = _tabs[index];
    tab.dispose();

    // Remover a aba
    _tabs.removeAt(index);

    // Ajustar o índice atual
    if (_tabs.isEmpty) {
      // Se não houver mais abas, criar uma nova
      addTab();
    } else if (_currentTabIndex >= _tabs.length) {
      // Se o índice atual for maior que o número de abas, ajustar para a última aba
      _currentTabIndex = _tabs.length - 1;
    }

    notifyListeners();
    globalLogger.i("Aba removida: ${tab.title}");
  }

  /// Alterna para uma aba específica
  void switchToTab(int index) {
    if (index < 0 || index >= _tabs.length) return;
    _currentTabIndex = index;
    notifyListeners();
    globalLogger.i("Alternado para aba: ${_tabs[index].title}");
  }

  /// Carrega as categorias do servidor
  Future<void> _loadCategories() async {
    if (_isLoadingCategories) return;

    _isLoadingCategories = true;
    _categoriesError = null;
    notifyListeners();

    try {
      globalLogger.i("Carregando categorias...");
      _categories = await _categoryService.fetchCategories();
      globalLogger.i("${_categories.length} categorias carregadas");
    } catch (e) {
      _categoriesError = 'Ocorreu um erro inesperado';
      globalLogger.e('Erro ao carregar categorias: $e');
    } finally {
      _isLoadingCategories = false;
      notifyListeners();
    }
  }

  /// Atualiza as categorias
  Future<void> refreshCategories() async {
    await _loadCategories();
  }

  /// Define a URL para a aba atual
  void setUrl(String value) {
    if (currentTab == null) return;

    String url = value.trim();

    if (url.isNotEmpty &&
        !url.startsWith('http://') &&
        !url.startsWith('https://')) {
      url = 'https://$url';
    }

    currentTab!.url = url;

    if (currentTab!.scrapedProduct != null &&
        url != currentTab!.scrapedProduct!.affiliateUrl) {
      clearCurrentTab(notify: false);
    }

    notifyListeners();
  }

  /// Busca os detalhes do produto para a aba atual
  Future<void> fetchProductDetails() async {
    if (currentTab == null) return;

    if (currentTab!.url.isEmpty) {
      currentTab!.fetchError = "Por favor, insira uma URL válida";
      notifyListeners();
      return;
    }

    try {
      // Usar a URL do controller da aba atual
      final url = currentTab!.urlController.text;
      currentTab!.url = url;

      // Armazenar o índice da aba que iniciou a requisição
      final tabIndex = _currentTabIndex;
      final tabId = currentTab!.id;

      currentTab!.isFetching = true;
      currentTab!.fetchError = null;
      currentTab!.scrapedProduct = null;
      notifyListeners();

      globalLogger.i(
        "Iniciando busca de detalhes do produto para URL: $url na aba $tabId (índice $tabIndex)",
      );

      final result = await _scrapingService.scrapeProduct(url);

      globalLogger.d("Resposta do scraping para aba $tabId: $result");

      // Encontrar a aba correta pelo ID (mais seguro que usar índice, pois o índice pode mudar)
      final targetTab = _tabs.firstWhere(
        (tab) => tab.id == tabId,
        orElse: () => currentTab!,
      );

      // Verificar se a aba ainda existe e se a URL ainda é a mesma
      if (targetTab.url == url) {
        targetTab.scrapedProduct = result;

        if (result.error == true) {
          targetTab.fetchError =
              result.description ??
              'Falha ao buscar dados do produto.';
          globalLogger.w(
            "Falha na busca para $url na aba $tabId: ${targetTab.fetchError}",
          );
        } else if (result.title.isEmpty) {
          targetTab.fetchError = 'Dados do produto vieram vazios';
          globalLogger.w("Dados vazios para $url na aba $tabId");
        } else {
          globalLogger.i(
            "Busca realizada com sucesso para $url na aba $tabId: ${result.title}",
          );
          targetTab.updateFromScrapedData(
            result,
            _currencyFormatter,
            _categories,
          );
        }

        // Atualizar a interface mesmo se não estivermos mais na mesma aba
        targetTab.isFetching = false;
        notifyListeners();
      } else {
        globalLogger.w(
          "A URL da aba $tabId mudou durante o scraping. Ignorando resultado.",
        );
      }
    } catch (e, stackTrace) {
      // Adicionado stackTrace
      if (currentTab != null) {
        globalLogger.e(
          "Erro ao buscar detalhes do produto: $e\n$stackTrace",
        );
        currentTab!.fetchError = "Erro ao buscar detalhes: $e";
        currentTab!.isFetching = false;
        notifyListeners();
      }
    }
  }

  /// Limpa os dados da aba atual
  void clearCurrentTab({bool notify = true}) {
    if (currentTab == null) return;

    currentTab!.clear();

    if (notify) {
      notifyListeners();
    }
  }

  /// Define a categoria para a aba atual
  void selectCategory(CategoryModel? category) {
    if (currentTab == null) return;

    currentTab!.selectedCategory = category;
    currentTab!.currentSubcategories = category?.subcategories ?? [];

    if (currentTab!.currentSubcategories.isEmpty ||
        (currentTab!.selectedSubcategory != null &&
            !currentTab!.currentSubcategories.contains(
              currentTab!.selectedSubcategory,
            ))) {
      currentTab!.selectedSubcategory = null;
    }

    notifyListeners();
  }

  /// Define a subcategoria para a aba atual
  void selectSubcategory(String? subcategory) {
    if (currentTab == null) return;

    currentTab!.selectedSubcategory = subcategory;
    notifyListeners();
  }

  /// Define o melhor preço para a aba atual
  void setBestPrice(bool value) {
    if (currentTab == null) return;

    currentTab!.bestPrice = value;
    notifyListeners();
  }

  /// Define se o produto é recomendado para a aba atual
  void setRecommended(bool value) {
    if (currentTab == null) return;

    currentTab!.recommended = value;
    notifyListeners();
  }

  /// Define se o produto é um story para a aba atual
  void setIsStory(bool value) {
    if (currentTab == null) return;

    currentTab!.isStory = value;
    notifyListeners();
  }

  /// Define se o produto tem frete para a aba atual
  void setHasShipping(bool value) {
    if (currentTab == null) return;

    currentTab!.hasShipping = value;
    notifyListeners();
  }

  /// Define se o produto deve ser disparado no WhatsApp para a aba atual
  void setDispatchWhatsapp(bool value) {
    if (currentTab == null) return;

    currentTab!.dispatchWhatsapp = value;
    if (!value) {
      currentTab!.selectedWhatsappGroup = null;
    }
    notifyListeners();
  }

  /// Define o grupo de WhatsApp para a aba atual
  void setSelectedWhatsappGroup(String? value) {
    if (currentTab == null) return;

    currentTab!.selectedWhatsappGroup = value;
    notifyListeners();
  }

  /// Define a imagem local para a aba atual
  void setLocalImage(String imageUrl) {
    if (currentTab == null) return;

    currentTab!.localImageUrl = imageUrl;
    currentTab!.isUsingLocalImage = true;
    notifyListeners();
    globalLogger.i("Imagem local definida: $imageUrl");
  }

  /// Limpa a imagem local para a aba atual
  void clearLocalImage() {
    if (currentTab == null) return;

    currentTab!.localImageUrl = null;
    currentTab!.isUsingLocalImage = false;
    notifyListeners();
    globalLogger.i("Imagem local removida");
  }

  /// Publica o produto da aba atual
  Future<bool> publishProduct({
    required String currentTitle,
    required String currentDescription,
    required String currentPriceStr,
    required String oldPriceStr,
    required String coupon,
  }) async {
    if (currentTab == null ||
        currentTab!.scrapedProduct == null ||
        currentTab!.isPublishing)
      return false;

    if (currentTab!.dispatchWhatsapp &&
        currentTab!.selectedWhatsappGroup == null) {
      currentTab!.publishError =
          "Selecione um grupo do WhatsApp para disparo.";
      notifyListeners();
      return false;
    }

    final currentTabId = currentTab!.id;

    currentTab!.isPublishing = true;
    currentTab!.publishError = null;
    notifyListeners();
    globalLogger.i("Publicando produto: $currentTitle");

    try {
      // Converter preços de string para double
      double currentPrice;
      double oldPrice;

      try {
        currentPrice =
            _currencyFormatter.parse(currentPriceStr).toDouble();
      } catch (e) {
        // Método alternativo se o parse falhar
        final cleanStr = currentPriceStr
            .replaceAll('R\$', '')
            .trim()
            .replaceAll('.', '')
            .replaceAll(',', '.');
        currentPrice = double.parse(cleanStr);
      }

      try {
        oldPrice = _currencyFormatter.parse(oldPriceStr).toDouble();
      } catch (e) {
        // Método alternativo se o parse falhar
        final cleanStr = oldPriceStr
            .replaceAll('R\$', '')
            .trim()
            .replaceAll('.', '')
            .replaceAll(',', '.');
        oldPrice = double.parse(cleanStr);
      }

      final productData = {
        "plataforma": currentTab!.scrapedProduct!.platform,
        "url_afiliado": currentTab!.url,
        "url_produto": currentTab!.scrapedProduct!.productUrl,
        "url_imagem":
            currentTab!.isUsingLocalImage
                ? currentTab!.localImageUrl
                : currentTab!.scrapedProduct!.imageUrl ?? '',
        "titulo": currentTitle,
        "categoria": currentTab!.selectedCategory?.name,
        "subcategoria": currentTab!.selectedSubcategory,
        "descricao": currentDescription,
        "preco_atual": currentPrice,
        "preco_antigo": oldPrice,
        "cupom": coupon.isNotEmpty ? coupon : null,
        "menor_preco": currentTab!.bestPrice,
        "indicamos": currentTab!.recommended,
        "frete": currentTab!.hasShipping,
        "grupo_whatsapp": currentTab!.selectedWhatsappGroup,
        "disparar_whatsapp": currentTab!.dispatchWhatsapp,
        "ativo": true,
        "preco_alternativo": 0.0,
        "isStory": currentTab!.isStory,
        "invalidProduct": false,
        "is_local_image": currentTab!.isUsingLocalImage,
      };

      globalLogger.d("Dados para salvar: $productData");

      final savedProduct = await _productService.saveProduct(
        productData,
      );

      // Encontrar a aba correta pelo ID (mais seguro que usar índice, pois o índice pode mudar)
      final targetTabIndex = _tabs.indexWhere(
        (tab) => tab.id == currentTabId,
      );
      final targetTab =
          targetTabIndex >= 0 ? _tabs[targetTabIndex] : null;

      if (savedProduct != null) {
        globalLogger.i(
          "Produto salvo com sucesso (ID: ${savedProduct.id}).",
        );

        if (targetTab != null && targetTab.dispatchWhatsapp) {
          globalLogger.i(
            "Disparo do WhatsApp seria acionado aqui para o grupo: ${targetTab.selectedWhatsappGroup}",
          );
        }

        // Verificar se o ID do produto é temporário (caso de enfileiramento)
        if (savedProduct.id.isEmpty ||
            savedProduct.id.startsWith('temp_')) {
          globalLogger.i(
            "Produto enfileirado para salvamento. Aguardando processamento. ID: ${savedProduct.id}",
          );

          // Atualizar a mensagem de erro para informar que o produto foi enfileirado
          if (targetTab != null) {
            targetTab.publishError =
                "Produto enfileirado para salvamento. Aguardando processamento.";
            targetTab.isPublishing =
                false; // Desativar o indicador de carregamento

            // Mudar a cor do indicador para amarelo para indicar que está em processamento
            targetTab.statusColor = Colors.orange;
            targetTab.statusMessage =
                "Produto enfileirado para salvamento";

            notifyListeners();
          }

          // Remover a aba após um breve atraso para dar tempo ao usuário de ver a mensagem
          globalLogger.i(
            "Agendando remoção da aba após 3 segundos: ${targetTab?.title}",
          );

          // Usar um Future.delayed com um callback que captura as variáveis atuais
          final String tabId =
              currentTabId; // Capturar o ID da aba atual
          final int currentIndex =
              targetTabIndex; // Capturar o índice atual

          Future.delayed(Duration(seconds: 3), () {
            globalLogger.i(
              "Executando remoção agendada da aba: $tabId",
            );

            // Encontrar a aba pelo ID (mais seguro que usar o índice)
            final int indexToRemove = _tabs.indexWhere(
              (tab) => tab.id == tabId,
            );
            globalLogger.i(
              "Índice da aba a ser removida: $indexToRemove (original: $currentIndex)",
            );

            if (indexToRemove >= 0 && indexToRemove < _tabs.length) {
              final tabToRemove = _tabs[indexToRemove];
              globalLogger.i(
                "Aba encontrada para remoção: ${tabToRemove.title}",
              );

              if (_tabs.length > 1) {
                // Se houver mais de uma aba, remover a atual
                globalLogger.i(
                  "Removendo aba ${tabToRemove.title} (múltiplas abas)",
                );
                removeTab(indexToRemove);
              } else {
                // Se for a única aba, criar uma nova aba vazia e depois remover a atual
                final currentTitle = tabToRemove.title;
                globalLogger.i(
                  "Criando nova aba antes de remover a única aba: $currentTitle",
                );
                addTab(); // Adiciona uma nova aba

                // Encontrar a aba novamente, pois o índice pode ter mudado
                final newIndexToRemove = _tabs.indexWhere(
                  (tab) => tab.id == tabId,
                );
                if (newIndexToRemove >= 0) {
                  globalLogger.i(
                    "Removendo aba original após criar nova: $currentTitle",
                  );
                  removeTab(
                    newIndexToRemove,
                  ); // Remove a aba original
                } else {
                  globalLogger.e(
                    "Não foi possível encontrar a aba original para remover",
                  );
                }
              }
            } else {
              globalLogger.e(
                "Aba não encontrada para remoção: $tabId",
              );
            }
          });

          return true;
        }

        // Remover a aba após publicar com sucesso
        globalLogger.i(
          "Removendo aba após salvamento direto: ${targetTab?.title}",
        );

        // Capturar o ID da aba atual
        final String tabId = currentTabId;
        final int currentIndex = targetTabIndex;

        if (currentIndex >= 0 && currentIndex < _tabs.length) {
          final tabToRemove = _tabs[currentIndex];
          globalLogger.i(
            "Aba encontrada para remoção direta: ${tabToRemove.title}",
          );

          if (_tabs.length > 1) {
            // Se houver mais de uma aba, remover a atual
            globalLogger.i(
              "Removendo aba ${tabToRemove.title} diretamente (múltiplas abas)",
            );
            removeTab(currentIndex);
          } else {
            // Se for a única aba, criar uma nova aba vazia e depois remover a atual
            final currentTitle = tabToRemove.title;
            globalLogger.i(
              "Criando nova aba antes de remover a única aba diretamente: $currentTitle",
            );
            addTab(); // Adiciona uma nova aba

            // Encontrar a aba novamente, pois o índice pode ter mudado
            final newIndexToRemove = _tabs.indexWhere(
              (tab) => tab.id == tabId,
            );
            if (newIndexToRemove >= 0) {
              globalLogger.i(
                "Removendo aba original diretamente após criar nova: $currentTitle",
              );
              removeTab(newIndexToRemove); // Remove a aba original
            } else {
              globalLogger.e(
                "Não foi possível encontrar a aba original para remover diretamente",
              );
            }
          }
        } else {
          globalLogger.e(
            "Aba não encontrada para remoção direta: $tabId",
          );
        }

        return true;
      } else {
        // Atualizar a mensagem de erro na aba correta
        if (targetTab != null) {
          targetTab.publishError =
              "Falha ao salvar o produto no servidor.";
          targetTab.isPublishing =
              false; // Garantir que o indicador de carregamento seja desativado
          notifyListeners();
        }
        return false;
      }
    } catch (e) {
      globalLogger.e("Erro ao publicar produto: $e");

      // Encontrar a aba correta pelo ID
      final targetTabIndex = _tabs.indexWhere(
        (tab) => tab.id == currentTabId,
      );
      if (targetTabIndex >= 0) {
        _tabs[targetTabIndex].publishError = "Erro ao publicar: $e";
        _tabs[targetTabIndex].isPublishing =
            false; // Garantir que o indicador de carregamento seja desativado
        notifyListeners();
      }

      return false;
    } finally {
      // Garantir que o indicador de carregamento seja desativado em todas as situações
      final targetTabIndex = _tabs.indexWhere(
        (tab) => tab.id == currentTabId,
      );
      if (targetTabIndex >= 0) {
        _tabs[targetTabIndex].isPublishing = false;
        notifyListeners();
      }
    }
  }
}
