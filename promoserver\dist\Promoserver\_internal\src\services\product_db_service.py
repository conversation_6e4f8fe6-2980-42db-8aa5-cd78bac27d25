"""
Serviço para operações de banco de dados relacionadas a produtos.
"""

import logging
import uuid
from typing import Dict, List, Optional, Any

from src.models.product_supabase import ProdutoSupabase
from src.models.product_supabase_get import ProdutoSupabaseGet
from src.services.storage_service import StorageService
from src.config.digital_ocean_config import DigitalOceanConfig

log = logging.getLogger(__name__)


class ProductDbService:
    """Serviço para operações de banco de dados relacionadas a produtos."""

    def __init__(
        self, supabase_service, storage_service: Optional[StorageService] = None
    ):
        """
        Inicializa o serviço com a conexão Supabase.

        Args:
            supabase_service: Serviço Supabase para operações de banco de dados
            storage_service: Serviço de armazenamento para upload de imagens (opcional)
        """
        self.supabase_service = supabase_service
        self.storage_service = storage_service

        # Se o storage_service não foi fornecido, tenta criar um
        if self.storage_service is None:
            try:
                do_config = DigitalOceanConfig()
                if do_config.is_valid():
                    self.storage_service = StorageService(do_config)
                    log.info(
                        "Serviço de armazenamento Digital Ocean inicializado com sucesso."
                    )
                else:
                    log.warning(
                        "Configuração do Digital Ocean inválida. Uploads de imagem serão ignorados."
                    )
                    self.storage_service = None
            except Exception as e:
                log.exception(f"Erro ao inicializar serviço de armazenamento: {e}")
                self.storage_service = None

    async def save_product(
        self, product_data: Dict[str, Any]
    ) -> Optional[ProdutoSupabaseGet]:
        """
        Salva um produto no banco de dados, incluindo o upload da imagem para o Digital Ocean.

        Args:
            product_data: Dados do produto a ser salvo

        Returns:
            ProdutoSupabaseGet: Produto salvo ou None se falhar
        """
        try:
            # Verificar se temos uma URL de imagem
            image_url = product_data.get("url_imagem")
            image_id = product_data.get("image_id", "")

            # Se temos uma URL de imagem e o serviço de armazenamento está disponível
            if image_url and not image_id and self.storage_service:
                # Gerar um ID para o produto (será usado como parte do ID da imagem)
                product_id = str(uuid.uuid4())

                # Fazer upload da imagem para o Digital Ocean
                success, do_url, do_image_id = (
                    await self.storage_service.upload_image_from_url(
                        image_url=image_url, product_id=product_id
                    )
                )

                if success and do_url and do_image_id:
                    log.info(
                        f"Imagem enviada com sucesso para o Digital Ocean: {do_url}"
                    )
                    # Atualizar os dados do produto com a nova URL e ID da imagem
                    product_data["url_imagem"] = do_url
                    product_data["image_id"] = do_image_id
                else:
                    log.warning(
                        f"Falha ao enviar imagem para o Digital Ocean. Usando URL original: {image_url}"
                    )

            # Filtrar apenas os campos que são aceitos pelo construtor de ProdutoSupabase
            product_data_copy = product_data.copy()

            # Obter os parâmetros aceitos pelo construtor de ProdutoSupabase
            import inspect

            accepted_params = inspect.signature(
                ProdutoSupabase.__init__
            ).parameters.keys()
            # Remover 'self' da lista de parâmetros
            accepted_params = [param for param in accepted_params if param != "self"]

            # Filtrar os dados para incluir apenas os campos aceitos
            filtered_data = {}
            for key, value in product_data_copy.items():
                if key in accepted_params:
                    filtered_data[key] = value
                else:
                    log.debug(f"Ignorando campo não reconhecido: {key}={value}")

            # Criar o objeto ProdutoSupabase com os dados filtrados
            log.debug(
                f"Criando ProdutoSupabase com campos filtrados: {filtered_data.keys()}"
            )
            produto = ProdutoSupabase(**filtered_data)

            # Converter para dicionário compatível com a tabela
            data_to_insert = produto.to_dict()
            log.debug(f"Dados a serem inseridos no Supabase: {data_to_insert}")

            # Salvar no Supabase (usando o método inserir_produto)
            try:
                response = await self.supabase_service.inserir_produto(produto)
                log.debug(f"Resposta do Supabase após inserção: {response}")
            except Exception as e:
                log.error(f"Erro ao inserir produto no Supabase: {e}")
                raise

            if response and hasattr(response, "data") and response.data:
                saved_data_dict = response.data[0]
                log.info(f"Produto salvo com sucesso: {saved_data_dict.get('id')}")

                # Converter para o modelo ProdutoSupabaseGet
                try:
                    from pydantic import BaseModel

                    if hasattr(BaseModel, "model_validate"):
                        # Pydantic v2
                        saved_product = ProdutoSupabaseGet.model_validate(
                            saved_data_dict
                        )
                    else:
                        # Pydantic v1
                        saved_product = ProdutoSupabaseGet.parse_obj(saved_data_dict)
                    return saved_product
                except Exception as e:
                    log.error(f"Erro ao converter resposta para modelo: {e}")
                    # Criar manualmente o objeto ProdutoSupabaseGet
                    return ProdutoSupabaseGet.from_dict(saved_data_dict)
            else:
                error_detail = getattr(
                    response, "error", "Erro desconhecido no banco de dados"
                )
                log.error(f"Falha ao salvar produto no Supabase: {error_detail}")
                return None

        except Exception as e:
            log.exception(f"Erro ao salvar produto: {e}")
            return None

    async def update_product(
        self, product_id: str, product_data: Dict[str, Any]
    ) -> Optional[ProdutoSupabaseGet]:
        """
        Atualiza um produto no banco de dados.

        Args:
            product_id: ID do produto a ser atualizado
            product_data: Novos dados do produto

        Returns:
            ProdutoSupabaseGet: Produto atualizado ou None se falhar
        """
        try:
            # Verificar se temos uma URL de imagem
            image_url = product_data.get("url_imagem")
            # Obter o ID da imagem (usado mais tarde se houver uma nova imagem)
            _ = product_data.get("image_id", "")

            # Obter o produto atual para verificar se a imagem mudou
            current_product = await self.get_product_by_id(product_id)

            # Verificar se o produto existe
            if not current_product:
                log.error(f"Produto com ID {product_id} não encontrado")
                return None

            # Verificar se temos uma nova imagem para processar
            is_new_image = False

            # Verificar se é uma imagem local (file://)
            # Isso é usado apenas para log, não afeta a lógica
            _ = image_url and image_url.startswith("file://")

            # Verificar se a imagem mudou
            if (
                current_product
                and image_url
                and image_url != current_product.url_imagem
            ):
                is_new_image = True
                log.info(
                    f"Detectada nova imagem para o produto {product_id}. Atual: {current_product.url_imagem}, Nova: {image_url}"
                )

            # Se temos uma URL de imagem nova e o serviço de armazenamento está disponível
            if is_new_image and self.storage_service:
                # Se o produto tinha uma imagem anterior no Digital Ocean, excluir primeiro
                if current_product and current_product.image_id:
                    log.info(
                        f"Excluindo imagem antiga do Digital Ocean antes de fazer upload da nova. ID: {current_product.image_id}"
                    )
                    try:
                        # Excluir a imagem antiga antes de fazer upload da nova
                        deleted = await self.storage_service.delete_image(
                            current_product.url_imagem
                        )
                        if deleted:
                            log.info(
                                f"Imagem antiga excluída com sucesso: {current_product.url_imagem}"
                            )
                        else:
                            log.warning(
                                f"Não foi possível excluir a imagem antiga: {current_product.url_imagem}"
                            )
                    except Exception as e:
                        log.warning(f"Erro ao excluir imagem antiga: {e}")

                # Fazer upload da nova imagem para o Digital Ocean
                log.info(
                    f"Iniciando upload da nova imagem para o Digital Ocean: {image_url}"
                )
                success, do_url, do_image_id = (
                    await self.storage_service.upload_image_from_url(
                        image_url=image_url, product_id=product_id
                    )
                )

                if success and do_url and do_image_id:
                    log.info(
                        f"Nova imagem enviada com sucesso para o Digital Ocean: {do_url}"
                    )
                    # Atualizar os dados do produto com a nova URL e ID da imagem
                    product_data["url_imagem"] = do_url
                    product_data["image_id"] = do_image_id
                else:
                    log.warning(
                        f"Falha ao enviar nova imagem para o Digital Ocean. Usando URL original: {image_url}"
                    )

            # Filtrar apenas os campos que são aceitos pelo construtor de ProdutoSupabase
            product_data_copy = product_data.copy()

            # Obter os parâmetros aceitos pelo construtor de ProdutoSupabase
            import inspect

            accepted_params = inspect.signature(
                ProdutoSupabase.__init__
            ).parameters.keys()
            # Remover 'self' da lista de parâmetros
            accepted_params = [param for param in accepted_params if param != "self"]

            # Filtrar os dados para incluir apenas os campos aceitos
            filtered_data = {}
            for key, value in product_data_copy.items():
                if key in accepted_params:
                    filtered_data[key] = value
                else:
                    log.debug(f"Ignorando campo não reconhecido: {key}={value}")

            # Verificar o campo frete antes de criar o objeto
            if "frete" in filtered_data:
                log.info(
                    f"Campo frete antes de criar ProdutoSupabase: {filtered_data['frete']} (tipo: {type(filtered_data['frete']).__name__})"
                )
                # Garantir que o campo frete seja booleano
                filtered_data["frete"] = bool(filtered_data["frete"])
                log.info(
                    f"Campo frete após conversão: {filtered_data['frete']} (tipo: {type(filtered_data['frete']).__name__})"
                )

            # Criar o objeto ProdutoSupabase com os dados filtrados
            log.debug(
                f"Criando ProdutoSupabase com campos filtrados: {filtered_data.keys()}"
            )
            produto = ProdutoSupabase(**filtered_data)

            # Converter para dicionário compatível com a tabela
            data_to_update = produto.to_dict()
            log.debug(f"Dados a serem atualizados no Supabase: {data_to_update}")

            # Atualizar no Supabase (usando o método atualizar_produto)
            try:
                response = await self.supabase_service.atualizar_produto(
                    product_id, produto
                )
                log.debug(f"Resposta do Supabase após atualização: {response}")
                # Verificar se a resposta contém um erro
                if hasattr(response, "error") and response.error:
                    log.error(f"Erro retornado pelo Supabase: {response.error}")
                    raise Exception(f"Erro retornado pelo Supabase: {response.error}")
            except Exception as e:
                log.error(f"Erro ao atualizar produto no Supabase: {e}")
                raise

            if response and hasattr(response, "data") and response.data:
                saved_data_dict = response.data[0]
                log.info(f"Produto atualizado com sucesso: {saved_data_dict.get('id')}")

                # Converter para o modelo ProdutoSupabaseGet
                try:
                    from pydantic import BaseModel

                    if hasattr(BaseModel, "model_validate"):
                        # Pydantic v2
                        saved_product = ProdutoSupabaseGet.model_validate(
                            saved_data_dict
                        )
                    else:
                        # Pydantic v1
                        saved_product = ProdutoSupabaseGet.parse_obj(saved_data_dict)
                    return saved_product
                except Exception as e:
                    log.error(f"Erro ao converter resposta para modelo: {e}")
                    # Criar manualmente o objeto ProdutoSupabaseGet
                    return ProdutoSupabaseGet.from_dict(saved_data_dict)
            else:
                error_detail = getattr(
                    response, "error", "Erro desconhecido no banco de dados"
                )
                log.error(
                    f"Falha ao atualizar produto no Supabase: {product_id}. Erro: {error_detail}"
                )
                return None

        except Exception as e:
            error_str = str(e).lower()
            if "frete" in error_str or "bool" in error_str or "type" in error_str:
                log.error(f"Possível erro de tipo no campo frete: {error_str}")
                # Verificar se o campo frete está presente nos dados filtrados
                if "frete" in filtered_data:
                    log.error(
                        f"Valor do campo frete nos dados filtrados: {filtered_data['frete']} (tipo: {type(filtered_data['frete']).__name__})"
                    )
                # Verificar se o campo frete está presente nos dados originais
                if "frete" in product_data:
                    log.error(
                        f"Valor do campo frete nos dados originais: {product_data['frete']} (tipo: {type(product_data['frete']).__name__})"
                    )
            log.exception(f"Erro ao atualizar produto: {e}")
            return None

    async def get_product_by_id(self, product_id: str) -> Optional[ProdutoSupabaseGet]:
        """
        Obtém um produto pelo ID.

        Args:
            product_id: ID do produto a ser obtido

        Returns:
            ProdutoSupabaseGet: Produto encontrado ou None se não existir
        """
        try:
            return await self.supabase_service.get_product_by_id(product_id)
        except Exception as e:
            log.exception(f"Erro ao obter produto por ID: {e}")
            return None

    async def get_all_products(self) -> List[ProdutoSupabaseGet]:
        """
        Obtém todos os produtos.

        Returns:
            List[ProdutoSupabaseGet]: Lista de produtos
        """
        try:
            return await self.supabase_service.get_all_products()
        except Exception as e:
            log.exception(f"Erro ao obter todos os produtos: {e}")
            return []

    async def delete_product(self, product_id: str) -> bool:
        """
        Exclui um produto pelo ID.

        Args:
            product_id: ID do produto a ser excluído

        Returns:
            bool: True se excluído com sucesso, False caso contrário
        """
        try:
            # Obter o produto para verificar se tem imagem no Digital Ocean
            product = await self.get_product_by_id(product_id)

            if product and product.image_id and self.storage_service:
                # Tentar excluir a imagem do Digital Ocean
                try:
                    await self.storage_service.delete_image(product.url_imagem)
                    log.info(f"Imagem excluída com sucesso: {product.url_imagem}")
                except Exception as e:
                    log.warning(f"Erro ao excluir imagem: {e}")

            # Excluir o produto do Supabase
            result = await self.supabase_service.delete_product(product_id)

            if result:
                log.info(f"Produto excluído com sucesso: {product_id}")
                return True
            else:
                log.error(f"Falha ao excluir produto do Supabase: {product_id}")
                return False

        except Exception as e:
            log.exception(f"Erro ao excluir produto: {e}")
            return False
