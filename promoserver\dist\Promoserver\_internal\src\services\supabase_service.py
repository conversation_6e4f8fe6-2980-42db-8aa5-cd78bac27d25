import asyncio
from typing import Any, List, Dict, Optional
from supabase import create_client, Client
from gotrue.errors import AuthApiError
import os
import logging
from src.models.product_supabase import ProdutoSupabase
from src.models.product_supabase_get import ProdutoSupabaseGet
from fastapi import HTTPException, status

log = logging.getLogger(__name__)


class SupabaseService:
    _instance = None
    client: Client | None = None

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(SupabaseService, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if hasattr(self, "_initialized") and self._initialized:
            return

        log.info("Inicializando Serviço Supabase...")
        url: str | None = os.environ.get("SUPABASE_URL")
        key: str | None = os.environ.get("SUPABASE_KEY")

        if not url or not key:
            log.error(
                "SUPABASE_URL e SUPABASE_KEY devem ser definidos nas variáveis de ambiente."
            )
            raise ValueError("URL ou Chave do Supabase não configurados.")

        try:
            self.client: Client = create_client(url, key)
            log.info("Cliente Supabase criado com sucesso.")
            self._initialized = True
        except Exception as e:
            log.exception(f"Falha ao criar cliente Supabase: {e}")
            self.client = None
            raise

    async def login_user(self, email: str, password: str) -> dict:
        """Autentica usuário e obtém token do Supabase."""
        if not self.client:
            log.error("Cliente Supabase não inicializado durante tentativa de login.")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Serviço de autenticação não disponível.",
            )
        try:
            if hasattr(
                self.client.auth, "sign_in_with_password"
            ) and asyncio.iscoroutinefunction(self.client.auth.sign_in_with_password):
                response = await self.client.auth.sign_in_with_password(
                    {"email": email, "password": password}
                )
            else:
                response = self.client.auth.sign_in_with_password(
                    {"email": email, "password": password}
                )

            log.info(f"Login no Supabase bem-sucedido para {email}")
            return {
                "access_token": response.session.access_token,
                "token_type": "bearer",
                "user": {
                    "id": response.user.id,
                    "email": response.user.email,
                },
            }
        except AuthApiError as e:
            log.warning(
                f"Falha no login do Supabase para {email}: {e.message} (Status: {e.status})"
            )
            raise HTTPException(
                status_code=e.status if e.status else status.HTTP_401_UNAUTHORIZED,
                detail=f"Falha na autenticação: {e.message}",
            )
        except Exception as e:
            log.exception(
                f"Erro inesperado durante login no Supabase para {email}: {e}"
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Erro interno do servidor durante o login.",
            )

    async def validate_token_with_supabase(self, access_token: str) -> Dict[str, Any]:
        """
        Valida o access token fornecido diretamente com o servidor Supabase.
        Retorna dados do usuário se válido, levanta exceção caso contrário.
        """
        if not self.client:
            log.error("Cliente Supabase não inicializado durante validação de token.")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Serviço de validação de autenticação não disponível.",
            )
        try:
            log.debug("Validando token com Supabase...")
            if hasattr(self.client.auth, "get_user") and asyncio.iscoroutinefunction(
                self.client.auth.get_user
            ):
                user_response = await self.client.auth.get_user(access_token)
            else:
                user_response = self.client.auth.get_user(access_token)

            if user_response and hasattr(user_response, "user"):
                user = user_response.user
                if user and hasattr(user, "id") and user.id:
                    # Reduzido para log.debug para evitar excesso de logs
                    log.debug(
                        f"Token validado via Supabase para usuário ID: {user.id}, Email: {user.email}"
                    )
                    return {
                        "user_id": str(user.id),
                        "email": user.email,
                        "role": user.role if hasattr(user, "role") else None,
                    }
                else:
                    log.warning(
                        "Validação de token via Supabase retornou objeto de usuário sem ID."
                    )
                    raise AuthApiError(
                        "Token de sessão inválido (dados do usuário incompletos).",
                        status=401,
                    )
            else:
                log.warning(
                    f"Validação de token via Supabase retornou estrutura de resposta inesperada: {type(user_response)}"
                )
                raise AuthApiError(
                    "Token de sessão inválido (resposta malformada).", status=401
                )

        except AuthApiError as e:
            log.warning(
                f"Validação de token via Supabase falhou: {e.message} (Status: {e.status})"
            )
            raise HTTPException(
                status_code=e.status if e.status else status.HTTP_401_UNAUTHORIZED,
                detail=f"Token inválido ou expirado: {e.message}",
                headers={"WWW-Authenticate": "Bearer"},
            )
        except Exception as e:
            log.exception(
                f"Erro inesperado ao contactar Supabase para validação de token: {e}"
            )
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Falha ao verificar o token de autenticação com o provedor.",
            )

    async def buscar_produtos(self) -> List[ProdutoSupabaseGet]:
        """Método legado para buscar produtos. Use get_all_products em vez disso."""
        return await self.get_all_products() # Mantém compatibilidade se chamado sem args

    async def get_all_products(self, page: int = 1, limit: Optional[int] = None) -> List[ProdutoSupabaseGet]:
        """Obtém produtos ativos com paginação.

        Args:
            page: O número da página (começando em 1).
            limit: O número de itens por página. Se None, o Supabase pode aplicar um limite padrão (ex: 1000).
                   Para buscar todos os produtos sem limite do cliente (confiando no limite do Supabase),
                   ou para quando o backend da API controla o "sem limite", este pode ser None.
                   No entanto, para paginação efetiva, um limite deve ser fornecido.

        Returns:
            List[ProdutoSupabaseGet]: Lista de produtos
        """
        if not self.client:
            return []
        try:
            query = (
                self.client.table("produtos_cadastro")
                .select("*", count="exact") # Adiciona count="exact" para obter a contagem total
                .eq("ativo", True)
                .order("criado_em", desc=True)
            )

            if limit is not None:
                # Calcula o range para a paginação
                # page é 1-indexed
                range_from = (page - 1) * limit
                range_to = range_from + limit - 1
                query = query.range(range_from, range_to)
            
            response = query.execute()
            
            # O log do total de itens pode ser útil aqui, se o count for usado.
            # if response and hasattr(response, 'count') and response.count is not None:
            #    log.info(f"Total de produtos ativos encontrados no Supabase: {response.count}")

            if response and hasattr(response, "data") and response.data:
                return [ProdutoSupabaseGet.from_dict(item) for item in response.data]
            else:
                log.warning(
                    "Nenhum produto encontrado ou erro na resposta do Supabase."
                )
                return []
        except Exception as e:
            log.exception(f"Erro ao buscar produtos do Supabase: {e}")
            return []

    async def get_product_by_id(self, product_id: str) -> Optional[ProdutoSupabaseGet]:
        """Obtém um produto pelo ID.

        Args:
            product_id: ID do produto a ser obtido

        Returns:
            ProdutoSupabaseGet: Produto encontrado ou None se não existir
        """
        if not self.client:
            return None
        try:
            response = (
                self.client.table("produtos_cadastro")
                .select("*")
                .eq("id", product_id)
                .execute()
            )

            if (
                response
                and hasattr(response, "data")
                and response.data
                and len(response.data) > 0
            ):
                return ProdutoSupabaseGet.from_dict(response.data[0])
            else:
                log.warning(f"Produto {product_id} não encontrado no Supabase.")
                return None
        except Exception as e:
            log.exception(f"Erro ao buscar produto {product_id} do Supabase: {e}")
            return None

    async def inserir_produto(self, produto: ProdutoSupabase) -> Any:
        if not self.client:
            raise Exception("Cliente Supabase não inicializado.")
        try:
            # Obter o dicionário de dados do produto
            data_to_insert = produto.to_dict()

            # O campo url_produto agora existe na tabela, não é mais necessário removê-lo

            # Remover campos que não existem na tabela produtos_cadastro
            campos_nao_existentes = [
                "is_local_image",  # Campo usado apenas no frontend
                "created_at",  # Campo usado no frontend, mas na tabela é criado_em
                "updated_at",  # Campo usado no frontend, mas não existe na tabela
            ]

            for campo in campos_nao_existentes:
                if campo in data_to_insert:
                    log.debug(f"Removendo campo que não existe na tabela: {campo}")
                    del data_to_insert[campo]

            log.debug(f"Inserindo dados do produto: {data_to_insert}")
            log.debug(
                f"Campos sendo enviados para o Supabase: {list(data_to_insert.keys())}"
            )

            response = (
                self.client.table("produtos_cadastro").insert(data_to_insert).execute()
            )

            log.debug(f"Resposta de inserção do Supabase: {response}")
            return response
        except Exception as e:
            log.exception(f"Erro ao inserir produto no Supabase: {e}")
            raise

    async def excluir_produto(self, product_id: str) -> bool:
        """Método legado para excluir produto. Use delete_product em vez disso."""
        return await self.delete_product(product_id)

    async def delete_product(self, product_id: str) -> bool:
        """Exclui um produto pelo ID.

        Args:
            product_id: ID do produto a ser excluído

        Returns:
            bool: True se excluído com sucesso, False caso contrário
        """
        if not self.client:
            return False
        try:
            response = (
                self.client.table("produtos_cadastro")
                .delete()
                .eq("id", product_id)
                .execute()
            )

            if response and hasattr(response, "data") and response.data:
                log.info(f"Produto {product_id} excluído com sucesso do Supabase.")
                return True
            elif response and not getattr(response, "error", None):
                log.info(
                    f"Comando de exclusão do produto {product_id} executado (sem dados retornados). Assumindo sucesso."
                )
                return True
            else:
                log.warning(
                    f"Produto {product_id} não encontrado ou falha na exclusão. Resposta: {response}"
                )
                return False
        except Exception as e:
            log.exception(f"Erro ao excluir produto {product_id} do Supabase: {e}")
            return False

    async def atualizar_produto(self, product_id: str, produto: ProdutoSupabase) -> Any:
        """Atualiza um produto existente no Supabase.

        Args:
            product_id: ID do produto a ser atualizado
            produto: Objeto ProdutoSupabase com os dados atualizados

        Returns:
            Any: Resposta da operação do Supabase

        Raises:
            Exception: Se o cliente Supabase não estiver inicializado ou ocorrer um erro na atualização
        """
        if not self.client:
            raise Exception("Cliente Supabase não inicializado.")
        try:
            # Obter o dicionário de dados do produto
            data_to_update = produto.to_dict()

            # Garantir que o campo frete seja booleano
            if "frete" in data_to_update:
                log.info(
                    f"Campo frete antes de enviar para o Supabase: {data_to_update['frete']} (tipo: {type(data_to_update['frete']).__name__})"
                )
                data_to_update["frete"] = bool(data_to_update["frete"])
                log.info(
                    f"Campo frete após conversão: {data_to_update['frete']} (tipo: {type(data_to_update['frete']).__name__})"
                )

            # O campo url_produto agora existe na tabela, não é mais necessário removê-lo

            # Remover campos que não devem ser atualizados
            if "id" in data_to_update:
                del data_to_update["id"]
            if "criado_em" in data_to_update:
                del data_to_update["criado_em"]

            # Remover campos que não existem na tabela produtos_cadastro
            campos_nao_existentes = [
                "is_local_image",  # Campo usado apenas no frontend
                "created_at",  # Campo usado no frontend, mas na tabela é criado_em
                "updated_at",  # Campo usado no frontend, mas não existe na tabela
            ]

            for campo in campos_nao_existentes:
                if campo in data_to_update:
                    log.debug(f"Removendo campo que não existe na tabela: {campo}")
                    del data_to_update[campo]

            log.debug(f"Atualizando produto {product_id} com dados: {data_to_update}")
            log.debug(
                f"Campos sendo enviados para o Supabase: {list(data_to_update.keys())}"
            )

            try:
                response = (
                    self.client.table("produtos_cadastro")
                    .update(data_to_update)
                    .eq("id", product_id)
                    .execute()
                )

                log.debug(f"Resposta de atualização do Supabase: {response}")

                # Verificar se a resposta contém um erro
                if hasattr(response, "error") and response.error:
                    log.error(
                        f"Erro retornado pelo Supabase na atualização: {response.error}"
                    )

                return response
            except Exception as e:
                log.error(f"Erro ao executar atualização no Supabase: {e}")
                raise
        except Exception as e:
            log.exception(f"Erro ao atualizar produto {product_id} no Supabase: {e}")
            raise
