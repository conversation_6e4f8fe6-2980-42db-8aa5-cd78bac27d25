"""
Serviço para armazenamento de arquivos no Digital Ocean Spaces.
"""

import logging
import base64
import hmac
import hashlib
import time
from typing import Optional, <PERSON>ple
import aiohttp
from email.utils import formatdate

from src.config.digital_ocean_config import DigitalOceanConfig

log = logging.getLogger(__name__)


class StorageService:
    """Serviço para armazenamento de arquivos no Digital Ocean Spaces."""

    def __init__(self, config: DigitalOceanConfig):
        """Inicializa o serviço com a configuração do Digital Ocean."""
        self.config = config

    async def upload_image_from_url(
        self, image_url: str, product_id: str
    ) -> Tuple[bool, Optional[str], Optional[str]]:
        """
        Faz o download de uma imagem a partir de uma URL e a envia para o Digital Ocean Spaces.
        Também suporta caminhos de arquivo local com o prefixo 'file://'.

        Args:
            image_url: URL da imagem a ser baixada ou caminho local com prefixo 'file://'
            product_id: ID do produto ao qual a imagem está associada

        Returns:
            Tuple contendo:
            - bool: Sucesso da operação
            - Optional[str]: URL da imagem no Digital Ocean (se sucesso)
            - Optional[str]: ID único da imagem (se sucesso)
        """
        if not self.config.is_valid():
            log.error(
                "Configuração do Digital Ocean inválida. Upload de imagem cancelado."
            )
            return False, None, None

        try:
            # Gerar um ID único para a imagem
            image_id = f"{product_id}_{int(time.time())}"

            # Verificar se é um caminho de arquivo local
            if image_url.startswith("file://"):
                # Extrair o caminho do arquivo do URL
                file_path = image_url[7:]  # Remover o prefixo 'file://'
                log.info(f"Processando arquivo local: {file_path}")

                try:
                    # Ler o arquivo diretamente do sistema de arquivos
                    with open(file_path, "rb") as f:
                        image_data = f.read()
                except Exception as e:
                    log.error(f"Erro ao ler arquivo local: {file_path}. Erro: {e}")
                    return False, None, None
            else:
                # É uma URL normal, baixar a imagem
                log.info(f"Baixando imagem da URL: {image_url}")
                async with aiohttp.ClientSession() as session:
                    async with session.get(image_url) as response:
                        if response.status != 200:
                            log.error(
                                f"Falha ao baixar imagem da URL: {image_url}. Status: {response.status}"
                            )
                            return False, None, None

                        image_data = await response.read()

            # Determinar o tipo de conteúdo baseado nos primeiros bytes da imagem
            content_type = self._determine_content_type(image_data)
            if not content_type:
                log.error(f"Tipo de imagem não suportado para URL: {image_url}")
                return False, None, None

            # Definir o caminho da imagem no bucket
            path = f"products/{image_id}.{content_type.split('/')[-1]}"

            # Fazer upload para o Digital Ocean
            success, do_url = await self._upload_to_digital_ocean(
                path, image_data, content_type
            )

            if success:
                log.info(f"Imagem enviada com sucesso para o Digital Ocean: {do_url}")
                return True, do_url, image_id
            else:
                log.error("Falha ao enviar imagem para o Digital Ocean")
                return False, None, None

        except Exception as e:
            log.exception(f"Erro ao processar upload de imagem: {e}")
            return False, None, None

    def _determine_content_type(self, image_data: bytes) -> Optional[str]:
        """
        Determina o tipo de conteúdo (MIME type) baseado nos primeiros bytes da imagem.

        Args:
            image_data: Bytes da imagem

        Returns:
            String com o tipo de conteúdo ou None se não for reconhecido
        """
        # Verificar assinaturas de arquivo comuns
        if image_data.startswith(b"\xff\xd8\xff"):
            return "image/jpeg"
        elif image_data.startswith(b"\x89PNG\r\n\x1a\n"):
            return "image/png"
        elif image_data.startswith(b"GIF87a") or image_data.startswith(b"GIF89a"):
            return "image/gif"
        elif image_data.startswith(b"RIFF") and image_data[8:12] == b"WEBP":
            return "image/webp"

        # Se não conseguir determinar, assume PNG
        log.warning("Não foi possível determinar o tipo de imagem. Assumindo PNG.")
        return "image/png"

    async def _upload_to_digital_ocean(
        self, path: str, data: bytes, content_type: str
    ) -> Tuple[bool, Optional[str]]:
        """
        Envia um arquivo para o Digital Ocean Spaces.

        Args:
            path: Caminho do arquivo no bucket
            data: Conteúdo do arquivo em bytes
            content_type: Tipo MIME do conteúdo

        Returns:
            Tuple contendo:
            - bool: Sucesso da operação
            - Optional[str]: URL do arquivo no Digital Ocean (se sucesso)
        """
        try:
            # Gerar a data no formato HTTP
            date = formatdate(timeval=None, localtime=False, usegmt=True)

            # Gerar a string para assinatura
            string_to_sign = self._generate_string_to_sign(path, date, content_type)

            # Calcular a assinatura
            signature = self._calculate_signature(string_to_sign)

            # Preparar os headers
            headers = {
                "Content-Type": content_type,
                "Date": date,
                "Authorization": f"AWS {self.config.access_key}:{signature}",
                "x-amz-acl": "public-read",
            }

            # URL completa para o upload
            url = f"{self.config.spaces_endpoint}/{path}"

            log.info(f"Iniciando upload para: {url}")

            # Fazer o upload
            async with aiohttp.ClientSession() as session:
                async with session.put(url, data=data, headers=headers) as response:
                    log.info(f"Resposta do upload: Status {response.status}")

                    if response.status in (200, 201):
                        return True, url
                    else:
                        response_text = await response.text()
                        log.error(
                            f"Falha no upload. Status: {response.status}, Resposta: {response_text}"
                        )
                        return False, None

        except Exception as e:
            log.exception(f"Erro durante o upload para o Digital Ocean: {e}")
            return False, None

    def _generate_string_to_sign(self, path: str, date: str, content_type: str) -> str:
        """
        Gera a string para assinatura conforme especificação da AWS S3.

        Args:
            path: Caminho do arquivo no bucket
            date: Data formatada para o header HTTP
            content_type: Tipo MIME do conteúdo

        Returns:
            String formatada para assinatura
        """
        return (
            f"PUT\n\n"
            f"{content_type}\n"
            f"{date}\n"
            f"x-amz-acl:public-read\n"
            f"/{self.config.bucket_name}/{path}"
        )

    def _calculate_signature(self, string_to_sign: str) -> str:
        """
        Calcula a assinatura HMAC-SHA1 para autenticação.

        Args:
            string_to_sign: String a ser assinada

        Returns:
            Assinatura codificada em base64
        """
        hmac_obj = hmac.new(
            key=self.config.secret_key.encode("utf-8"),
            msg=string_to_sign.encode("utf-8"),
            digestmod=hashlib.sha1,
        )
        return base64.b64encode(hmac_obj.digest()).decode("utf-8")

    async def delete_image(self, image_url: str) -> bool:
        """
        Exclui uma imagem do Digital Ocean Spaces.

        Args:
            image_url: URL completa da imagem no Digital Ocean

        Returns:
            bool: Sucesso da operação
        """
        log.info(f"Iniciando exclusão de imagem do Digital Ocean: {image_url}")

        if not self.config.is_valid():
            log.error(
                "Configuração do Digital Ocean inválida. Exclusão de imagem cancelada."
            )
            return False

        try:
            # Extrair o caminho relativo da URL
            if not image_url or self.config.spaces_endpoint not in image_url:
                log.error(
                    f"URL inválida ou não pertence ao Digital Ocean configurado: {image_url}"
                )
                return False

            path = image_url.replace(self.config.spaces_endpoint + "/", "")
            log.debug(f"Caminho da imagem a ser excluída: {path}")

            # Gerar a data no formato HTTP
            date = formatdate(timeval=None, localtime=False, usegmt=True)

            # Gerar a string para assinatura (para DELETE)
            string_to_sign = (
                f"DELETE\n\n\n" f"{date}\n" f"/{self.config.bucket_name}/{path}"
            )

            # Calcular a assinatura
            signature = self._calculate_signature(string_to_sign)

            # Preparar os headers
            headers = {
                "Date": date,
                "Authorization": f"AWS {self.config.access_key}:{signature}",
            }

            # Fazer a requisição DELETE
            async with aiohttp.ClientSession() as session:
                async with session.delete(image_url, headers=headers) as response:
                    success = response.status in (
                        200,
                        204,
                        404,
                    )  # 404 também é considerado sucesso (já não existe)

                    if success:
                        log.info(
                            f"Imagem excluída com sucesso do Digital Ocean. Status: {response.status}"
                        )
                    else:
                        log.error(
                            f"Falha ao excluir imagem do Digital Ocean. Status: {response.status}"
                        )

                    return success

        except Exception as e:
            log.exception(f"Erro durante a exclusão da imagem: {e}")
            return False
