import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../../components/custom_dropdown_dialog.dart';
import '../../../../components/input_field_pattern.dart';
import '../../../../models/category_model.dart';
import '../../controllers/tabs_controller.dart';
import '../../models/product_tab.dart';
import 'image_picker_widget.dart';

class ProductInfoSection extends StatelessWidget {
  final TabsController controller = Modular.get<TabsController>();
  final ThemeData theme;

  ProductInfoSection({super.key, required this.theme});

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, _) {
        final ProductTab? tab = controller.currentTab;
        if (tab == null) {
          return const SizedBox.shrink();
        }
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Imagem do produto com seletor de imagem local
                ImagePickerWidget(
                  imageUrl: tab.currentImageUrl,
                  isLoading: tab.isFetching,
                  onImageSelected: controller.setLocalImage,
                  onClearImage:
                      tab.isUsingLocalImage
                          ? controller.clearLocalImage
                          : null,
                  width: 150,
                  height: 150,
                ),
                const SizedBox(width: 16),

                // Informações do produto
                Expanded(
                  child: Column(
                    children: [
                      // Título
                      TextFormField(
                        controller: tab.titleController,
                        readOnly: tab.isFetching,
                        decoration: InputFieldPattern.decoration(
                          label: 'Título do Produto',
                          prefixIcon: Icons.title_outlined,
                        ),
                        style: theme.textTheme.titleMedium,
                        validator:
                            (v) =>
                                (v == null || v.isEmpty)
                                    ? 'Título obrigatório'
                                    : null,
                      ),
                      const SizedBox(height: 16),

                      // Descrição
                      TextFormField(
                        controller: tab.descriptionController,
                        readOnly: tab.isFetching,
                        decoration: InputFieldPattern.decoration(
                          label: 'Descrição (WhatsApp)',
                          prefixIcon: Icons.description_outlined,
                        ),
                        minLines: 1,
                        maxLines: 2,
                      ),
                      const SizedBox(height: 16),

                      // Categorias
                      Row(
                        children: [
                          // Categoria
                          Expanded(
                            child: CustomDropdownDialog<
                              CategoryModel
                            >(
                              value: tab.selectedCategory,
                              items: controller.categories,
                              itemLabelBuilder: (cat) => cat.name,
                              onChanged:
                                  tab.isFetching
                                      ? null
                                      : controller.selectCategory,
                              label: 'Categoria',
                              prefixIcon: Icons.category_outlined,
                              validator:
                                  (v) =>
                                      ((v == null || v.key.isEmpty) &&
                                              (tab.selectedCategory ==
                                                      null ||
                                                  tab
                                                      .selectedCategory!
                                                      .key
                                                      .isEmpty))
                                          ? 'Categoria obrigatória'
                                          : null,
                              isEnabled: !tab.isFetching,
                              maxDialogHeight:
                                  0.6, // Limita a altura a 60% da tela
                              dialogTitle: 'Selecione a Categoria',
                            ),
                          ),
                          const SizedBox(width: 16),

                          // Subcategoria
                          Expanded(
                            child: CustomDropdownDialog<String>(
                              value: tab.selectedSubcategory,
                              items: tab.currentSubcategories,
                              itemLabelBuilder: (sub) => sub,
                              onChanged:
                                  tab.isFetching
                                      ? null
                                      : controller.selectSubcategory,
                              label: 'Subcategoria',
                              prefixIcon:
                                  Icons
                                      .subdirectory_arrow_right_outlined,
                              iconColor:
                                  tab.currentSubcategories.isEmpty
                                      ? Colors.grey
                                      : null,
                              isEnabled:
                                  !tab.isFetching &&
                                  tab.currentSubcategories.isNotEmpty,
                              maxDialogHeight:
                                  0.6, // Limita a altura a 60% da tela
                              dialogTitle: 'Selecione a Subcategoria',
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }
}
