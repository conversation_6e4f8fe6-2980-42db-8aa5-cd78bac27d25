import logging
import asyncio
import concurrent.futures
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from typing import Dict, Optional

from components.auth import verify_supabase_session
from src.utils.selector_validator import SelectorValidator

# Configuração de logging
log = logging.getLogger("api.selectors_validation")

# Criação do router
router = APIRouter(prefix="/selectors-validation", tags=["selectors-validation"])

# Variável global para armazenar o status da validação
validation_status = {"is_running": False, "last_result": None, "progress": 0}


@router.get("/validate-mercadolivre", response_model=Dict)
async def validate_mercadolivre_selectors_endpoint(
    background_tasks: BackgroundTasks,
    product_url: Optional[str] = None,
    user_info=Depends(verify_supabase_session),
):
    """
    Endpoint para validar os seletores do Mercado Livre.

    Args:
        background_tasks: Tarefas em background do FastAPI
        product_url: URL opcional de um produto específico para validar
        user_info: Informações do usuário autenticado

    Returns:
        Dict com status da validação
    """
    global validation_status

    try:
        # Verifica se já está em execução
        if validation_status["is_running"]:
            return {
                "status": "already_running",
                "message": "Uma validação já está em andamento",
                "progress": validation_status["progress"],
            }

        # Inicia a validação em background
        validation_status = {"is_running": True, "last_result": None, "progress": 0}

        log.info(
            f"Usuário {user_info.get('email')} iniciou validação de seletores do Mercado Livre"
        )

        # Executa a validação em background
        background_tasks.add_task(_run_validation_in_background, product_url=product_url)

        return {"status": "started", "message": "Validação iniciada em background"}
    except Exception as e:
        log.error(f"Erro ao iniciar validação de seletores: {e}")
        log.exception(e)
        return {"status": "error", "message": f"Erro ao iniciar validação: {e}"}


@router.get("/status", response_model=Dict)
async def get_validation_status(
    user_info=Depends(verify_supabase_session),
):  # user_info é usado apenas para autenticação
    """
    Retorna o status atual da validação de seletores.

    Args:
        user_info: Informações do usuário autenticado

    Returns:
        Dict com status da validação
    """
    global validation_status

    return {
        "is_running": validation_status["is_running"],
        "last_result": validation_status["last_result"],
        "progress": validation_status["progress"],
    }


@router.post("/update-selector", response_model=Dict)
async def update_selector_endpoint(
    store_id: str,
    selector_id: int,
    active: bool,
    user_info=Depends(verify_supabase_session),
):
    """
    Atualiza o status de um seletor específico.

    Args:
        store_id: ID da loja (ex: "mercadolivre")
        selector_id: ID do seletor
        active: Status do seletor (ativo/inativo)
        user_info: Informações do usuário autenticado

    Returns:
        Dict com resultado da atualização
    """
    log.info(
        f"Usuário {user_info.get('email')} atualizando seletor {selector_id} para {active}"
    )

    validator = SelectorValidator(store_id)
    selector = validator.selector_manager.find_selector_by_id(selector_id)

    if not selector:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Seletor com ID {selector_id} não encontrado",
        )

    # Atualiza o seletor
    result = validator.selector_manager.update_selector(
        selector_id=selector_id,
        store_id=store_id,
        selector_type=selector.get("type"),
        selector_text=selector.get("selector"),
        description=selector.get("description"),
        active=active,
    )

    if result:
        validator.selector_manager.save_selectors(store_id)
        return {
            "success": True,
            "message": f"Seletor {selector_id} atualizado com sucesso",
        }
    else:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erro ao atualizar seletor",
        )


def validate_mercadolivre_selectors(test_url: str) -> Dict:
    """
    Valida seletores do Mercado Livre usando IA.

    Args:
        test_url: URL para testar os seletores

    Returns:
        Dict com estatísticas da validação
    """
    log.info(f"Validando seletores do Mercado Livre em: {test_url}")

    # Inicializa o validador de seletores
    validator = SelectorValidator("mercadolivre")

    # Executa a validação
    stats = validator.validate_selectors(test_url)

    log.info(f"Validação concluída para URL {test_url}. Estatísticas: {stats}")
    return stats


async def _run_validation_in_background(product_url: Optional[str] = None):
    """
    Executa a validação de seletores em background usando IA.

    Args:
        product_url: URL opcional de um produto específico para validar
    """
    global validation_status

    try:
        log.info(
            "Iniciando validação de seletores do Mercado Livre com IA e métodos manuais"
        )

        # Valida seletores na página principal/ofertas
        validation_status["progress"] = 10
        validation_status["last_result"] = {
            "success": True,
            "message": "Analisando página principal com IA e métodos manuais...",
            "progress": 10,
        }

        # Executa a validação em uma thread separada para não bloquear o event loop
        with concurrent.futures.ThreadPoolExecutor() as executor:
            stats = await asyncio.get_event_loop().run_in_executor(
                executor,
                lambda: validate_mercadolivre_selectors(
                    "https://www.mercadolivre.com.br/ofertas"
                ),
            )

        validation_status["progress"] = 50
        validation_status["last_result"] = {
            "success": True,
            "stats": stats,
            "message": "Página principal analisada. Processando página de produto com IA e métodos manuais...",
            "progress": 50,
        }

        # Se fornecida, valida também em uma página de produto específica
        if product_url:
            log.info(f"Analisando página de produto: {product_url}")

            # Executa a validação em uma thread separada para não bloquear o event loop
            with concurrent.futures.ThreadPoolExecutor() as executor:
                product_stats = await asyncio.get_event_loop().run_in_executor(
                    executor, lambda: validate_mercadolivre_selectors(product_url)
                )

            validation_status["progress"] = 90

            # Combina estatísticas
            for key in stats:
                stats[key] += product_stats[key]

        # Atualiza o status final
        validation_status["last_result"] = {
            "success": True,
            "stats": stats,
            "message": "Validação concluída com sucesso usando IA e métodos manuais",
        }
        log.info(f"Validação concluída. Estatísticas: {stats}")
    except Exception as e:
        log.error(f"Erro durante validação de seletores: {e}")
        validation_status["last_result"] = {
            "success": False,
            "message": f"Erro durante validação: {str(e)}",
        }
    finally:
        validation_status["is_running"] = False
        validation_status["progress"] = 100


def initialize():
    """Inicializa o módulo de validação de seletores."""
    log.info("Módulo de validação de seletores inicializado.")
