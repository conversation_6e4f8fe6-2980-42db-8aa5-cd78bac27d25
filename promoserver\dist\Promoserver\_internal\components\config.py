"""
Módulo de configuração para a API do Promoserver.
Contém funções e variáveis relacionadas à configuração da aplicação.
"""

import logging
import os
import sys
from dotenv import load_dotenv

log = logging.getLogger("api.config")


def setup_logging():
    """Configura o logging da aplicação."""
    # Importar o gerenciador de logs universal
    from src.utils.log_manager import get_log_manager

    log_manager = get_log_manager()

    # Configurar o logger raiz para usar o console e remover handlers de arquivo
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)

    # Limpar handlers existentes
    for handler in root_logger.handlers[
        :
    ]:  # Cria uma cópia da lista para evitar problemas durante a iteração
        if isinstance(handler, logging.FileHandler):
            root_logger.removeHandler(handler)

    # Verificar se já existe um handler de console
    has_console_handler = any(
        isinstance(h, logging.StreamHandler) and not isinstance(h, logging.FileHandler)
        for h in root_logger.handlers
    )

    # Adicionar handler de console se não existir
    if not has_console_handler:
        console_handler = logging.StreamHandler()
        log_format = "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
        console_handler.setFormatter(logging.Formatter(log_format))
        console_handler.setLevel(logging.INFO)
        root_logger.addHandler(console_handler)

    # Registrar loggers específicos no gerenciador de logs
    log_manager.register_logger("src.services.promohunter_service_scrapy")
    log_manager.register_logger("src.services.scrapy_service")
    log_manager.register_logger("src.scrapers.base.ai_content_generator")

    # Configurar loggers de FastAPI e Uvicorn para reduzir verbosidade
    logging.getLogger("uvicorn").setLevel(logging.ERROR)
    logging.getLogger("uvicorn.access").setLevel(logging.ERROR)
    logging.getLogger("uvicorn.error").setLevel(logging.ERROR)
    logging.getLogger("fastapi").setLevel(logging.ERROR)

    # Configurar loggers de Starlette para reduzir verbosidade
    logging.getLogger("starlette").setLevel(logging.ERROR)
    logging.getLogger("starlette.middleware").setLevel(logging.ERROR)
    logging.getLogger("starlette.routing").setLevel(logging.ERROR)

    # Configurar loggers de bibliotecas externas para reduzir verbosidade
    logging.getLogger("httpcore").setLevel(logging.ERROR)
    logging.getLogger("httpx").setLevel(logging.ERROR)
    logging.getLogger("hpack").setLevel(logging.ERROR)

    return logging.getLogger("api")


def setup_environment():
    """
    Configura o ambiente da aplicação.

    Returns:
        tuple: (project_root, supabase_url, supabase_key)
    """
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(script_dir)  # Um nível acima da pasta components

    if project_root not in sys.path:
        sys.path.insert(0, project_root)
        log.info(f"Adicionado {project_root} ao sys.path")
    # Limpar variáveis de ambiente que podem ter sido definidas anteriormente
    # para garantir que apenas as variáveis do arquivo correto sejam usadas
    env_vars_to_clear = [
        "SUPABASE_URL",
        "SUPABASE_KEY",
        "DO_REGION",
        "DO_BUCKET_NAME",
        "DO_ACCESS_KEY",
        "DO_SECRET_KEY",
        "DO_SPACES_ENDPOINT",
        "GOOGLE_AI_STUDIO_KEY",
    ]
    for var in env_vars_to_clear:
        if var in os.environ:
            del os.environ[var]
            log.info(f"Variável de ambiente {var} removida para evitar conflitos")

    # Definir qual arquivo .env usar
    # TODO: Mudar para produção quando necessário
    env_file = ".env.prod"
    # env_file = ".env.dev"

    dotenv_path = os.path.join(project_root, env_file)
    if os.path.exists(dotenv_path):
        # Carregar o arquivo .env especificado, sobrescrevendo variáveis existentes
        load_dotenv(dotenv_path=dotenv_path, override=True)
        log.info(f"Arquivo de ambiente carregado de {dotenv_path}")

    else:
        log.warning(
            f"Arquivo {env_file} não encontrado. Utilizando variáveis de ambiente do sistema."
        )

    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY")

    if not supabase_url or not supabase_key:
        log.critical(
            "FATAL: SUPABASE_URL e SUPABASE_KEY não encontrados nas variáveis de ambiente!"
        )
        sys.exit("FATAL: URL/Key do Supabase devem ser configurados.")

    return project_root, supabase_url, supabase_key


def initialize_services():
    """
    Inicializa os serviços da aplicação.

    Returns:
        tuple: (selector_manager, product_service, supabase_service, category_loader, product_db_service, promohunter_service)
    """
    # Verificar se a variável de ambiente GOOGLE_AI_STUDIO_KEY está definida
    google_ai_key = os.getenv("GOOGLE_AI_STUDIO_KEY")
    log.info(f"GOOGLE_AI_STUDIO_KEY: {'Presente' if google_ai_key else 'Ausente'}")

    try:
        from src.utils.selector_manager import SelectorManager
        from src.services.product_service import ProductService
        from src.services.supabase_service import SupabaseService
        from src.services.product_db_service import ProductDbService
        from src.services.storage_service import StorageService

        # Usar o serviço Scrapy para o PromoHunter
        from src.services.promohunter_service_scrapy import (
            PromoHunterServiceScrapy as PromoHunterService,
        )
        from src.config.digital_ocean_config import DigitalOceanConfig
        from src.scrapers.base.base_scraper import ProductCategories
    except ImportError as e:
        log.exception(
            f"FATAL: Falha ao importar módulos de lógica de negócio: {e}. Verifique as importações e caminhos."
        )
        sys.exit(f"Erro de Importação: {e}")

    try:
        # Inicializar serviços básicos
        selector_manager = SelectorManager()
        product_service = ProductService()
        supabase_service = SupabaseService()
        category_loader = ProductCategories()

        # Inicializar serviço de armazenamento Digital Ocean
        try:
            do_config = DigitalOceanConfig()
            if do_config.is_valid():
                storage_service = StorageService(do_config)
                log.info(
                    "Serviço de armazenamento Digital Ocean inicializado com sucesso."
                )
            else:
                log.warning(
                    "Configuração do Digital Ocean inválida. Uploads de imagem serão ignorados."
                )
                storage_service = None
        except Exception as e:
            log.warning(f"Erro ao inicializar serviço de armazenamento: {e}")
            storage_service = None

        # Inicializar serviço de banco de dados de produtos
        product_db_service = ProductDbService(supabase_service, storage_service)

        # Inicializar serviço do PromoHunter
        promohunter_service = PromoHunterService(selector_manager)

        return (
            selector_manager,
            product_service,
            supabase_service,
            category_loader,
            product_db_service,
            promohunter_service,
        )
    except Exception as e:
        log.exception(f"FATAL: Falha ao inicializar serviços: {e}")
        sys.exit(f"Falha ao inicializar serviços: {e}")


def configure_cors(app):
    """
    Configura o CORS para a aplicação.

    Args:
        app: Instância do FastAPI
    """
    from fastapi.middleware.cors import CORSMiddleware

    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
