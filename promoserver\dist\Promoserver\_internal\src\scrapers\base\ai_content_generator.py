import asyncio
import logging
import os
import re
import json
from abc import ABC, abstractmethod
from typing import Optional, Dict, List, Any, Tuple
import httpx

# Importa ProductCategories para usar na geração de categorias/subcategorias
from .product_categories import ProductCategories

logger = logging.getLogger(__name__)


class LLMProvider(ABC):
    """Classe base abstrata para provedores de LLM."""

    @abstractmethod
    async def generate_content(self, prompt: str, temperature: float = 0.7, max_tokens: int = 100) -> str:
        """Gera conteúdo baseado no prompt fornecido."""
        pass

    @abstractmethod
    def is_available(self) -> bool:
        """Verifica se o provedor está disponível."""
        pass

    @property
    @abstractmethod
    def provider_name(self) -> str:
        """Retorna o nome do provedor."""
        pass


class GeminiProvider(LLMProvider):
    """Provedor de LLM usando Google Gemini."""

    def __init__(self, api_key: str = None, model: str = "gemini-2.5-flash-preview-04-17"):
        self.api_key = api_key or os.getenv("GOOGLE_AI_STUDIO_KEY")
        self.model_name = model
        self.client = None
        self._available = False

        try:
            from google import genai
            from google.genai import types
            self.genai = genai
            self.types = types

            if self.api_key:
                self.client = genai.Client(api_key=self.api_key)
                self._available = True
                logger.info(f"Modelo Google AI ({model}) configurado com sucesso.")
            else:
                logger.error("Chave da API Google AI não encontrada")
        except Exception as e:
            logger.error(f"Erro ao configurar Google AI: {e}")

    async def generate_content(self, prompt: str, temperature: float = 0.7, max_tokens: int = 100) -> str:
        """Gera conteúdo usando o Gemini."""
        if not self._available or not self.client:
            return ""

        try:
            generation_config = self.types.GenerateContentConfig(
                temperature=temperature,
                max_output_tokens=max_tokens,
            )

            raw_response_content = await asyncio.to_thread(
                self.client.models.generate_content,
                model=self.model_name,
                contents=prompt,
                config=generation_config,
            )

            if raw_response_content and hasattr(raw_response_content, "text"):
                return raw_response_content.text.strip()
            else:
                logger.warning("Resposta vazia ou inválida do Gemini")
                return ""
        except Exception as e:
            logger.error(f"Erro ao gerar conteúdo com Gemini: {e}")
            return ""

    def is_available(self) -> bool:
        """Verifica se o Gemini está disponível."""
        return self._available

    @property
    def provider_name(self) -> str:
        """Retorna o nome do provedor."""
        return "Gemini"


class OpenAIProvider(LLMProvider):
    """Provedor de LLM usando OpenAI."""

    def __init__(self, api_key: str = None, model: str = "gpt-3.5-turbo", endpoint: str = None):
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")
        self.model_name = model
        self.endpoint = endpoint
        self._available = False

        try:
            if self.api_key:
                self._available = True
                logger.info(f"Modelo OpenAI ({model}) configurado com sucesso.")
            else:
                logger.error("Chave da API OpenAI não encontrada")
        except Exception as e:
            logger.error(f"Erro ao configurar OpenAI: {e}")

    async def generate_content(self, prompt: str, temperature: float = 0.7, max_tokens: int = 100) -> str:
        """Gera conteúdo usando a OpenAI."""
        if not self._available:
            return ""

        try:
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

            payload = {
                "model": self.model_name,
                "messages": [{"role": "user", "content": prompt}],
                "temperature": temperature,
                "max_tokens": max_tokens
            }

            # Usar o endpoint personalizado se fornecido, caso contrário usar o padrão da OpenAI
            url = self.endpoint if self.endpoint else "https://api.openai.com/v1/chat/completions"

            async with httpx.AsyncClient() as client:
                response = await client.post(url, json=payload, headers=headers)
                response.raise_for_status()
                result = response.json()

                if "choices" in result and len(result["choices"]) > 0:
                    return result["choices"][0]["message"]["content"].strip()
                else:
                    logger.warning("Resposta vazia ou inválida da OpenAI")
                    return ""
        except Exception as e:
            logger.error(f"Erro ao gerar conteúdo com OpenAI: {e}")
            return ""

    def is_available(self) -> bool:
        """Verifica se a OpenAI está disponível."""
        return self._available

    @property
    def provider_name(self) -> str:
        """Retorna o nome do provedor."""
        return "OpenAI"


class LLMConfig:
    """Gerencia a configuração dos provedores de LLM."""

    CONFIG_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), "llm_config.json")

    @classmethod
    def save_config(cls, configs: Dict[str, Dict[str, Any]]) -> None:
        """Salva as configurações de LLM no arquivo."""
        try:
            with open(cls.CONFIG_FILE, 'w') as f:
                json.dump(configs, f, indent=4)
            logger.info(f"Configurações de LLM salvas em {cls.CONFIG_FILE}")
        except Exception as e:
            logger.error(f"Erro ao salvar configurações de LLM: {e}")

    @classmethod
    def load_config(cls) -> Dict[str, Dict[str, Any]]:
        """Carrega as configurações de LLM do arquivo."""
        default_config = {
            "selected": "Gemini",
            "providers": {
                "Gemini": {
                    "api_key": os.getenv("GOOGLE_AI_STUDIO_KEY", ""),
                    "model": "gemini-2.5-flash-preview-04-17"
                },
                "OpenAI": {
                    "api_key": os.getenv("OPENAI_API_KEY", ""),
                    "model": "gpt-3.5-turbo",
                    "endpoint": ""
                }
            }
        }

        try:
            if os.path.exists(cls.CONFIG_FILE):
                with open(cls.CONFIG_FILE, 'r') as f:
                    return json.load(f)
            else:
                # Criar arquivo de configuração padrão se não existir
                cls.save_config(default_config)
                return default_config
        except Exception as e:
            logger.error(f"Erro ao carregar configurações de LLM: {e}")
            return default_config

    @classmethod
    def get_selected_provider(cls) -> Tuple[str, Dict[str, Any]]:
        """Retorna o provedor selecionado e suas configurações."""
        config = cls.load_config()
        selected = config.get("selected", "Gemini")
        provider_config = config.get("providers", {}).get(selected, {})
        return selected, provider_config

    @classmethod
    def set_selected_provider(cls, provider_name: str) -> None:
        """Define o provedor selecionado."""
        config = cls.load_config()
        if provider_name in config.get("providers", {}):
            config["selected"] = provider_name
            cls.save_config(config)
            logger.info(f"Provedor selecionado alterado para {provider_name}")
        else:
            logger.error(f"Provedor {provider_name} não encontrado nas configurações")

    @classmethod
    def add_or_update_provider(cls, provider_name: str, config: Dict[str, Any]) -> None:
        """Adiciona ou atualiza um provedor nas configurações."""
        all_config = cls.load_config()
        all_config.setdefault("providers", {})[provider_name] = config
        cls.save_config(all_config)
        logger.info(f"Provedor {provider_name} adicionado/atualizado nas configurações")


class AIContentGenerator:
    """Gera conteúdo relacionado a produtos (descrições, categorias) e analisa seletores usando APIs de IA."""

    def __init__(self, product_categories: ProductCategories = None):
        self.product_categories = product_categories
        self.available = False
        self.fallback_description = ""  # Define fallback aqui

        # Carregar configuração e inicializar o provedor selecionado
        provider_name, provider_config = LLMConfig.get_selected_provider()
        self.provider = self._initialize_provider(provider_name, provider_config)

        if self.provider and self.provider.is_available():
            self.available = True
            logger.info(f"Provedor de LLM {provider_name} inicializado com sucesso")

            # Definir categorias padrão para testes se necessário
            if self.product_categories and not self.product_categories.categories:
                logger.info("Usando categorias padrão para testes")
        else:
            logger.error(f"Falha ao inicializar provedor de LLM {provider_name}")

    def _initialize_provider(self, provider_name: str, config: Dict[str, Any]) -> Optional[LLMProvider]:
        """Inicializa o provedor de LLM com base no nome e configuração."""
        try:
            if provider_name == "Gemini":
                return GeminiProvider(api_key=config.get("api_key"), model=config.get("model"))
            elif provider_name == "OpenAI":
                return OpenAIProvider(
                    api_key=config.get("api_key"),
                    model=config.get("model"),
                    endpoint=config.get("endpoint")
                )
            else:
                logger.error(f"Provedor de LLM desconhecido: {provider_name}")
                return None
        except Exception as e:
            logger.error(f"Erro ao inicializar provedor {provider_name}: {e}")
            return None

    async def generate_product_description(self, product_name: str) -> str:
        """
        Gera uma descrição promocional curta e impactante para o produto usando IA.

        Args:
            product_name: O nome do produto.

        Returns:
            Uma string com a descrição gerada ou uma descrição padrão em caso de falha.
        """
        if not self.available or not self.provider:
            logger.warning("IA não disponível. Retornando descrição padrão.")
            return self.fallback_description

        try:
            prompt = (
                f"Crie uma frase promocional extremamente curta e chamativa (máximo 10 palavras) para o produto '{product_name}'. "
                f"Comece OBRIGATORIAMENTE com um emoji relevante. Use linguagem direta e vendedora. "
                f"Exemplos: ✨ Elegância e Potência! | 🚀 Leve sua música aonde for! | 💄 Realce sua beleza natural!"
                f"NÃO use emojis no final. Seja conciso e persuasivo."
            )

            logger.info(f"Gerando descrição para: '{product_name}' usando {self.provider.provider_name}")

            # Gera o conteúdo usando o provedor selecionado
            description = await self.provider.generate_content(
                prompt=prompt,
                temperature=0.8,  # Aumenta um pouco a criatividade
                max_tokens=50,  # Limita o tamanho da resposta
            )

            # Processa a resposta
            description = description.strip().strip('"').strip()

            # Garante que comece com emoji (verifica ampla gama de emojis comuns)
            if description and not re.match(
                r"^[\U0001F300-\U0001FAFF\U00002600-\U000027BF]", description
            ):
                description = ""
            elif not description:  # Caso a IA retorne texto vazio mas não erro
                description = self.fallback_description

            # Limpeza adicional: remove quebras de linha e espaços extras
            description = " ".join(description.split())

            # Trunca se for excessivamente longo (apesar do prompt)
            if len(description.split()) > 12:
                description = " ".join(description.split()[:12]) + "..."

            logger.info(f"Descrição gerada para '{product_name}': {description}")
            return description

        except Exception as e:
            logger.error(
                f"Erro ao gerar descrição do produto para '{product_name}': {e}",
                exc_info=True,
            )
            return self.fallback_description

    async def generate_product_category(self, product_name: str) -> Optional[str]:
        """
        Sugere a chave da categoria mais provável para o produto usando IA e a lista de categorias.
        Considera tanto as categorias quanto as subcategorias para encontrar a melhor correspondência.

        Args:
            product_name: O nome do produto.

        Returns:
            A chave da categoria (string numérica) ou None se não puder determinar.
        """
        if not self.available or not self.provider:
            logger.warning("IA não disponível. Não é possível gerar categoria.")
            return None
        if not self.product_categories.categories:
            logger.warning(
                "Lista de categorias não carregada. Não é possível gerar categoria."
            )
            return None

        try:
            # Prepara a lista de opções de categorias e subcategorias para o prompt
            category_options = []
            subcategory_details = []

            for cat_key, cat_data in self.product_categories.categories.items():
                category_name = cat_data.get("name")
                subcategories = cat_data.get("subcategories", [])

                if category_name:
                    # Formato: "key. Nome da Categoria"
                    category_options.append(f"{cat_key}. {category_name}")

                    # Adiciona detalhes das subcategorias para cada categoria
                    if subcategories:
                        subcategory_list = ", ".join(subcategories)
                        subcategory_details.append(
                            f"Categoria {cat_key} ({category_name}) inclui: {subcategory_list}"
                        )

            if not category_options:
                logger.error("Nenhuma opção de categoria formatada para o prompt.")
                return None

            category_list_str = "\n".join(category_options)
            subcategory_details_str = "\n".join(subcategory_details)

            prompt = (
                f"Analise cuidadosamente o nome do produto: '{product_name}'.\n\n"
                f"Categorias disponíveis:\n{category_list_str}\n\n"
                f"Detalhes das subcategorias:\n{subcategory_details_str}\n\n"
                f"Instruções:\n"
                f"1. Analise o nome do produto e identifique palavras-chave relevantes.\n"
                f"2. Verifique TANTO as categorias principais QUANTO as subcategorias listadas.\n"
                f"3. Considere que um produto pode pertencer a várias categorias, mas você deve escolher a mais específica.\n"
                f"4. Dê prioridade à categoria que contenha uma subcategoria que corresponda exatamente ao tipo de produto.\n\n"
                f"Exemplos:\n"
                f"- 'Jogo de Panelas Antiaderente' pertence a 'Espaço Decor' porque contém a subcategoria 'Utensílios domésticos e de cozinha'.\n"
                f"- 'Cômoda Infantil' pertence a 'Mundo Baby' porque contém a subcategoria 'Móveis Infantis'.\n\n"
                f"Responda EXCLUSIVAMENTE com o NÚMERO da categoria mais adequada (por exemplo, '1'). NÃO inclua nenhum texto adicional.\n"
                f"Se nenhuma categoria se encaixar bem, responda EXCLUSIVAMENTE com 'N/A'."
            )

            logger.info(f"Gerando categoria para: '{product_name}' usando {self.provider.provider_name}")

            # Gera o conteúdo usando o provedor selecionado
            response_text = await self.provider.generate_content(
                prompt=prompt,
                temperature=0.2,  # Baixa temperatura para resposta direta
                max_tokens=10,  # Limita o tamanho da resposta para obter apenas o número
            )

            # Processa a resposta
            response_text = response_text.strip().strip('"').strip().lower()
            logger.debug(f"Texto da resposta da IA para categoria: '{response_text}'")

            if response_text == "n/a":
                logger.info(f"IA indicou 'N/A' para categoria de '{product_name}'.")
                return None

            # Tenta extrair um número da resposta, mesmo que haja texto adicional.
            match = re.search(r"\d+", response_text)
            if match:
                category_key = match.group(0)
                # Valida se a chave existe nas categorias carregadas
                if category_key in self.product_categories.categories:
                    category_name = self.product_categories.get_category_name(
                        category_key
                    )
                    logger.info(
                        f"Categoria gerada para '{product_name}': {category_name} (Chave: {category_key})"
                    )
                    return category_key
                else:
                    logger.warning(
                        f"Chave de categoria '{category_key}' gerada pela IA ('{response_text}') não existe na lista de categorias."
                    )
                    return None
            else:
                logger.warning(
                    f"Não foi possível extrair um número de categoria válido da resposta da IA: '{response_text}'"
                )
                return None

        except Exception as e:
            logger.error(
                f"Erro ao gerar categoria do produto para '{product_name}': {e}",
                exc_info=True,
            )
            return None

    async def generate_product_subcategory(
        self, product_name: str, category_key: str
    ) -> Optional[str]:
        """
        Sugere o índice da subcategoria mais provável para o produto dentro de uma categoria específica.
        Analisa detalhadamente o nome do produto para encontrar a subcategoria mais adequada.

        Args:
            product_name: O nome do produto.
            category_key: A chave da categoria previamente determinada.

        Returns:
            O índice da subcategoria (string numérica) ou None.
        """
        if not self.available or not self.provider:
            logger.warning("IA não disponível. Não é possível gerar subcategoria.")
            return None
        if not category_key or category_key not in self.product_categories.categories:
            logger.warning(
                f"Chave de categoria '{category_key}' inválida ou não fornecida. Não é possível gerar subcategoria."
            )
            return None

        category_data = self.product_categories.categories.get(category_key, {})
        subcategories = category_data.get("subcategories", [])
        category_name = category_data.get(
            "name", category_key
        )  # Usa a chave se o nome não existir

        if not subcategories:
            logger.info(
                f"Categoria '{category_name}' (Chave: {category_key}) não possui subcategorias definidas."
            )
            return None  # Não há subcategorias para escolher

        try:
            # Prepara a lista de opções de subcategorias para o prompt
            subcategory_options = []
            for idx, subcategory_name in enumerate(subcategories):
                # Formato: "index. Nome da Subcategoria"
                subcategory_options.append(f"{idx}. {subcategory_name}")

            subcategory_list_str = "\n".join(subcategory_options)

            # Exemplos específicos para a categoria atual
            examples = {
                "1": "'Jogo de Panelas Antiaderente' pertence à subcategoria 'Utensílios domésticos e de cozinha'",  # Espaço Decor
                "2": "'Liquidificador Philips' pertence à subcategoria 'Liquidificadores, mixers e processadores'",  # Eletro Lar
                "7": "'Cômoda Infantil' pertence à subcategoria 'Móveis Infantis'",  # Mundo Baby
            }

            example_text = examples.get(category_key, "")
            if example_text:
                example_section = f"Exemplo para esta categoria: {example_text}\n\n"
            else:
                example_section = ""

            prompt = (
                f"Analise cuidadosamente o produto: '{product_name}'\n\n"
                f"Este produto pertence à categoria '{category_name}' (ID: {category_key}).\n\n"
                f"Subcategorias disponíveis nesta categoria:\n{subcategory_list_str}\n\n"
                f"{example_section}"
                f"Instruções:\n"
                f"1. Analise o nome do produto e identifique palavras-chave relevantes.\n"
                f"2. Considere a função, tipo e características do produto.\n"
                f"3. Escolha a subcategoria mais específica e adequada.\n"
                f"4. Se o produto for um conjunto ou kit, considere o tipo principal de item no conjunto.\n\n"
                f"Responda EXCLUSIVAMENTE com o NÚMERO da subcategoria mais adequada (por exemplo, '2'). NÃO inclua nenhum texto adicional.\n"
                f"Se nenhuma subcategoria específica se encaixar bem, responda com o NÚMERO correspondente a 'Mais produtos relacionados'."
            )

            logger.info(
                f"Gerando subcategoria para: '{product_name}' (Categoria: {category_name}) usando {self.provider.provider_name}"
            )

            # Gera o conteúdo usando o provedor selecionado
            response_text = await self.provider.generate_content(
                prompt=prompt,
                temperature=0.2,  # Baixa temperatura para resposta direta
                max_tokens=10,  # Limita o tamanho da resposta para obter apenas o número
            )

            # Processa a resposta
            response_text = response_text.strip().strip('"').strip().lower()
            logger.debug(f"Texto da resposta da IA para subcategoria: '{response_text}'")

            fallback_subcategory_name = "Mais produtos relacionados"
            fallback_subcategory_idx_str = None
            try:
                fallback_idx = subcategories.index(fallback_subcategory_name)
                fallback_subcategory_idx_str = str(fallback_idx)
                logger.debug(
                    f"Índice de fallback para '{fallback_subcategory_name}': {fallback_subcategory_idx_str}"
                )
            except ValueError:
                logger.warning(
                    f"Subcategoria de fallback '{fallback_subcategory_name}' não encontrada na lista para categoria '{category_name}'."
                )
                # Se o fallback não existe, não podemos usá-lo. Isso não deveria acontecer.

            if response_text == "n/a":
                logger.info(
                    f"IA indicou 'N/A' para subcategoria de '{product_name}'. Usando fallback '{fallback_subcategory_name}' se disponível."
                )
                if fallback_subcategory_idx_str is not None:
                    logger.info(
                        f"Subcategoria definida como fallback: {fallback_subcategory_name} (Índice: {fallback_subcategory_idx_str})"
                    )
                    return fallback_subcategory_idx_str
                return None  # Fallback não encontrado

            # Tenta extrair um número da resposta, mesmo que haja texto adicional.
            match = re.search(r"\d+", response_text)
            if match:
                subcategory_idx_str = match.group(0)
                try:
                    idx = int(subcategory_idx_str)
                    # Valida se o índice é válido para a lista de subcategorias
                    if 0 <= idx < len(subcategories):
                        subcategory_name = subcategories[idx]
                        logger.info(
                            f"Subcategoria gerada para '{product_name}': {subcategory_name} (Índice: {idx})"
                        )
                        return str(idx)  # Retorna o índice como string
                    else:
                        logger.warning(
                            f"Índice de subcategoria '{idx}' gerado pela IA ('{response_text}') está fora dos limites (0-{len(subcategories)-1}) para a categoria '{category_name}'. Usando fallback se disponível."
                        )
                        if fallback_subcategory_idx_str is not None:
                            logger.info(
                                f"Subcategoria definida como fallback: {fallback_subcategory_name} (Índice: {fallback_subcategory_idx_str})"
                            )
                            return fallback_subcategory_idx_str
                        return None  # Fallback não encontrado
                except ValueError:
                    logger.warning(
                        f"Não foi possível converter o índice de subcategoria '{subcategory_idx_str}' ('{response_text}') para inteiro. Usando fallback se disponível."
                    )
                    if fallback_subcategory_idx_str is not None:
                        logger.info(
                            f"Subcategoria definida como fallback: {fallback_subcategory_name} (Índice: {fallback_subcategory_idx_str})"
                        )
                        return fallback_subcategory_idx_str
                    return None  # Fallback não encontrado
            else:
                logger.warning(
                    f"Não foi possível extrair um número de índice válido da resposta da IA: '{response_text}'. Usando fallback se disponível."
                )
                if fallback_subcategory_idx_str is not None:
                    logger.info(
                        f"Subcategoria definida como fallback: {fallback_subcategory_name} (Índice: {fallback_subcategory_idx_str})"
                    )
                    return fallback_subcategory_idx_str
                return None  # Fallback não encontrado

        except Exception as e:
            logger.error(
                f"Erro ao gerar subcategoria para '{product_name}': {e}", exc_info=True
            )
            return None

    def identify_selectors_sync(
        self, html_content: str, store_id: str
    ) -> Dict[str, List[Dict[str, Any]]]:
        """
        Identifica seletores CSS em uma página HTML usando IA (versão síncrona).

        Args:
            html_content: Conteúdo HTML da página
            store_id: ID da loja (ex: "mercadolivre")

        Returns:
            Dicionário com tipos de seletores e suas informações
        """
        if not self.available or not self.provider:
            logger.warning("IA não disponível. Não é possível identificar seletores.")
            return {}

        # Limita o tamanho do HTML para evitar exceder limites da API
        max_html_length = 60000  # Aumentado para 60k caracteres

        # Se o HTML for muito grande, processa em partes
        if len(html_content) > max_html_length:
            logger.info(
                f"HTML muito grande ({len(html_content)} caracteres). Processando em partes."
            )

            # Extrai a parte principal do HTML (geralmente o conteúdo principal está no meio)
            # Pega o início (head e parte inicial do body)
            head_part = html_content[:15000]

            # Pega o meio (onde geralmente estão os produtos)
            middle_start = max(15000, (len(html_content) - max_html_length) // 2)
            middle_part = html_content[
                middle_start : middle_start + max_html_length - 30000
            ]

            # Pega o final (onde podem estar scripts importantes)
            tail_part = html_content[-15000:]

            # Combina as partes
            html_content = (
                head_part
                + "\n<!-- Parte do meio -->\n"
                + middle_part
                + "\n<!-- Parte final -->\n"
                + tail_part
            )

            logger.info(
                f"HTML processado em partes. Novo tamanho: {len(html_content)} caracteres."
            )

        try:
            # Prepara o prompt para análise de seletores
            selector_types = [
                {
                    "name": "product",
                    "description": "Seletor para o container do produto inteiro",
                },
                {"name": "title", "description": "Seletor para o título do produto"},
                {
                    "name": "price",
                    "description": "Seletor para o preço atual do produto",
                },
                {
                    "name": "old_price",
                    "description": "Seletor para o preço antigo/original do produto (riscado)",
                },
                {
                    "name": "image",
                    "description": "Seletor para a imagem principal do produto",
                },
                {"name": "link", "description": "Seletor para o link do produto"},
                {
                    "name": "installments",
                    "description": "Seletor para informações de parcelamento",
                },
                {
                    "name": "coupon",
                    "description": "Seletor para cupom de desconto, se disponível",
                },
                {
                    "name": "shipping",
                    "description": "Seletor para informações de frete",
                },
                {
                    "name": "product_url",
                    "description": "Seletor para a URL canônica do produto",
                },
            ]

            selector_types_str = "\n".join(
                [
                    f"{i+1}. {s['name']}: {s['description']}"
                    for i, s in enumerate(selector_types)
                ]
            )

            prompt = f"""
            Você é um especialista em web scraping e precisa identificar seletores CSS em uma página do {store_id}.
            Analise o HTML abaixo e identifique os seletores CSS mais precisos para cada tipo de informação.

            Tipos de seletores a identificar:
            {selector_types_str}

            Para cada tipo de seletor, identifique até 3 seletores CSS alternativos que possam funcionar.
            Priorize seletores que sejam estáveis e menos propensos a mudanças (classes e IDs específicos).

            Instruções específicas para o Mercado Livre:
            1. Para imagens, priorize seletores como '.poly-card__portada img', 'img.poly-component__picture', ou qualquer seletor que capture imagens de produtos com URLs contendo 'http2.mlstatic.com'
            2. Para preços, procure seletores como '.andes-money-amount__fraction' dentro de containers de preço
            3. Para links de produtos, priorize seletores como 'a.poly-component__title' ou links que levem a páginas de produtos
            4. Para cupons, procure seletores como '.poly-coupons__pill' ou elementos que contenham textos de cupom

            HTML da página:
            ```html
            {html_content}
            ```

            Responda em formato JSON estruturado assim:
            ```json
            {{
              "selectors": {{
                "product": [
                  {{
                    "selector": ".selector-css",
                    "description": "Descrição do que este seletor captura",
                    "confidence": 0.9  // Nível de confiança de 0.0 a 1.0
                  }}
                ],
                "title": [ ... ],
                // outros tipos de seletores
              }}
            }}
            ```

            Inclua APENAS o JSON na sua resposta, sem texto adicional.
            """

            logger.info(f"Analisando HTML para identificar seletores do {store_id} usando {self.provider.provider_name}")

            # Como este método é síncrono, precisamos executar o método assíncrono em um loop de eventos
            import asyncio

            # Cria um novo loop de eventos se não estiver em um contexto assíncrono
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # Se já estiver em um contexto assíncrono, cria um novo loop
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
            except RuntimeError:
                # Se não houver loop de eventos, cria um novo
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            # Executa a chamada assíncrona no loop de eventos
            response_text = loop.run_until_complete(
                self.provider.generate_content(
                    prompt=prompt,
                    temperature=0.2,
                    max_tokens=8192
                )
            )

            # Processa a resposta
            response_text = response_text.strip()

            # Remove marcadores de código se presentes
            if response_text.startswith("```json"):
                response_text = response_text.replace("```json", "", 1)
            if response_text.endswith("```"):
                response_text = response_text.rsplit("```", 1)[0]

            response_text = response_text.strip()

            try:
                import json

                result = json.loads(response_text)

                # Verifica se o resultado tem a estrutura esperada
                if "selectors" in result and isinstance(result["selectors"], dict):
                    logger.info(f"Seletores identificados com sucesso para {store_id}")
                    return result["selectors"]
                else:
                    logger.warning("Resposta da IA não contém a estrutura esperada de seletores")
                    return {}
            except json.JSONDecodeError as e:
                logger.error(f"Erro ao decodificar JSON da resposta da IA: {e}")
                logger.debug(f"Resposta recebida: {response_text[:500]}...")
                return {}

        except Exception as e:
            logger.error(f"Erro ao identificar seletores: {e}")
            return {}

    async def identify_selectors(
        self, html_content: str, store_id: str
    ) -> Dict[str, List[Dict[str, Any]]]:
        """
        Identifica seletores CSS em uma página HTML usando IA.

        Args:
            html_content: Conteúdo HTML da página
            store_id: ID da loja (ex: "mercadolivre")

        Returns:
            Dicionário com tipos de seletores e suas informações
        """
        if not self.available or not self.provider:
            logger.warning("IA não disponível. Não é possível identificar seletores.")
            return {}

        # Limita o tamanho do HTML para evitar exceder limites da API
        max_html_length = 60000  # Aumentado para 60k caracteres

        # Se o HTML for muito grande, processa em partes
        if len(html_content) > max_html_length:
            logger.info(
                f"HTML muito grande ({len(html_content)} caracteres). Processando em partes."
            )

            # Extrai a parte principal do HTML (geralmente o conteúdo principal está no meio)
            # Pega o início (head e parte inicial do body)
            head_part = html_content[:15000]

            # Pega o meio (onde geralmente estão os produtos)
            middle_start = max(15000, (len(html_content) - max_html_length) // 2)
            middle_part = html_content[
                middle_start : middle_start + max_html_length - 30000
            ]

            # Pega o final (onde podem estar scripts importantes)
            tail_part = html_content[-15000:]

            # Combina as partes
            html_content = (
                head_part
                + "\n<!-- Parte do meio -->\n"
                + middle_part
                + "\n<!-- Parte final -->\n"
                + tail_part
            )

            logger.info(
                f"HTML processado em partes. Novo tamanho: {len(html_content)} caracteres."
            )

        try:
            # Prepara o prompt para análise de seletores
            selector_types = [
                {
                    "name": "product",
                    "description": "Seletor para o container do produto inteiro",
                },
                {"name": "title", "description": "Seletor para o título do produto"},
                {
                    "name": "price",
                    "description": "Seletor para o preço atual do produto",
                },
                {
                    "name": "old_price",
                    "description": "Seletor para o preço antigo/original do produto (riscado)",
                },
                {
                    "name": "image",
                    "description": "Seletor para a imagem principal do produto",
                },
                {"name": "link", "description": "Seletor para o link do produto"},
                {
                    "name": "installments",
                    "description": "Seletor para informações de parcelamento",
                },
                {
                    "name": "coupon",
                    "description": "Seletor para cupom de desconto, se disponível",
                },
                {
                    "name": "shipping",
                    "description": "Seletor para informações de frete",
                },
                {
                    "name": "product_url",
                    "description": "Seletor para a URL canônica do produto",
                },
            ]

            selector_types_str = "\n".join(
                [
                    f"{i+1}. {s['name']}: {s['description']}"
                    for i, s in enumerate(selector_types)
                ]
            )

            prompt = f"""
            Você é um especialista em web scraping e precisa identificar seletores CSS em uma página do {store_id}.
            Analise o HTML abaixo e identifique os seletores CSS mais precisos para cada tipo de informação.

            Tipos de seletores a identificar:
            {selector_types_str}

            Para cada tipo de seletor, identifique até 3 seletores CSS alternativos que possam funcionar.
            Priorize seletores que sejam estáveis e menos propensos a mudanças (classes e IDs específicos).

            Instruções específicas para o Mercado Livre:
            1. Para imagens, priorize seletores como '.poly-card__portada img', 'img.poly-component__picture', ou qualquer seletor que capture imagens de produtos com URLs contendo 'http2.mlstatic.com'
            2. Para preços, procure seletores como '.andes-money-amount__fraction' dentro de containers de preço
            3. Para links de produtos, priorize seletores como 'a.poly-component__title' ou links que levem a páginas de produtos
            4. Para cupons, procure seletores como '.poly-coupons__pill' ou elementos que contenham textos de cupom

            HTML da página:
            ```html
            {html_content}
            ```

            Responda em formato JSON estruturado assim:
            ```json
            {{
              "selectors": {{
                "product": [
                  {{
                    "selector": ".selector-css",
                    "description": "Descrição do que este seletor captura",
                    "confidence": 0.9  // Nível de confiança de 0.0 a 1.0
                  }}
                ],
                "title": [ ... ],
                // outros tipos de seletores
              }}
            }}
            ```

            Inclua APENAS o JSON na sua resposta, sem texto adicional.
            """

            logger.info(f"Analisando HTML para identificar seletores do {store_id} usando {self.provider.provider_name}")

            # Gera o conteúdo usando o provedor selecionado
            response_text = await self.provider.generate_content(
                prompt=prompt,
                temperature=0.2,
                max_tokens=8192
            )

            # Processa a resposta
            response_text = response_text.strip()

            # Remove marcadores de código se presentes
            if response_text.startswith("```json"):
                response_text = response_text.replace("```json", "", 1)
            if response_text.endswith("```"):
                response_text = response_text.rsplit("```", 1)[0]

            response_text = response_text.strip()

            try:
                import json

                result = json.loads(response_text)

                # Verifica se o resultado tem a estrutura esperada
                if "selectors" in result and isinstance(result["selectors"], dict):
                    logger.info(f"Seletores identificados com sucesso para {store_id}")
                    return result["selectors"]
                else:
                    logger.warning("Resposta da IA não contém a estrutura esperada de seletores")
                    return {}
            except json.JSONDecodeError as e:
                logger.error(f"Erro ao decodificar JSON da resposta da IA: {e}")
                logger.debug(f"Resposta recebida: {response_text[:500]}...")
                return {}

        except Exception as e:
            logger.error(f"Erro ao identificar seletores: {e}", exc_info=True)
            return {}
