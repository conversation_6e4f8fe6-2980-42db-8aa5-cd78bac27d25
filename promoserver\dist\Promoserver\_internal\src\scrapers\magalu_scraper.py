"""
Adaptador para o scraper do Magalu usando Scrapy
"""

import logging
import asyncio
import re
from typing import Dict, Optional

from .runner import scrape_magalu
from .base.base_scraper import BaseScraper

logger = logging.getLogger(__name__)


class MagaluScraper(BaseScraper):
    """
    Scraper para o Magalu usando Scrapy
    """

    def __init__(self):
        super().__init__(store_id="magalu")
        self.scrapy_available = True
        logger.info("MagaluScraper inicializado com Scrapy")

    def extract_product_id(self, url: str) -> Optional[str]:
        """
        Extrai o ID do produto da URL do Magalu
        """
        if not url:
            return None

        pattern1 = r"/p/([a-zA-Z0-9]+)/"
        match1 = re.search(pattern1, url)
        if match1:
            return match1.group(1)

        pattern2 = r"/produto/[^/]+/(\d{7,})/?|/p/[^/]+/(\d{7,})/?|/([a-zA-Z0-9]{10,})/?$"
        match2 = re.search(pattern2, url)
        if match2:
            # Pega o grupo que não for None (ID numérico ou alfanumérico)
            prod_id = next((g for g in match2.groups() if g is not None), None)
            if prod_id:
                return prod_id

        logger.warning(f"Não foi possível extrair o ID do produto da URL: {url}")
        return None

    async def scrape(self, url: str) -> Dict:
        """
        Raspa os detalhes do produto do Magalu usando Scrapy
        """
        logger.info(f"Iniciando raspagem com Scrapy para URL: {url}")

        try:
            # Usa o runner do Scrapy para raspar o produto
            products = await scrape_magalu(url=url, max_pages=1)

            if not products:
                logger.warning(f"Nenhum produto encontrado para a URL: {url}")
                return self._error_response("Nenhum produto encontrado", url)

            # Pega o primeiro produto (deve ser o único para URLs de produto)
            product = products[0]

            # Extrai o ID do produto se não estiver presente
            if not product.get("product_id"):
                product_id = self.extract_product_id(url)
                if product_id:
                    product["product_id"] = product_id

            # Garante que todos os campos necessários estão presentes
            product.setdefault("platform", "Magazine Luiza")
            product.setdefault("url_produto", url)
            # product.setdefault("url_afiliado", "") # Spider já define
            # product.setdefault("description", "") # Será preenchido pela IA
            product.setdefault("error", False)

            # --- Adicionar Enriquecimento com IA ---
            product_title = product.get("title")
            # Garante que os campos existam mesmo se a IA falhar ou o título não for encontrado
            product["description"] = self._ai_generator.fallback_description if self._ai_generator else ""
            product["categoryKey"] = None
            product["category"] = None
            product["subcategoryIndex"] = None
            product["subcategory"] = None

            if product_title and product_title != "Título não encontrado":
                logger.info(f"Tentando gerar conteúdo com IA para: {product_title}")

                try:
                    # Gerar Descrição
                    generated_description = await self.generate_product_description(product_title)
                    product["description"] = generated_description
                    logger.info(f"Descrição da IA: {generated_description}")

                    # Gerar Categoria
                    category_key = await self.generate_product_category(product_title)
                    product["categoryKey"] = category_key
                    product["category"] = self.get_category_name(category_key) if category_key else None
                    logger.info(f"Chave da Categoria da IA: {category_key}, Nome: {product['category']}")

                    # Gerar Subcategoria (apenas se a categoria foi encontrada)
                    if category_key:
                        subcategory_idx = await self.generate_product_subcategory(product_title, category_key)
                        product["subcategoryIndex"] = subcategory_idx
                        product["subcategory"] = self.get_subcategory_name(category_key, subcategory_idx) if subcategory_idx else None
                        logger.info(f"Índice da Subcategoria da IA: {subcategory_idx}, Nome: {product['subcategory']}")
                    else:
                        product["subcategoryIndex"] = None
                        product["subcategory"] = None
                        logger.info("Subcategoria não gerada pois a categoria não foi encontrada.")
                except Exception as ai_exc:
                    logger.error(f"Erro durante a geração de conteúdo com IA para '{product_title}': {ai_exc}", exc_info=True)
                    # Mantém os valores de fallback/None definidos anteriormente
            else:
                logger.warning("Título do produto não disponível ou inválido para geração de conteúdo com IA.")
                 # Fallbacks já estão definidos

            logger.info(f"Raspagem com Scrapy e IA (se aplicável) concluída com sucesso para: {url}")
            return product

        except Exception as e:
            logger.error(f"Erro ao raspar com Scrapy: {e}")
            return self._error_response(f"Erro ao raspar com Scrapy: {str(e)}", url)
