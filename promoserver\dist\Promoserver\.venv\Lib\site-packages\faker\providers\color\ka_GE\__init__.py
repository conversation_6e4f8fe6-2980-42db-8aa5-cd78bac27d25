from collections import OrderedDict

from .. import Provider as ColorProvider

localized = True


class Provider(ColorProvider):
    """Implement color provider for ``ka_GE`` locale."""

    all_colors = OrderedDict(
        (
            ("ანტიკური თეთრი", "#FAEBD7"),
            ("აკვამარინი", "#7FFFD4"),
            ("ლურჯი", "#F0FFFF"),
            ("ბეჟი", "#F5F5DC"),
            ("შავი", "#000000"),
            ("ცისფერი", "#0000FF"),
            ("საზღვაო ლურჯი", "#8A2BE2"),
            ("ყავისფერი", "#A52A2A"),
            ("შოკოლადისფერი", "#D2691E"),
            ("კორალისფერი", "#FF7F50"),
            ("ვერცხლისფერი", "#6495ED"),
            ("ჟოლოსფერი", "#DC143C"),
            ("მუქი ლურჯი", "#00008B"),
            ("მუქი ცისფერი", "#008B8B"),
            ("მუქი ნაცრისფერი", "#A9A9A9"),
            ("მუქი მწვანე", "#006400"),
            ("მუქი ხაკისფერი", "#BDB76B"),
            ("მუქი ნარინჯისფერი", "#FF8C00"),
            ("მუქი წითელი", "#8B0000"),
            ("მუქი ზღვისფერი", "#00CED1"),
            ("მუქი იასამნისფერი", "#9400D3"),
            ("მუქი ვარდისფერი", "#FF1493"),
            ("მკვდარი ნაცრისფერი", "#696969"),
            ("ფუქსია", "#FF00FF"),
            ("ოქროსფერი", "#FFD700"),
            ("ნაცრისფერი", "#808080"),
            ("მწვანე", "#008000"),
            ("ყვითელ-მწვანე", "#ADFF2F"),
            ("ინტენსიური ვარდისფერი", "#FF69B4"),
            ("ინდიგო", "#4B0082"),
            ("სპილოს ძვალი", "#FFFFF0"),
            ("ხაკისფერი", "#F0E68C"),
            ("ლავანდისფერი ვარდისფერი", "#FFF0F5"),
            ("ღია ლურჯი", "#ADD8E6"),
            ("ღია ცისფერი", "#E0FFFF"),
            ("ღია ნაცრისფერი", "#D3D3D3"),
            ("ღია მწვანე", "#90EE90"),
            ("ღია ვარდისფერი", "#FFB6C1"),
            ("ღია ცისფერი", "#87CEFA"),
            ("ღია ყვითელი", "#FFFFE0"),
            ("მეწამული", "#800000"),
            ("ნარინჯისფერი", "#FFA500"),
            ("ნარინჯისფერი წითელი", "#FF4500"),
            ("ღია მწვანე", "#98FB98"),
            ("ღია ზღვისფერი", "#AFEEEE"),
            ("ვარდისფერი", "#FFC0CB"),
            ("ქლიავისფერი", "#DDA0DD"),
            ("იასამნისფერი", "#800080"),
            ("წითელი", "#FF0000"),
            ("ზღვისფერი", "#2E8B57"),
            ("ვერცხლისფერი", "#C0C0C0"),
            ("თურქული ლურჯი", "#40E0D0"),
            ("იისფერი", "#EE82EE"),
            ("თეთრი", "#FFFFFF"),
            ("ყვითელი", "#FFFF00"),
            ("ყვითელ-მწვანე", "#9ACD32"),
            ("ბრინჯაოსფერი", "#CD7F32"),
            ("მსხლისფერი", "#D1E231"),
            ("თითბერისფერი", "#D5A642"),
            ("მანდარინისფერი", "#FF8800"),
            ("ფიჭვისფერი", "#01796F"),
            ("ზეთისხილისფერი", "#808000"),
        )
    )

    safe_colors = (
        "შავი",
        "მუქი წითელი",
        "მწვანე",
        "ხაკისფერი",
        "იასამნისფერი",
        "თურმანჯურა",
        "ცაცხვისფერი",
        "ცისფერი",
        "ვერცხლისფერი",
        "ნაცრისფერი",
        "ყვითელი",
        "ფუქსია",
        "თეთრი",
    )
