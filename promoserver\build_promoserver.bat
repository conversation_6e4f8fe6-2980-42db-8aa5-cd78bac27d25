@echo off
echo ========================================
echo    PROMOSERVER - BUILD AUTOMATIZADO
echo ========================================
echo.

echo [1/4] Ativando ambiente virtual...
call .venv\Scripts\activate.bat
if errorlevel 1 (
    echo ERRO: Falha ao ativar o ambiente virtual
    pause
    exit /b 1
)

echo [2/4] Limpando builds anteriores...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"

echo [3/4] Gerando executavel com PyInstaller...
pyinstaller promoserver.spec --clean
if errorlevel 1 (
    echo ERRO: Falha na geracao do executavel
    pause
    exit /b 1
)

echo [4/6] Copiando pastas necessarias...
echo Copiando venv...
if exist ".venv" (
    xcopy ".venv" "dist\Promoserver\.venv" /E /I /Y >nul 2>&1
    echo OK: Pasta venv copiada
) else (
    echo AVISO: Pasta venv nao encontrada
)
echo Copiando components...
if exist "components" (
    xcopy "components" "dist\Promoserver\components" /E /I /Y >nul 2>&1
    echo OK: Pasta components copiada
) else (
    echo AVISO: Pasta components nao encontrada
)

echo Copiando src...
if exist "src" (
    xcopy "src" "dist\Promoserver\src" /E /I /Y >nul 2>&1
    echo OK: Pasta src copiada
) else (
    echo AVISO: Pasta src nao encontrada
)

echo Copiando assets...
if exist "assets" (
    xcopy "assets" "dist\Promoserver\assets" /E /I /Y >nul 2>&1
    echo OK: Pasta assets copiada
) else (
    echo AVISO: Pasta assets nao encontrada
)
if exist ".env.prod" (
    copy ".env.prod" "dist\Promoserver\.env.prod" >nul 2>&1
    echo OK: Arquivo .env.prod copiado
) else (
    echo AVISO: Arquivo .env.prod nao encontrado
)
echo [5/6] Copiando arquivo api_main.py...
if exist "api_main.py" (
    copy "api_main.py" "dist\Promoserver\api_main.py" >nul 2>&1
    echo OK: Arquivo api_main.py copiado
) else (
    echo AVISO: Arquivo api_main.py nao encontrado
)

echo [6/6] Verificando estrutura final...
if exist "dist\Promoserver\Promoserver.exe" (
    echo OK: Executavel criado com sucesso!
    echo OK: Pasta components incluida
    echo OK: Pasta src incluida
    echo OK: Assets incluidos
    echo.
    echo EXECUTAVEL PRONTO: dist\Promoserver\Promoserver.exe
    echo.
    echo Estrutura final:
    dir "dist\Promoserver" /b
) else (
    echo ERRO: Executavel nao foi criado
    pause
    exit /b 1
)

echo.
echo ========================================
echo           BUILD CONCLUIDO!
echo ========================================
echo.
echo O executavel esta pronto em: dist\Promoserver\
echo Agora voce pode:
echo 1. Executar: dist\Promoserver\Promoserver.exe
echo 2. Distribuir a pasta completa: dist\Promoserver\
echo.
pause
