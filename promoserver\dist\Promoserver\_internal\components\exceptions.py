"""
Módulo de exceções para a API do Promoserver.
Centraliza as exceções personalizadas utilizadas na aplicação.
"""

import logging
from fastapi import HTTPException, status

log = logging.getLogger("api.exceptions")


class ServiceUnavailableException(HTTPException):
    """Exceção para quando um serviço não está disponível."""

    def __init__(self, service_name: str):
        detail = f"{service_name} não disponível"
        super().__init__(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail=detail)
        log.warning(f"Serviço indisponível: {service_name}")


class ResourceNotFoundException(HTTPException):
    """Exceção para quando um recurso não é encontrado."""

    def __init__(self, resource_type: str, resource_id: str):
        detail = f"{resource_type} ID {resource_id} não encontrado"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)
        log.warning(f"Recurso não encontrado: {resource_type} ID {resource_id}")


class InternalServerErrorException(HTTPException):
    """Exceção para erros internos do servidor."""

    def __init__(self, error_message: str, original_exception: Exception = None):
        detail = f"Erro interno do servidor: {error_message}"
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail
        )
        if original_exception:
            log.exception(f"Erro interno: {error_message}", exc_info=original_exception)
        else:
            log.error(f"Erro interno: {error_message}")


class BadRequestException(HTTPException):
    """Exceção para requisições inválidas."""

    def __init__(self, detail: str):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)
        log.warning(f"Requisição inválida: {detail}")


def handle_exception(e: Exception, context: str = "") -> HTTPException:
    """
    Função auxiliar para tratar exceções e convertê-las em HTTPException.

    Args:
        e: A exceção original
        context: Contexto adicional para o log

    Returns:
        HTTPException: Uma exceção HTTP apropriada
    """
    if isinstance(e, HTTPException):
        return e

    error_msg = f"{context}: {str(e)}" if context else str(e)
    return InternalServerErrorException(error_msg, e)


def format_exception_response(e: Exception, context: str = ""):
    """
    Formata uma exceção para o modelo de resposta padrão.

    Args:
        e: A exceção original
        context: Contexto adicional para o log

    Returns:
        ApiResponse: Uma resposta de API formatada
    """
    from components.response_models import ApiResponse

    if isinstance(e, HTTPException):
        return ApiResponse.error_response(
            message=e.detail, status_code=e.status_code, errors=[str(e)]
        )

    error_msg = f"{context}: {str(e)}" if context else str(e)
    log.error(f"Erro interno: {error_msg}", exc_info=e)
    return ApiResponse.error_response(
        message=error_msg, status_code=500, errors=[str(e)]
    )
