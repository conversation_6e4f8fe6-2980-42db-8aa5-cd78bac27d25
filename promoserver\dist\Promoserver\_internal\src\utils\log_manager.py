"""
Gerenciador de logs universal para o PromoServer.
Permite que os logs sejam exibidos na UI sem salvar em arquivo.
"""

import logging
import tkinter as tk
from typing import Optional, Callable

# Singleton para o gerenciador de logs
_instance = None

class LogManager:
    """Gerenciador de logs universal para o PromoServer."""

    def __init__(self):
        """Inicializa o gerenciador de logs."""
        self.log_text = None
        self.log_callback = None
        self.logger = logging.getLogger("promoserver")

        # Remover todos os handlers existentes para evitar duplicação
        for handler in self.logger.handlers[:]:  # Cria uma cópia da lista para evitar problemas durante a iteração
            self.logger.removeHandler(handler)

        # Configurar logger padrão apenas para console
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)

        # Configurar o logger raiz para não propagar logs para handlers de arquivo
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:  # Cria uma cópia da lista para evitar problemas durante a iteração
            if isinstance(handler, logging.FileHandler):
                root_logger.removeHandler(handler)

        # Filtrar mensagens de debug para não aparecerem na UI
        self.debug_filter = lambda record: record.levelno > logging.DEBUG
        handler.addFilter(self.debug_filter)

        # Lista para armazenar logs recentes
        self.recent_logs = []

    def set_log_widget(self, log_text: tk.Text):
        """
        Define o widget de texto para exibir logs na UI.

        Args:
            log_text: Widget de texto do Tkinter
        """
        self.log_text = log_text

    def set_log_callback(self, callback: Callable[[str, Optional[str]], None]):
        """
        Define uma função de callback para processar logs.

        Args:
            callback: Função que recebe a mensagem e a tag opcional
        """
        self.log_callback = callback

    def log_message(self, message: str, tag: Optional[str] = None, level: str = "INFO"):
        """
        Registra uma mensagem de log.

        Args:
            message: Mensagem a ser registrada
            tag: Tag opcional para formatação na UI
            level: Nível de log (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        """
        # Registrar no logger padrão (apenas para console, não para arquivo)
        log_method = getattr(self.logger, level.lower(), self.logger.info)
        log_method(message)

        # Adicionar à lista de logs recentes
        self.recent_logs.append((level, message, tag))
        if len(self.recent_logs) > 1000:  # Limitar o tamanho da lista
            self.recent_logs.pop(0)

        # Exibir na UI se disponível
        if self.log_callback:
            self.log_callback(message, tag)
        elif self.log_text:
            self._update_log_text(message, tag)

    def _update_log_text(self, message: str, tag: Optional[str] = None):
        """
        Atualiza o widget de texto com a mensagem de log.

        Args:
            message: Mensagem a ser exibida
            tag: Tag opcional para formatação
        """
        if not self.log_text:
            return

        self.log_text.config(state=tk.NORMAL)
        if tag:
            self.log_text.insert(tk.END, message + "\n", (tag,))
            self.log_text.tag_config(tag, foreground=tag)
        else:
            self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)

    def debug(self, message: str, tag: Optional[str] = None):
        """Registra uma mensagem de nível DEBUG."""
        self.log_message(message, tag, "DEBUG")

    def info(self, message: str, tag: Optional[str] = None):
        """Registra uma mensagem de nível INFO."""
        self.log_message(message, tag, "INFO")

    def warning(self, message: str, tag: Optional[str] = None):
        """Registra uma mensagem de nível WARNING."""
        self.log_message(message, tag, "WARNING")

    def error(self, message: str, tag: Optional[str] = None):
        """Registra uma mensagem de nível ERROR."""
        self.log_message(message, tag, "ERROR")

    def critical(self, message: str, tag: Optional[str] = None):
        """Registra uma mensagem de nível CRITICAL."""
        self.log_message(message, tag, "CRITICAL")

    def register_logger(self, logger_name: str):
        """Registra um logger para usar o gerenciador de logs.

        Args:
            logger_name: Nome do logger a ser registrado
        """
        logger = logging.getLogger(logger_name)

        # Remover handlers existentes
        for handler in logger.handlers[:]:  # Cria uma cópia da lista para evitar problemas durante a iteração
            logger.removeHandler(handler)

        # Adicionar handler personalizado que redireciona para o gerenciador de logs
        class LogManagerHandler(logging.Handler):
            def __init__(self, log_manager):
                super().__init__()
                self.log_manager = log_manager

            def emit(self, record):
                level = record.levelname.lower()
                message = self.format(record)
                log_method = getattr(self.log_manager, level, self.log_manager.info)
                log_method(message)

        handler = LogManagerHandler(self)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)

        # Configurar o logger para não propagar logs para o logger raiz
        # Isso evita que os logs sejam salvos em arquivo
        logger.propagate = False

        return logger


def remove_log_file():
    """
    Remove o arquivo de log existente, se houver.
    """
    import os
    import glob

    # Remove o arquivo logs.log
    log_file = "logs.log"
    if os.path.exists(log_file):
        try:
            os.remove(log_file)
            print(f"Arquivo de log {log_file} removido com sucesso.")
        except Exception as e:
            print(f"Erro ao remover arquivo de log {log_file}: {e}")

    # Remove todos os arquivos de log do Scrapy
    scrapy_logs = glob.glob("*.log")
    for log_file in scrapy_logs:
        try:
            os.remove(log_file)
            print(f"Arquivo de log {log_file} removido com sucesso.")
        except Exception as e:
            print(f"Erro ao remover arquivo de log {log_file}: {e}")

    # Remove todos os arquivos de log do Scrapy na pasta scrapy_implementation
    scrapy_dir = os.path.join(
        os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
        "scrapers",
        "scrapy_implementation",
    )
    if os.path.exists(scrapy_dir):
        scrapy_logs = glob.glob(os.path.join(scrapy_dir, "*.log"))
        for log_file in scrapy_logs:
            try:
                os.remove(log_file)
                print(f"Arquivo de log {log_file} removido com sucesso.")
            except Exception as e:
                print(f"Erro ao remover arquivo de log {log_file}: {e}")


def get_log_manager() -> LogManager:
    """
    Obtém a instância única do gerenciador de logs.

    Returns:
        LogManager: Instância do gerenciador de logs
    """
    global _instance
    if _instance is None:
        _instance = LogManager()
        # Remover o arquivo de log existente
        remove_log_file()
    return _instance
