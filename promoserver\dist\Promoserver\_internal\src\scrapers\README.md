# PromoScraper - Implementação Scrapy para o Promobell

Este módulo implementa scrapers baseados em Scrapy puro para o Promobell, visando melhorar a performance, a escalabilidade e a resistência a mecanismos anti-bot.

## Estrutura do Projeto

```
scrapers/
├── scrapy.cfg                  # Arquivo de configuração do Scrapy
├── items.py                    # Definição dos itens
├── middlewares.py              # Middlewares personalizados
├── pipelines.py                # Pipelines de processamento
├── runner.py                   # Módulo para executar spiders programaticamente
└── spiders/                    # Spiders
    ├── __init__.py
    ├── settings.py             # Configurações do Scrapy
    ├── mercadolivre_spider.py  # Spider do Mercado Livre
    ├── magalu_spider.py        # Spider do Magalu
    └── amazon_spider.py        # Spider da Amazon
```

## Características

- **Spiders Otimizados**: Implementação focada em performance e resistência a bloqueios
- **Middlewares Personalizados**: Rotação de User Agents, delays adaptativos e detecção de CAPTCHAs
- **Pipelines de Processamento**: Limpeza de dados e processamento de imagens
- **Execução Programática**: API para executar spiders a partir de código Python
- **Compatibilidade com PromoHunter**: Integração com o sistema existente

## Como Usar

### Via Python

```python
import asyncio
from src.scrapers.runner import scrape_mercadolivre, scrape_magalu, scrape_amazon

async def main():
    # Raspar produtos do Mercado Livre
    ml_products = await scrape_mercadolivre(
        url="https://www.mercadolivre.com.br/ofertas",
        max_pages=2
    )
    print(f"Produtos do Mercado Livre: {len(ml_products)}")

    # Raspar produtos do Magalu
    magalu_products = await scrape_magalu(
        url="https://www.magazineluiza.com.br/selecao/ofertasdodia/",
        max_pages=2
    )
    print(f"Produtos do Magalu: {len(magalu_products)}")

    # Raspar produtos da Amazon
    amazon_products = await scrape_amazon(
        url="https://www.amazon.com.br/deals",
        max_pages=2
    )
    print(f"Produtos da Amazon: {len(amazon_products)}")

# Executar a função assíncrona
asyncio.run(main())
```

### Via Linha de Comando

```bash
# A partir do diretório raiz do projeto
cd promoserver/src/scrapers

# Raspar produtos do Mercado Livre a partir de uma URL específica
python runner.py --spider mercadolivre --url "https://www.mercadolivre.com.br/ofertas" --output produtos_ml.json

# Raspar produtos do Magalu a partir de uma URL específica
python runner.py --spider magalu --url "https://www.magazineluiza.com.br/selecao/ofertasdodia/" --output produtos_magalu.json

# Raspar produtos da Amazon a partir de uma URL específica
python runner.py --spider amazon --url "https://www.amazon.com.br/deals" --output produtos_amazon.json
```

### Via Scrapy CLI

```bash
# A partir do diretório raiz do projeto
cd promoserver/src/scrapers

# Executar o spider do Mercado Livre
scrapy crawl mercadolivre -a url="https://www.mercadolivre.com.br/ofertas" -o produtos_ml.json

# Executar o spider do Magalu
scrapy crawl magalu -a url="https://www.magazineluiza.com.br/selecao/ofertasdodia/" -o produtos_magalu.json

# Executar o spider da Amazon
scrapy crawl amazon -a url="https://www.amazon.com.br/deals" -o produtos_amazon.json
```

### Testando os Scrapers

```bash
# A partir do diretório raiz do projeto
cd promoserver/src/scrapers

# Executar o script de teste
python test_scrapers.py
```

## Integração com o Promobell

Este módulo foi projetado para ser integrado ao Promobell, substituindo a implementação anterior baseada em WebDriver e BrowserSimulator. A integração é feita através do `runner.py`, que fornece uma API assíncrona para executar os spiders a partir de código Python.

## Diferenças em Relação à Implementação Anterior

1. **Performance**: Scrapy puro é significativamente mais rápido que WebDriver/Selenium.
2. **Escalabilidade**: Scrapy é projetado para raspagem em larga escala, com suporte a concorrência e paralelismo.
3. **Manutenção**: Código mais limpo e modular, facilitando a manutenção e extensão.
4. **Resistência a Bloqueios**: Implementação de middlewares para rotação de User Agents, delays adaptativos e detecção de CAPTCHAs.
5. **Compatibilidade**: Mantém a mesma interface de API, facilitando a integração com o sistema existente.

## Campos de Dados

Os scrapers extraem os seguintes campos de dados:

- **platform**: Nome da plataforma (Mercado Livre, Magalu, Amazon)
- **product_id**: ID do produto na plataforma
- **url_produto**: URL do produto (limpa, sem parâmetros de tracking)
- **url_afiliado**: Deixado em branco conforme solicitado
- **title**: Título do produto
- **description**: Deixado em branco conforme solicitado
- **price**: Preço atual do produto
- **old_price**: Preço anterior do produto (se disponível)
- **image_url**: URL da imagem do produto
- **installments**: Informações de parcelamento
- **coupon_info**: Informações de cupom (se disponível)
- **shipping**: Informações de frete
- **timestamp**: Data e hora da extração
- **cupom**: Informações de cupom (campo adicional para compatibilidade com PromoHunter)

## Manutenção e Atualização

Os seletores CSS e XPath utilizados pelos scrapers podem precisar ser atualizados periodicamente, à medida que os sites mudam. Para facilitar a manutenção, os seletores principais estão centralizados no arquivo `spiders/settings.py`.
