"""
Módulo de endpoints de produtos para a API do Promoserver.
Contém funções relacionadas aos endpoints de produtos.
"""

import logging
from typing import List, Optional
from fastapi import APIRouter, Depends, status, BackgroundTasks, Query # Adicionado Query

from components.auth import verify_supabase_session
from components.models import ProductGetResponse, ProductSaveInput
from components.exceptions import ServiceUnavailableException, handle_exception
from src.models.product_supabase_get import ProdutoSupabaseGet
from src.models.product_supabase import ProdutoSupabase
from src.services.product_save_queue import ProductSaveQueue

log = logging.getLogger("api.products")

# Roteador para endpoints de produtos
router = APIRouter(prefix="/products", tags=["products"])

# Referências aos serviços (serão definidas na inicialização)
product_service = None
supabase_service = None
product_db_service = None
product_save_queue = ProductSaveQueue()


def initialize(p_service, s_service, db_service=None):
    """Inicializa o módulo com as instâncias dos serviços."""
    global product_service, supabase_service, product_db_service
    product_service = p_service
    supabase_service = s_service
    product_db_service = db_service

    log.info(
        "Módulo de produtos inicializado com serviços: "
        + f"product_service={type(p_service).__name__}, "
        + f"supabase_service={type(s_service).__name__}, "
        + f"product_db_service={type(db_service).__name__ if db_service else None}"
    )


@router.get(
    "",
    response_model=List[ProductGetResponse],
    dependencies=[Depends(verify_supabase_session)],
)
async def get_products_api(
    user_info: dict = Depends(verify_supabase_session),
    page: Optional[int] = Query(1, ge=1, description="Número da página"),
    limit: Optional[int] = Query(100, ge=1, le=500, description="Número de itens por página (máx 500)") # Limite da API
):
    """
    Retorna produtos com paginação.

    Args:
        user_info: Informações do usuário autenticado

    Returns:
        List[ProductGetResponse]: Lista de produtos
    """
    log.info(f"Usuário {user_info.get('email')} requisitando GET /products?page={page}&limit={limit}")
    if not supabase_service:
        raise ServiceUnavailableException("Serviço de Banco de Dados")
    try:
        # Usar o método get_all_products com os parâmetros de paginação
        products_db_list: List[ProdutoSupabaseGet] = (
            await supabase_service.get_all_products(page=page, limit=limit)
        )

        response_list = []
        for index, p_obj in enumerate(products_db_list):
            try:
                response_item = ProductGetResponse.from_supabase_get(p_obj)
                response_list.append(response_item)
            except Exception as conversion_error:
                log.error(f"Erro ao converter produto no índice {index}: {p_obj}")
                log.error(f"Erro de conversão: {conversion_error}")
                continue

        log.info(
            f"Retornando {len(response_list)} produtos para o usuário {user_info.get('email')}"
        )
        return response_list

    except Exception as e:
        log.exception(
            f"Erro ao buscar produtos do banco de dados para o usuário {user_info.get('email')}"
        )
        raise handle_exception(e, "Erro ao processar produtos")


@router.post(
    "",
    status_code=status.HTTP_202_ACCEPTED,
    dependencies=[Depends(verify_supabase_session)],
)
async def save_product_api(
    product_input: ProductSaveInput,
    background_tasks: BackgroundTasks,
    user_info: dict = Depends(verify_supabase_session),
):
    """
    Salva um novo produto usando uma fila para evitar problemas de concorrência.

    Args:
        product_input: Dados do produto
        background_tasks: Tarefas em segundo plano do FastAPI
        user_info: Informações do usuário autenticado

    Returns:
        ApiResponse[ProductGetResponse]: Resposta padronizada indicando que o produto foi enfileirado
    """
    from components.response_models import ApiResponse
    from components.exceptions import format_exception_response

    log.info(
        f"Usuário {user_info.get('email')} tentando salvar produto: {product_input.titulo}"
    )
    if not supabase_service:
        return ApiResponse.error_response(
            message="Serviço de Banco de Dados não disponível",
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
        )
    try:
        # Verificar se temos o serviço de banco de dados de produtos
        if product_db_service:
            log.info(
                "Usando ProductDbService com fila para salvar produto com upload de imagem para Digital Ocean"
            )
            try:
                # Converter o modelo de entrada para um dicionário
                product_dict = product_input.to_dict()
                log.debug(f"Dados do produto preparados: {product_dict}")

                # Criar uma função de callback para processar o resultado
                def save_callback(
                    saved_product: Optional[ProdutoSupabaseGet],
                    error: Optional[Exception],
                ):
                    if saved_product:
                        log.info(
                            f"Produto '{saved_product.titulo}' salvo pelo usuário {user_info.get('email')} "
                            + f"ID: {saved_product.id}, Imagem: {saved_product.url_imagem}, "
                            + f"Image ID: {saved_product.image_id}"
                        )
                    elif error:
                        log.error(f"Erro ao salvar produto na fila: {error}")
                        # Aqui poderíamos implementar um mecanismo para notificar o frontend sobre o erro
                        # Por exemplo, através de WebSockets ou um endpoint de status

                # Adicionar o produto à fila de salvamento
                background_tasks.add_task(
                    product_save_queue.add_to_queue,
                    product_dict,
                    product_db_service.save_product,
                    save_callback,
                )

                # Retornar resposta imediata indicando que o produto foi enfileirado
                return ApiResponse.success_response(
                    data=None,
                    message="Produto enfileirado para salvamento",
                    status_code=status.HTTP_202_ACCEPTED,
                )
            except Exception as e:
                log.exception(f"Erro ao enfileirar produto: {e}")
                return ApiResponse.error_response(
                    message=f"Erro ao enfileirar produto: {str(e)}",
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )
        else:
            # Fluxo antigo usando diretamente o supabase_service
            log.info(
                "Usando SupabaseService diretamente para salvar produto (sem upload de imagem para Digital Ocean)"
            )
            try:
                produto_obj = product_input.to_supabase_model()
                log.debug(f"Dados do produto preparados para Supabase: {produto_obj}")
            except Exception as model_e:
                log.error(
                    f"Erro ao converter modelo de entrada para modelo Supabase: {model_e}",
                    exc_info=True,
                )
                return format_exception_response(
                    model_e, "Erro ao preparar dados do produto"
                )

            response = await supabase_service.inserir_produto(produto_obj)
            if response and hasattr(response, "data") and response.data:
                saved_data_dict = response.data[0]
                log.info(
                    f"Produto '{saved_data_dict.get('titulo')}' salvo pelo usuário {user_info.get('email')} ID: {saved_data_dict.get('id')}"
                )
                try:
                    saved_product_get = ProdutoSupabaseGet.model_validate(
                        saved_data_dict
                    )
                    response_model = ProductGetResponse.from_supabase_get(
                        saved_product_get
                    )
                    return ApiResponse.success_response(
                        data=response_model,
                        message="Produto salvo com sucesso",
                        status_code=status.HTTP_201_CREATED,
                    )
                except Exception as conv_e:
                    log.error(
                        f"Erro ao converter produto salvo para modelo de resposta: {conv_e} - Dados: {saved_data_dict}",
                        exc_info=True,
                    )
                    # Cria uma resposta simplificada
                    simple_response = ProductGetResponse(
                        id=str(saved_data_dict.get("id", "")),  # Converter ID para string
                        plataforma=saved_data_dict.get("plataforma", ""),
                        url_afiliado=saved_data_dict.get("url_afiliado", ""),
                        titulo=saved_data_dict.get(
                            "titulo", "Produto salvo com sucesso"
                        ),
                        descricao="Produto salvo com sucesso, mas houve um erro ao formatar a resposta.",
                    )
                    return ApiResponse.success_response(
                        data=simple_response,
                        message="Produto salvo com sucesso, mas houve um erro ao formatar a resposta",
                        status_code=status.HTTP_201_CREATED,
                    )
            else:
                error_detail = getattr(
                    response, "error", "Erro desconhecido no banco de dados"
                )
                log.error(
                    f"Falha ao salvar produto para usuário {user_info.get('email')}. Resposta DB: {error_detail}. Objeto: {response}"
                )
                return ApiResponse.error_response(
                    message="Falha ao salvar produto no banco de dados",
                    errors=[str(error_detail)],
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )
    except Exception as e:
        log.exception(
            f"Erro ao salvar produto para usuário {user_info.get('email')}: {product_input.titulo}"
        )
        return format_exception_response(e, "Erro interno ao salvar produto")


@router.put(
    "/{product_id}",
    status_code=status.HTTP_200_OK,
    dependencies=[Depends(verify_supabase_session)],
)
async def update_product_api(
    product_id: str,
    product_data: dict,
    user_info: dict = Depends(verify_supabase_session),
):
    """
    Atualiza um produto existente.

    Args:
        product_id: ID do produto a ser atualizado
        product_data: Dados do produto em formato de dicionário
        user_info: Informações do usuário autenticado

    Returns:
        ApiResponse[ProductGetResponse]: Resposta padronizada contendo o produto atualizado
    """
    from components.response_models import ApiResponse
    from components.exceptions import format_exception_response

    log.info(
        f"Usuário {user_info.get('email')} tentando atualizar produto ID: {product_id} - Título: {product_data.get('titulo', 'N/A')}"
    )
    if not supabase_service:
        return ApiResponse.error_response(
            message="Serviço de Banco de Dados não disponível",
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
        )

    try:
        # Verificar se temos o serviço de banco de dados de produtos
        if product_db_service:
            log.info(
                "Usando ProductDbService para atualizar produto com suporte a imagens no Digital Ocean"
            )
            try:
                # Garantir que o campo frete seja booleano
                if "frete" in product_data:
                    log.info(
                        f"Campo frete antes de enviar para o serviço: {product_data['frete']} (tipo: {type(product_data['frete']).__name__})"
                    )
                    product_data["frete"] = bool(product_data["frete"])
                    log.info(
                        f"Campo frete após conversão: {product_data['frete']} (tipo: {type(product_data['frete']).__name__})"
                    )

                # Atualizar o produto usando o serviço de banco de dados de produtos
                updated_product = await product_db_service.update_product(
                    product_id, product_data
                )

                if updated_product:
                    log.info(
                        f"Produto '{updated_product.titulo}' atualizado pelo usuário {user_info.get('email')} "
                        + f"ID: {updated_product.id}, Imagem: {updated_product.url_imagem}, "
                        + f"Image ID: {updated_product.image_id}"
                    )

                    # Converter para o modelo de resposta
                    response_model = ProductGetResponse.from_supabase_get(
                        updated_product
                    )
                    return ApiResponse.success_response(
                        data=response_model,
                        message="Produto atualizado com sucesso",
                        status_code=status.HTTP_200_OK,
                    )
                else:
                    log.error(
                        f"Falha ao atualizar produto ID: {product_id} para usuário {user_info.get('email')}"
                    )
                    # Verificar se o produto existe
                    existing_product = await product_db_service.get_product_by_id(
                        product_id
                    )
                    if not existing_product:
                        return ApiResponse.error_response(
                            message=f"Produto com ID {product_id} não encontrado",
                            status_code=status.HTTP_404_NOT_FOUND,
                        )

                    # Se o produto existe, mas a atualização falhou, pode ser um problema de permissão ou outro erro
                    # Verificar se o campo frete está presente e é do tipo correto
                    if "frete" in product_data:
                        log.info(
                            f"Campo frete presente no produto: {product_data['frete']} (tipo: {type(product_data['frete']).__name__})"
                        )

                    return ApiResponse.error_response(
                        message="Falha ao atualizar produto no banco de dados. Verifique se você tem permissão para editar este produto ou se os dados estão no formato correto.",
                        errors=[f"Dados do produto: {product_data}"],
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    )
            except Exception as e:
                log.exception(f"Erro ao atualizar produto usando ProductDbService: {e}")
                # Verificar se o erro está relacionado ao campo frete
                error_str = str(e).lower()
                if "frete" in error_str or "bool" in error_str or "type" in error_str:
                    log.error(f"Possível erro de tipo no campo frete: {error_str}")
                    if "frete" in product_data:
                        log.error(
                            f"Valor do campo frete: {product_data['frete']} (tipo: {type(product_data['frete']).__name__})"
                        )
                    return ApiResponse.error_response(
                        message="Erro ao atualizar produto: possível problema com o campo frete",
                        errors=[str(e)],
                        status_code=status.HTTP_400_BAD_REQUEST,
                    )
                return format_exception_response(e, "Erro ao atualizar produto")
        else:
            # Fluxo antigo usando diretamente o supabase_service
            log.info(
                "Usando SupabaseService diretamente para atualizar produto (sem suporte a imagens no Digital Ocean)"
            )
            try:
                # Criar um objeto ProdutoSupabase a partir dos dados recebidos
                produto_obj = ProdutoSupabase(
                    plataforma=product_data.get("plataforma", ""),
                    url_afiliado=product_data.get("url_afiliado", ""),
                    url_produto=product_data.get("url_produto", ""),
                    url_imagem=product_data.get("url_imagem", ""),
                    image_id=product_data.get(
                        "image_id", ""
                    ),  # Incluir o campo image_id
                    titulo=product_data.get("titulo", ""),
                    categoria=product_data.get("categoria", ""),
                    subcategoria=product_data.get("subcategoria", ""),
                    descricao=product_data.get("descricao", ""),
                    preco_atual=float(product_data.get("preco_atual", 0.0)),
                    preco_antigo=float(product_data.get("preco_antigo", 0.0)),
                    preco_alternativo=float(product_data.get("preco_alternativo", 0.0)),
                    ativo=bool(product_data.get("ativo", True)),
                    cupom=product_data.get("cupom", ""),
                    menor_preco=bool(product_data.get("menor_preco", False)),
                    indicamos=bool(product_data.get("indicamos", False)),
                    disparar_whatsapp=bool(
                        product_data.get("disparar_whatsapp", False)
                    ),
                    grupo_whatsapp=product_data.get("grupo_whatsapp", ""),
                    frete=bool(product_data.get("frete", False)),
                    isStory=bool(product_data.get("isStory", False)),
                    invalidProduct=bool(product_data.get("invalidProduct", False)),
                )
                log.debug(
                    f"Dados do produto preparados para atualização no Supabase: {produto_obj}"
                )
            except Exception as model_e:
                log.error(
                    f"Erro ao converter dados de entrada para modelo Supabase: {model_e}",
                    exc_info=True,
                )
                return format_exception_response(
                    model_e, "Erro ao preparar dados do produto"
                )

            response = await supabase_service.atualizar_produto(product_id, produto_obj)
            if response and hasattr(response, "data") and response.data:
                saved_data_dict = response.data[0]
                log.info(
                    f"Produto '{saved_data_dict.get('titulo')}' atualizado pelo usuário {user_info.get('email')} ID: {saved_data_dict.get('id')}"
                )
                try:
                    saved_product_get = ProdutoSupabaseGet.model_validate(
                        saved_data_dict
                    )
                    response_model = ProductGetResponse.from_supabase_get(
                        saved_product_get
                    )
                    return ApiResponse.success_response(
                        data=response_model,
                        message="Produto atualizado com sucesso",
                        status_code=status.HTTP_200_OK,
                    )
                except Exception as conv_e:
                    log.error(
                        f"Erro ao converter produto atualizado para modelo de resposta: {conv_e} - Dados: {saved_data_dict}",
                        exc_info=True,
                    )
                    # Cria uma resposta simplificada
                    simple_response = ProductGetResponse(
                        id=str(saved_data_dict.get("id", "")),  # Converter ID para string
                        plataforma=saved_data_dict.get("plataforma", ""),
                        url_afiliado=saved_data_dict.get("url_afiliado", ""),
                        titulo=saved_data_dict.get(
                            "titulo", "Produto atualizado com sucesso"
                        ),
                        descricao="Produto atualizado com sucesso, mas houve um erro ao formatar a resposta.",
                    )
                    return ApiResponse.success_response(
                        data=simple_response,
                        message="Produto atualizado com sucesso, mas houve um erro ao formatar a resposta",
                        status_code=status.HTTP_200_OK,
                    )
            else:
                error_detail = getattr(
                    response, "error", "Erro desconhecido no banco de dados"
                )
                log.error(
                    f"Falha ao atualizar produto ID: {product_id} para usuário {user_info.get('email')}. Resposta DB: {error_detail}. Objeto: {response}"
                )
                return ApiResponse.error_response(
                    message="Falha ao atualizar produto no banco de dados",
                    errors=[str(error_detail)],
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )
    except Exception as e:
        log.exception(
            f"Erro ao atualizar produto ID: {product_id} para usuário {user_info.get('email')}: {product_data.get('titulo', 'N/A')}"
        )
        return format_exception_response(e, "Erro interno ao atualizar produto")


@router.delete(
    "/{product_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    dependencies=[Depends(verify_supabase_session)],
)
async def delete_product_api(
    product_id: str, user_info: dict = Depends(verify_supabase_session)
):
    """
    Exclui um produto.

    Args:
        product_id: ID do produto
        user_info: Informações do usuário autenticado
    """
    log.info(
        f"Usuário {user_info.get('email')} tentando excluir produto ID: {product_id}"
    )
    if not supabase_service:
        raise ServiceUnavailableException("Serviço de Banco de Dados")
    try:
        # Verificar se temos o serviço de banco de dados de produtos
        if product_db_service:
            log.info(
                "Usando ProductDbService para excluir produto e sua imagem no Digital Ocean"
            )
            try:
                # Excluir o produto usando o serviço de banco de dados de produtos
                success = await product_db_service.delete_product(product_id)

                if not success:
                    log.warning(
                        f"Falha na exclusão do produto {product_id} pelo usuário {user_info.get('email')}."
                    )
                    raise handle_exception(
                        Exception(
                            f"Produto ID {product_id} não encontrado ou falha na exclusão"
                        )
                    )
                log.info(
                    f"Produto ID {product_id} e sua imagem excluídos com sucesso pelo usuário {user_info.get('email')}."
                )
            except Exception as e:
                log.exception(f"Erro ao excluir produto usando ProductDbService: {e}")
                raise handle_exception(e, "Erro ao excluir produto")
        else:
            # Fluxo antigo usando diretamente o supabase_service
            log.info(
                "Usando SupabaseService diretamente para excluir produto (sem excluir imagem do Digital Ocean)"
            )
            success = await supabase_service.excluir_produto(product_id)
            if not success:
                log.warning(
                    f"Falha na exclusão do produto {product_id} pelo usuário {user_info.get('email')}."
                )
                raise handle_exception(
                    Exception(
                        f"Produto ID {product_id} não encontrado ou falha na exclusão"
                    )
                )
            log.info(
                f"Produto ID {product_id} excluído com sucesso pelo usuário {user_info.get('email')}."
            )
    except Exception as e:
        log.exception(
            f"Erro ao excluir produto {product_id} pelo usuário {user_info.get('email')}"
        )
        raise handle_exception(e, "Erro interno ao excluir produto")
