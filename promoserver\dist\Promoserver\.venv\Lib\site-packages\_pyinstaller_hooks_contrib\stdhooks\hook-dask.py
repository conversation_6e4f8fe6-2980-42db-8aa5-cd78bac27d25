#-----------------------------------------------------------------------------
# Copyright (c) 2005-2020, PyInstaller Development Team.
#
# This file is distributed under the terms of the GNU General Public
# License (version 2.0 or later).
#
# The full license is available in LICENSE, distributed with
# this software.
#
# SPDX-License-Identifier: GPL-2.0-or-later
#-----------------------------------------------------------------------------

from PyInstaller.utils.hooks import collect_data_files

# Collect data files:
#  - dask.yaml
#  - dask-schema.yaml
#  - widgets/templates/*.html.j2 (but avoid collecting files from `widgets/tests/templates`!)
datas = collect_data_files('dask', includes=['*.yml', '*.yaml', 'widgets/templates/*.html.j2'])
