import 'package:flutter/material.dart';

import '../../controllers/register_product_controller.dart';

class UrlInputSection extends StatelessWidget {
  final RegisterProductController controller;

  const UrlInputSection({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        return Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: TextFormField(
                controller: controller.urlController,
                onFieldSubmitted:
                    (_) => controller.fetchProductDetails(),
                decoration: InputDecoration(
                  labelText: 'Link do Produto',
                  prefixIcon: const Icon(Icons.search_outlined),
                  hintText: 'Cole a URL aqui...',
                  errorText: controller.fetchError,
                  border: const OutlineInputBorder(),
                  suffixIcon:
                      controller.urlController.text.isNotEmpty
                          ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              controller.clearForm();
                            },
                          )
                          : null,
                ),
                enabled: !controller.isFetching,
                validator:
                    (v) =>
                        (v == null || v.isEmpty)
                            ? 'URL obrigatória'
                            : null,
              ),
            ),
            const SizedBox(width: 16),
            SizedBox(
              height: 42,
              child: ElevatedButton(
                onPressed:
                    controller.isFetching
                        ? null
                        : () {
                          controller.fetchProductDetails();
                        },
                child:
                    controller.isFetching
                        ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                          ),
                        )
                        : const Text('Buscar'),
              ),
            ),
          ],
        );
      },
    );
  }
}
