class Product:
    def __init__(
        self,
        platform,
        product_id,
        url_afiliado,
        url_produto,
        title,
        description,
        price,
        old_price,
        image_url,
        category,
        subcategory,
        installments=None,
        availability=None,
        coupon_info=None,
        shipping=None,
    ):
        self.platform = platform
        self.product_id = product_id
        self.url_afiliado = url_afiliado
        self.url_produto = url_produto
        self.title = title
        self.description = description
        self.price = price
        self.old_price = old_price
        self.image_url = image_url
        self.category = category
        self.subcategory = subcategory
        self.installments = installments
        self.availability = availability
        self.coupon_info = coupon_info
        self.shipping = shipping

    def to_dict(self):
        return {
            "platform": self.platform,
            "product_id": self.product_id,
            "url_afiliado": self.url_afiliado,
            "url_produto": self.url_produto,
            "title": self.title,
            "description": self.description,
            "price": self.price,
            "old_price": self.old_price,
            "image_url": self.image_url,
            "installments": self.installments,
            "availability": self.availability,
            "coupon_info": self.coupon_info,
            "category": self.category,
            "subcategory": self.subcategory,
            "shipping": self.shipping,
        }
