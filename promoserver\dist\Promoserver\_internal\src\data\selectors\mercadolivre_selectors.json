{"store_id": "mercadolivre", "store_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectors": {"product": [{"id": 1, "selector": "li.ui-search-layout__item", "description": "Container principal do produto", "active": false}, {"id": 2, "selector": "div.ui-search-result", "description": "Container principal do produto (alternativo)", "active": false}, {"id": 3, "selector": "div.promotion-item", "description": "Container de produto promocional", "active": false}, {"id": 4, "selector": "div.andes-card.poly-card--grid-card", "description": "Container de produto em grid", "active": true}, {"id": 1, "selector": "li.ui-search-layout__item", "description": "Container principal do produto", "active": false}, {"id": 2, "selector": "div.ui-search-result", "description": "Container principal do produto (alternativo)", "active": false}, {"id": 3, "selector": "div.promotion-item", "description": "Container de produto promocional", "active": false}, {"id": 50, "selector": ".andes-card", "description": "Seletor encontrado com 55 elementos", "active": false}, {"id": 59, "selector": ".andes-card.switch-filter.andes-card--flat.andes-card--padding-16", "description": "Novo seletor para product (contexto pai)", "active": false}, {"id": 60, "selector": ".andes-card--flat", "description": "Novo seletor para product (contexto pai)", "active": false}, {"id": 61, "selector": ".andes-card--padding-16", "description": "Novo seletor para product (contexto pai)", "active": false}, {"id": 62, "selector": ".andes-card.poly-card.poly-card--grid-card.andes-card--flat.andes-card--padding-0.andes-card--animated", "description": "Novo seletor para product (contexto pai)", "active": false}, {"id": 63, "selector": ".poly-card", "description": "Novo seletor para product (contexto pai)", "active": false}, {"id": 64, "selector": ".poly-card--grid-card", "description": "Novo seletor para product (contexto pai)", "active": false}, {"id": 65, "selector": ".andes-card--padding-0", "description": "Novo seletor para product (contexto pai)", "active": false}, {"id": 66, "selector": ".andes-card--animated", "description": "Novo seletor para product (contexto pai)", "active": false}, {"id": 67, "selector": ".poly-card__portada", "description": "Novo seletor para product (contexto pai)", "active": false}, {"id": 69, "selector": ".poly-component__bookmark", "description": "Novo seletor para product (contexto pai)", "active": false}, {"id": 70, "selector": ".poly-component__picture", "description": "Novo seletor para product (contexto pai)", "active": false}, {"id": 72, "selector": ".poly-card__content", "description": "Novo seletor para product (contexto pai)", "active": false}, {"id": 73, "selector": ".poly-component__highlight", "description": "Novo seletor para product (contexto pai)", "active": false}, {"id": 74, "selector": ".poly-component__title", "description": "Novo seletor para product (contexto pai)", "active": false}, {"id": 76, "selector": ".poly-component__seller", "description": "Novo seletor para product (contexto pai)", "active": false}, {"id": 77, "selector": ".poly-component__reviews", "description": "Novo seletor para product (contexto pai)", "active": false}, {"id": 78, "selector": ".poly-component__price", "description": "Novo seletor para product (contexto pai)", "active": false}, {"id": 82, "selector": ".poly-component__shipping", "description": "Novo seletor para product (contexto pai)", "active": false}, {"id": 85, "selector": ".poly-component__variations-text", "description": "Novo seletor para product (contexto pai)", "active": false}, {"id": 86, "selector": ".poly-component__brand", "description": "Novo seletor para product (contexto pai)", "active": false}, {"id": 89, "selector": ".poly-card__footer", "description": "Novo seletor para product (contexto pai)", "active": false}, {"id": 90, "selector": ".poly-component__ads-promotions", "description": "Novo seletor para product (contexto pai)", "active": false}, {"id": 91, "selector": ".poly-component__picture.lazy-loadable", "description": "Novo seletor para product (contexto pai)", "active": false}, {"id": 99, "selector": ".poly-component__rebates", "description": "Novo seletor para product (contexto pai)", "active": false}, {"id": 101, "selector": ".poly-component__trade-in", "description": "Novo seletor para product (contexto pai)", "active": false}, {"id": 4, "selector": "div.andes-card.poly-card--grid-card", "description": "Container de produto em grid", "active": false}], "title": [{"id": 5, "selector": "h2.ui-search-item__title", "description": "Título do produto na listagem", "active": false}, {"id": 6, "selector": "h2.promotion-item__title", "description": "Título do produto promocional", "active": false}, {"id": 7, "selector": "span.ui-search-item__title", "description": "Título do produto (alternativo)", "active": false}, {"id": 8, "selector": "a.poly-component__title", "description": "Título do produto, dentro de um link.", "active": true}, {"id": 30, "selector": "h1.ui-pdp-title", "description": "Título do produto em componente pdp", "active": false}, {"id": 35, "selector": "head > title", "description": "<PERSON><PERSON><PERSON><PERSON>gin<PERSON>", "active": false}, {"id": 5, "selector": "h2.ui-search-item__title", "description": "Título do produto na listagem", "active": false}, {"id": 6, "selector": "h2.promotion-item__title", "description": "Título do produto promocional", "active": false}, {"id": 7, "selector": "span.ui-search-item__title", "description": "Título do produto (alternativo)", "active": false}, {"id": 75, "selector": ".poly-component__title", "description": "Título do produto.", "active": false}, {"id": 8, "selector": "a.poly-component__title", "description": "Título do produto em componente poly", "active": false}], "price": [{"id": 9, "selector": "span.price-tag-amount", "description": "Preço do produto", "active": false}, {"id": 10, "selector": "span.ui-search-price__part", "description": "Preço na busca", "active": false}, {"id": 11, "selector": "span.promotion-item__price", "description": "Preço em promoção", "active": false}, {"id": 12, "selector": "div.poly-price__current .andes-money-amount__fraction", "description": "Preço atual (parte inteira)", "active": false}, {"id": 13, "selector": "div.poly-price__current .andes-money-amount__cents", "description": "Preço atual (centavos)", "active": false}, {"id": 36, "selector": "span.andes-money-amount__fraction", "description": "Preço atual do produto (parte inteira).", "active": false}, {"id": 37, "selector": ".andes-money-amount__fraction", "description": "Parte inteira do preço atual do produto.", "active": false}, {"id": 9, "selector": "span.price-tag-amount", "description": "Preço do produto", "active": false}, {"id": 79, "selector": ".poly-component__price", "description": "Novo seletor para price (contexto pai)", "active": false}, {"id": 81, "selector": ".poly-price__current", "description": "Novo seletor para price (contexto pai)", "active": false}, {"id": 87, "selector": ".poly-price__disc_label", "description": "Novo seletor para price (contexto pai)", "active": true}], "old_price": [{"id": 14, "selector": ".andes-money-amount--previous .andes-money-amount__fraction", "description": "Preço anterior (parte inteira)", "active": false}, {"id": 15, "selector": ".andes-money-amount--previous .andes-money-amount__cents", "description": "Preço anterior (centavos)", "active": false}, {"id": 16, "selector": "span.price-tag-amount-previous", "description": "Preço anterior (alternativo)", "active": true}, {"id": 51, "selector": "s.andes-money-amount--previous", "description": "Preço antigo/original do produto (riscado).", "active": false}, {"id": 52, "selector": "s[class*='andes-money-amount--previous']", "description": "Preço antigo/original do produto (riscado) - alternativa.", "active": false}, {"id": 80, "selector": ".andes-money-amount.andes-money-amount--previous.andes-money-amount--cents-comma", "description": "Novo seletor para old_price (contexto pai)", "active": false}, {"id": 93, "selector": "s.andes-money-amount--previous .andes-money-amount__fraction", "description": "Parte inteira do preço antigo do produto.", "active": false}, {"id": 94, "selector": "s[class*='andes-money-amount--previous'] span[class*='andes-money-amount__fraction']", "description": "Preço antigo riscado", "active": false}, {"id": 102, "selector": "s[aria-roledescription='<PERSON><PERSON>']", "description": "Preço antigo usando o atributo aria-roledescription.", "active": false}], "link": [{"id": 17, "selector": "a.ui-search-link", "description": "Link do produto na busca", "active": false}, {"id": 18, "selector": "a.promotion-item__link", "description": "Link do produto em promoção", "active": false}, {"id": 19, "selector": "a.poly-component__title", "description": "Link para a página do produto.", "active": false}, {"id": 38, "selector": ".nav-menu-item-link", "description": "Novo seletor para link", "active": true}], "image": [{"id": 31, "selector": ".ui-pdp-gallery__figure img", "description": "Imagem principal do produto", "attribute": "src", "active": false}, {"id": 20, "selector": "img.ui-pdp-image", "description": "Imagem do produto (alternativo)", "attribute": "src", "active": false}, {"id": 21, "selector": "img.promotion-item__img", "description": "Imagem do produto em promoção", "attribute": "src", "active": false}, {"id": 22, "selector": "img[data-src]", "description": "Imagem com carregamento lazy", "attribute": "data-src", "active": false}, {"id": 23, "selector": "img.poly-component__picture", "description": "Imagem principal do produto.", "attribute": "src", "active": false}, {"id": 46, "selector": ".poly-card__portada img", "description": "Imagem do produto dentro do container da imagem.", "attribute": "src", "active": false}, {"id": 47, "selector": ".poly-card__portada > img[alt='cover']", "description": "Imagem de capa com alt='cover'", "attribute": "src", "active": false}, {"id": 48, "selector": "img.poly-component__picture[src*='http2.mlstatic.com']", "description": "Imagem externa do Mercado Livre", "attribute": "src", "active": false}, {"id": 49, "selector": ".poly-card__portada img[src*='http2.mlstatic.com']", "description": "Imagem externa na capa do card", "attribute": "src", "active": false}, {"id": 53, "selector": "img[data-src*='http2.mlstatic.com']", "description": "Imagem do produto com URL contendo 'http2.mlstatic.com'.", "active": false}, {"id": 56, "selector": ".andes-carousel-snapped__slide.andes-carousel-snapped__slide--active", "description": "Novo seletor para image (contexto pai)", "active": false}, {"id": 57, "selector": ".andes-carousel-snapped__slide.andes-carousel-snapped__slide--next", "description": "Novo seletor para image (contexto pai)", "active": false}, {"id": 58, "selector": ".andes-carousel-snapped__slide", "description": "Novo seletor para image (contexto pai)", "active": false}, {"id": 68, "selector": ".poly-card__portada", "description": "Novo seletor para image (contexto pai)", "active": false}, {"id": 71, "selector": ".poly-component__picture", "description": "Novo seletor para image (contexto pai)", "active": false}, {"id": 92, "selector": ".poly-component__picture.lazy-loadable", "description": "Novo seletor para image (contexto pai)", "active": false}, {"id": 95, "selector": "img[src*='http2.mlstatic.com']", "description": "Imagem do produto com URL contendo 'http2.mlstatic.com'.", "active": true}], "installments": [{"id": 24, "selector": "span.poly-price__installments", "description": "Informações de parcelamento poly", "active": false}, {"id": 25, "selector": "span.ui-search-installments", "description": "Informações de parcelamento na busca", "active": false}, {"id": 26, "selector": "span.promotion-item__installments", "description": "Informações de parcelamento em promoção", "active": false}, {"id": 40, "selector": ".poly-price__installments", "description": "Informações de parcelamento.", "active": true}], "coupon": [{"id": 27, "selector": ".andes-money-amount__discount", "description": "Desconto em valor monetário", "active": false}, {"id": 28, "selector": ".ui-pdp-price__discount", "description": "Desconto na página do produto", "active": false}, {"id": 29, "selector": "span.promotion-item__discount", "description": "Desconto em item promocional", "active": false}, {"id": 39, "selector": ".andes-money-amount__discount.poly-price__disc_label", "description": "Novo seletor para coupon", "active": false}, {"id": 41, "selector": ".poly-component__coupons", "description": "Novo seletor para coupon", "active": false}, {"id": 42, "selector": ".poly-coupons__wrapper", "description": "Novo seletor para coupon", "active": false}, {"id": 43, "selector": ".poly-coupons__pill", "description": "Cupom de desconto.", "active": false}, {"id": 44, "selector": ".poly-coupons__icon", "description": "Novo seletor para coupon", "active": false}, {"id": 45, "selector": "#poly_coupon", "description": "Novo seletor para coupon", "active": false}, {"id": 54, "selector": ".poly-component__coupons .poly-coupons__pill", "description": "Cupom de desconto.", "active": false}, {"id": 88, "selector": ".poly-price__disc_label", "description": "Novo seletor para coupon (contexto pai)", "active": false}, {"id": 96, "selector": ".poly-component__coupons span", "description": "Texto do cupom de desconto", "active": false}, {"id": 97, "selector": "div[class='poly-coupons__wrapper'] span[class='poly-coupons__pill']", "description": "Cupom de desconto", "active": false}, {"id": 103, "selector": ".poly-coupons__wrapper span", "description": "Container do cupom de desconto.", "active": true}], "product_url": [{"id": 32, "selector": "a.ui-pdp-link", "description": "URL do produto na página de detalhes", "active": false}, {"id": 33, "selector": "link[rel='canonical']", "description": "URL canônica do produto", "attribute": "href", "active": false}, {"id": 34, "selector": "a.poly-component__title", "description": "URL do produto em componente poly", "attribute": "href", "active": true}], "shipping": [{"id": 55, "selector": ".poly-shipping--next_day", "description": "Informações de frete grátis.", "active": false}, {"id": 83, "selector": ".poly-component__shipping", "description": "Informações de frete.", "active": false}, {"id": 84, "selector": ".poly-shipping__promise-icon--full", "description": "Novo seletor para shipping (contexto pai)", "active": false}, {"id": 98, "selector": "span[class*='poly-shipping--next_day']", "description": "Informações de frete com entrega no próximo dia.", "active": false}, {"id": 100, "selector": ".poly-shipping__promise-icon--full-super", "description": "Novo seletor para shipping (contexto pai)", "active": true}]}}