import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../controllers/tabs_controller.dart';
import '../../models/product_tab.dart';

class ProductTabsHeader extends StatelessWidget {
  final TabsController controller = Modular.get<TabsController>();

  ProductTabsHeader({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return AnimatedBuilder(
      animation: controller,
      builder: (context, _) {
        return Container(
          height: 40,
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerHighest
                .withOpacity(0.5),
            border: Border(
              bottom: BorderSide(color: Colors.grey[400]!, width: 1),
            ),
          ),
          child: Row(
            children: [
              IconButton(
                icon: const Icon(Icons.chevron_left),
                onPressed:
                    () => controller.scrollController.animateTo(
                      controller.scrollController.position.pixels -
                          200,
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    ),
              ),
              Expanded(
                child: ListView.builder(
                  controller: controller.scrollController,
                  scrollDirection: Axis.horizontal,
                  itemCount: controller.tabs.length + 1,
                  itemBuilder: (context, index) {
                    if (index == controller.tabs.length) {
                      return _buildAddTabButton(context);
                    }
                    final tab = controller.tabs[index];
                    final isSelected =
                        index == controller.currentTabIndex;

                    return _buildTab(
                      context,
                      tab: tab,
                      isSelected: isSelected,
                      onTap: () {
                        controller.switchToTab(index);
                        controller.scrollToTab(index);
                      },
                      onClose: () => controller.removeTab(index),
                    );
                  },
                ),
              ),
              IconButton(
                icon: const Icon(Icons.chevron_right),
                onPressed:
                    () => controller.scrollController.animateTo(
                      controller.scrollController.position.pixels +
                          200,
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                    ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTab(
    BuildContext context, {
    required ProductTab tab,
    required bool isSelected,
    required VoidCallback onTap,
    required VoidCallback onClose,
  }) {
    final theme = Theme.of(context);

    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          height: 40,
          constraints: const BoxConstraints(
            minWidth: 120,
            maxWidth: 200,
          ),
          padding: const EdgeInsets.only(left: 12, right: 4),
          margin: const EdgeInsets.only(left: 2, top: 4),
          decoration: BoxDecoration(
            color:
                isSelected
                    ? theme.colorScheme.surface
                    : theme.colorScheme.surfaceContainerHighest,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(8),
              topRight: Radius.circular(8),
            ),
            border: Border.all(
              color:
                  isSelected
                      ? theme.colorScheme.primary
                      : theme.dividerColor,
              width: isSelected ? 1 : 0.5,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (tab.isFetching || tab.isPublishing)
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              else if (tab.statusColor != Colors.transparent)
                Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    color: tab.statusColor,
                    shape: BoxShape.circle,
                  ),
                  child:
                      tab.statusMessage != null
                          ? Tooltip(
                            message: tab.statusMessage!,
                            child: const SizedBox(),
                          )
                          : null,
                )
              else
                Icon(
                  Icons.description_outlined,
                  size: 16,
                  color:
                      isSelected
                          ? theme.colorScheme.primary
                          : theme.colorScheme.onSurfaceVariant,
                ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  tab.title,
                  style: TextStyle(
                    color:
                        isSelected
                            ? theme.colorScheme.primary
                            : theme.colorScheme.onSurfaceVariant,
                    fontWeight:
                        isSelected
                            ? FontWeight.bold
                            : FontWeight.normal,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              IconButton(
                icon: Icon(
                  Icons.close,
                  size: 16,
                  color:
                      isSelected
                          ? theme.colorScheme.primary
                          : theme.colorScheme.onSurfaceVariant,
                ),
                onPressed: onClose,
                visualDensity: VisualDensity.compact,
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(
                  minWidth: 24,
                  minHeight: 24,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAddTabButton(BuildContext context) {
    final theme = Theme.of(context);

    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: controller.addTab,
        child: Container(
          height: 40,
          width: 40,
          margin: const EdgeInsets.only(left: 2, top: 4),
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerHighest,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(8),
              topRight: Radius.circular(8),
            ),
            border: Border.all(color: theme.dividerColor, width: 0.5),
          ),
          child: Tooltip(
            message: 'Adicionar nova aba',
            child: Icon(
              Icons.add,
              color: theme.colorScheme.primary,
              size: 20,
            ),
          ),
        ),
      ),
    );
  }
}
