"""
Classe base para scrapers usando Scrapy
"""

import logging
from abc import ABC
from typing import Dict, Optional, Tuple

# Tenta importar SelectorManager de ambos os locais possíveis
try:
    from src.utils.selector_manager import SelectorManager
except ImportError:
    try:
        from ...utils.selector_manager import SelectorManager
    except ImportError:
        logging.critical(
            "Falha ao importar SelectorManager. BaseScraper não pode funcionar."
        )
        SelectorManager = None  # Define como None se a importação falhar

# Importa as classes necessárias
from .product_categories import ProductCategories
from .ai_content_generator import AIContentGenerator
from .header_manager import HeaderManager # Novo
from .cookie_manager import CookieManager # Novo
from .browser_simulator import BrowserSimulator, selenium_available # Novo
import asyncio # Novo

logger = logging.getLogger(__name__)


class BaseScraper(ABC):
    """
    Classe base abstrata para scrapers de lojas específicas.
    Versão simplificada que usa apenas Scrapy, sem WebDriver, BrowserSimulator, cache ou cookies.
    """

    def __init__(self, store_id: str):
        if not SelectorManager:
            raise ImportError(
                "SelectorManager não pôde ser importado. Não é possível inicializar BaseScraper."
            )
        if not store_id:
            logger.error(
                "BaseScraper inicializado sem store_id! Isso pode causar problemas na busca de seletores."
            )

        self.store_id = store_id
        self._selector_manager = SelectorManager()
        self._product_categories = ProductCategories()  # Carrega categorias no init
        self._header_manager = HeaderManager() # Novo
        self._cookie_manager = CookieManager() # Novo

        self._browser_simulator = None
        if selenium_available:
            self._browser_simulator = BrowserSimulator(self._cookie_manager, self._header_manager)
            logger.info("BrowserSimulator (Selenium) inicializado no BaseScraper.")
        else:
            logger.warning(
                "Selenium não está disponível. BrowserSimulator não será usado pelo BaseScraper."
            )

        # Instancia o AIContentGenerator (verifica disponibilidade internamente)
        logger.info("Inicializando AIContentGenerator...")
        self._ai_generator = AIContentGenerator(self._product_categories)
        logger.info(
            f"AIContentGenerator inicializado. Disponível: {self._ai_generator.available}"
        )
        if not self._ai_generator.available:
            logger.warning(
                "AIContentGenerator não está disponível (chave API ou pacote ausente)."
            )

    def get_selector_manager(self):
        """Retorna a instância do SelectorManager."""
        if not self._selector_manager:
            raise RuntimeError("SelectorManager não foi inicializado corretamente.")
        return self._selector_manager

    # --- Métodos de Geração de Conteúdo (delegados para AIContentGenerator) ---

    async def generate_product_description(self, product_name: str) -> str:
        """Gera descrição do produto usando IA."""
        if not product_name:
            # Usa o fallback definido no gerador
            return (
                self._ai_generator.fallback_description
                if self._ai_generator
                else ""
            )
        return await self._ai_generator.generate_product_description(product_name)

    async def generate_product_category(self, product_name: str) -> Optional[str]:
        """Gera a chave da categoria do produto usando IA."""
        if not product_name or not self._ai_generator:
            return None
        return await self._ai_generator.generate_product_category(product_name)

    async def generate_product_subcategory(
        self, product_name: str, category_key: Optional[str]
    ) -> Optional[str]:
        """Gera o índice da subcategoria do produto usando IA."""
        if not product_name or not category_key or not self._ai_generator:
            return None
        return await self._ai_generator.generate_product_subcategory(
            product_name, category_key
        )

    # # --- Métodos Abstratos (a serem implementados pelas subclasses) ---

    # @abstractmethod
    # def extract_product_id(self, url: str) -> Optional[str]:
    #     """
    #     Extrai o ID único do produto a partir da URL.
    #     Deve ser implementado por cada scraper específico.
    #     """
    #     pass

    # @abstractmethod
    # async def scrape(self, url: str) -> Dict:
    #     """
    #     Método principal de scraping para uma URL de produto específica.
    #     Deve ser implementado por cada scraper específico.
    #     Este método DEVE ser `async` se chamar métodos `async` como os de geração de IA.

    #     Espera-se que retorne um dicionário com os dados extraídos ou um dicionário de erro.
    #     """
    #     pass

    # --- Métodos Utilitários ---

    def _error_response(self, error_message: str, url: str) -> Dict:
        """
        Cria uma resposta de erro padronizada.

        Args:
            error_message: Mensagem de erro
            url: URL que causou o erro

        Returns:
            Dicionário com informações de erro
        """
        logger.error(f"Erro ao processar URL {url}: {error_message}")

        return {
            "platform": self.store_id.capitalize() if self.store_id else "Unknown",
            "product_id": None,
            "url_produto": url,
            "url_afiliado": "",
            "title": "Não disponível",
            "description": f"Erro ao buscar dados: {error_message}",
            "price": "N/A",
            "old_price": "N/A",
            "image_url": None,
            "installments": "N/A",
            "coupon_info": "N/A",
            "category": None,
            "subcategory": None,
            "shipping": "N/A",
            "error": True,
            "error_message": error_message,
        }

    def get_category_name(self, category_key: Optional[str]) -> Optional[str]:
        """Obtém o nome da categoria a partir da chave."""
        return self._product_categories.get_category_name(category_key)

    def get_subcategory_name(
        self, category_key: Optional[str], subcategory_idx: Optional[str]
    ) -> Optional[str]:
        """Obtém o nome da subcategoria a partir da chave e índice."""
        return self._product_categories.get_subcategory_name(
            category_key, subcategory_idx
        )

    def get_category_and_subcategory_indices_from_title(
        self, title: str
    ) -> Tuple[Optional[str], Optional[str]]:
        """Tenta determinar categoria/subcategoria baseado no título usando ProductCategories."""
        return self._product_categories.get_category_and_subcategory_indices(title)

    async def _get_html_with_browser_simulation(self, url: str) -> Optional[str]:
        """
        Obtém o HTML de uma URL usando simulação de navegador (Selenium).
        Este método é async e executa a chamada síncrona do BrowserSimulator em uma thread.
        """
        if not self._browser_simulator:
            logger.warning("BrowserSimulator não está disponível para buscar HTML.")
            return None
        
        logger.info(f"Solicitando HTML com simulação de navegador para: {url}")
        try:
            # Executa o método síncrono get_page_html em uma thread separada
            html_content = await asyncio.to_thread(self._browser_simulator.get_page_html, url)
            if html_content:
                logger.info(f"HTML obtido com sucesso via simulação para: {url}")
            else:
                logger.warning(f"Falha ao obter HTML com simulação para: {url}")
            return html_content
        except Exception as e:
            logger.error(f"Erro ao obter HTML com simulação de navegador para {url}: {e}", exc_info=True)
            return None
