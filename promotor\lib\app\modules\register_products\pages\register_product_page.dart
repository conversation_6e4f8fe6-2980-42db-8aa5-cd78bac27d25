import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:intl/intl.dart';

import '../controllers/register_product_controller.dart';
import 'widgets/product_info_section.dart';
import 'widgets/publish_section.dart';
import 'widgets/url_input_section.dart';
import 'widgets/values_section.dart';

class RegisterProductPage extends StatefulWidget {
  const RegisterProductPage({super.key});

  @override
  State<RegisterProductPage> createState() =>
      _RegisterProductPageState();
}

class _RegisterProductPageState extends State<RegisterProductPage> {
  final controller = Modular.get<RegisterProductController>();
  final _formKey = GlobalKey<FormState>(
    debugLabel: 'registerProductForm',
  );
  late final TextInputFormatter _currencyInputFormatter;

  @override
  void initState() {
    super.initState();
    _currencyInputFormatter = CurrencyInputFormatter(
      controller.currencyFormatter,
    );
  }

  Future<void> _publish() async {
    if (!_formKey.currentState!.validate()) return;

    final success = await controller.publishProduct(
      currentTitle: controller.titleController.text,
      currentDescription: controller.descriptionController.text,
      currentPriceStr: controller.priceController.text,
      oldPriceStr: controller.oldPriceController.text,
      coupon: controller.couponController.text,
    );

    if (mounted && success) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Produto publicado com sucesso!'),
          backgroundColor: Colors.green,
        ),
      );
    } else if (mounted && controller.publishError != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Erro ao publicar: ${controller.publishError}',
          ),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return AnimatedBuilder(
      animation: controller,
      builder: (context, _) {
        return Scaffold(
          appBar: AppBar(title: const Text('Cadastrar Produto')),
          body: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // URL Input Section
                  UrlInputSection(controller: controller),
                  const SizedBox(height: 24),

                  // Product Info Section (if product is being fetched or already fetched)
                  if (controller.scrapedProduct != null ||
                      controller.isFetching)
                    ProductInfoSection(theme: theme),

                  const SizedBox(height: 24),

                  // Values Section (if product is being fetched or already fetched)
                  if (controller.scrapedProduct != null ||
                      controller.isFetching)
                    ValuesSection(
                      theme: theme,
                      currencyFormatter: _currencyInputFormatter,
                    ),

                  const SizedBox(height: 24),

                  // Publish Section (if product is being fetched or already fetched)
                  if (controller.scrapedProduct != null ||
                      controller.isFetching)
                    PublishSection(theme: theme, onPublish: _publish),

                  // Error message
                  if (controller.publishError != null)
                    Padding(
                      padding: const EdgeInsets.only(top: 16.0),
                      child: Text(
                        controller.publishError!,
                        style: TextStyle(
                          color: theme.colorScheme.error,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

// Mantenha a classe CurrencyInputFormatter aqui ou mova para um arquivo separado
class CurrencyInputFormatter extends TextInputFormatter {
  final NumberFormat format;
  CurrencyInputFormatter(this.format);

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    if (newValue.text.isEmpty) {
      return newValue.copyWith(text: '');
    }

    final digitsOnly = newValue.text.replaceAll(RegExp(r'[^\d]'), '');
    if (digitsOnly.isEmpty) {
      return const TextEditingValue(
        text: '',
        selection: TextSelection.collapsed(offset: 0),
      );
    }

    try {
      final number = double.parse(digitsOnly) / 100.0;
      final newString = format.format(number);

      return TextEditingValue(
        text: newString,
        selection: TextSelection.collapsed(offset: newString.length),
      );
    } catch (e) {
      return oldValue;
    }
  }
}
