"""
Módulo de endpoints de scraping para a API do Promoserver.
Contém funções relacionadas aos endpoints de scraping.
"""

import logging
from typing import Dict
from fastapi import APIRouter, Depends

from components.auth import verify_supabase_session
from components.models import ScrapedProductResponse
from components.exceptions import handle_exception

log = logging.getLogger("api.scraping")

# Roteador para endpoints de scraping
router = APIRouter(tags=["scraping"])

# Referência ao ProductService (será definida na inicialização)
product_service = None


def initialize(p_service):
    """Inicializa o módulo com a instância do ProductService."""
    global product_service
    product_service = p_service


@router.get(
    "/scrape",
    dependencies=[Depends(verify_supabase_session)],
)
async def scrape_product_api(
    url: str, user_info: dict = Depends(verify_supabase_session)
):
    """
    Realiza o scraping de um produto.

    Args:
        url: URL do produto
        user_info: Informações do usuário autenticado

    Returns:
        ScrapedProductResponse: Dados do produto raspado
    """
    log.info(f"Usuário {user_info.get('email')} requisitou scraping para URL: {url}")
    if not product_service:
        raise handle_exception(Exception("Serviço de Produto não disponível"))
    try:
        scraped_data_dict = await product_service.fetch_product_details(url)
        if isinstance(scraped_data_dict, Dict):
            log.debug(
                f"Dados raspados para o usuário {user_info.get('email')}: {scraped_data_dict}"
            )
            # (restante do código igual)
            return ScrapedProductResponse(**response_data)
        else:
            log.error(
                f"Scraping para usuário {user_info.get('email')} retornou tipo inválido: {type(scraped_data_dict)}"
            )
            log.exception(Exception("Erro interno durante scraping (formato de dados inválido)"))
            raise handle_exception(
                Exception("Erro interno durante scraping (formato de dados inválido)")
            )
    except Exception as e:
        log.error(f"Falha no scraping para {url} solicitado pelo usuário {user_info.get('email')}: {e}")
        log.exception(e)
        error_response = {
            "platform": "Error",
            "affiliateUrl": url,  # Usar a URL original como URL de afiliado
            "productUrl": None,
            "title": "Falha no Scraping",
            "description": f"Erro: {e}",
            "error": True,
            "productId": None,
            "price": None,
            "oldPrice": None,
            "imageUrl": None,
            "installments": None,
            "couponInfo": None,
            "categoryKey": None,
            "subcategoryIndex": None,
            "category": None,
            "subcategory": None,
            "shipping": None,
        }
        return ScrapedProductResponse(**error_response)
