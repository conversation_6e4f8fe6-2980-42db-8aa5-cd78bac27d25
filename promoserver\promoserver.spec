# -*- mode: python ; coding: utf-8 -*-

import sys
import os
from pathlib import Path

block_cipher = None

# Corrigido: usar o diretório atual ao invés de __file__
base_dir = Path(os.path.abspath("."))

# Arquivos essenciais do .venv
venv_essentials = [
    # Python executáveis
    (str(base_dir / ".venv" / "Scripts" / "python.exe"), ".venv/Scripts"),
    (str(base_dir / ".venv" / "Scripts" / "pythonw.exe"), ".venv/Scripts"),

    # Uvicorn (essencial para o servidor)
    (str(base_dir / ".venv" / "Scripts" / "uvicorn.exe"), ".venv/Scripts"),

    # FastAPI CLI (opcional mas útil)
    (str(base_dir / ".venv" / "Scripts" / "fastapi.exe"), ".venv/Scripts"),

    # Scrapy (para web scraping)
    (str(base_dir / ".venv" / "Scripts" / "scrapy.exe"), ".venv/Scripts"),

    # Pip (para gerenciamento de pacotes se necessário)
    (str(base_dir / ".venv" / "Scripts" / "pip.exe"), ".venv/Scripts"),
    (str(base_dir / ".venv" / "Scripts" / "pip3.exe"), ".venv/Scripts"),
]

# Filtrar apenas arquivos que existem
venv_files = [(src, dst) for src, dst in venv_essentials if Path(src).exists()]

# Incluir assets e arquivos de configuração necessários
datas = [
    (str(base_dir / "assets" / "icon-promoserver-blue.ico"), "assets"),
    (str(base_dir / "src" / "scrapers" / "base" / "llm_config.json"), "src/scrapers/base"),
    (str(base_dir / "api_main.py"), "."),
    # Incluir toda a pasta components
    (str(base_dir / "components"), "components"),
    # Incluir toda a pasta src
    (str(base_dir / "src"), "src"),
    # Adicione outros arquivos/diretórios necessários aqui
]

a = Analysis(
    ['server_control_ui.py'],
    pathex=[str(base_dir)],
    binaries=[],
    datas=datas,
    hiddenimports=[
        'tkinter',
        'pystray',
        'PIL.Image',
        'PIL.ImageTk',
        'src.utils.log_manager',
        # Componentes do API
        'components',
        'components.auth',
        'components.categories',
        'components.config',
        'components.products',
        'components.promohunter',
        'components.scraping',
        'components.selectors',
        'components.selectors_validation',
        'components.models',
        'components.exceptions',
        'components.response_models',
        # Adicione outros imports dinâmicos se necessário
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='Promoserver',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # Oculta o prompt de comando/console
    icon=str(base_dir / "assets" / "icon-promoserver-blue.ico"),
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='Promoserver'
)
