"""
Serviço para operações relacionadas ao PromoHunter usando Scrapy.
"""

import json
import logging
import os
import threading
import time
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime

from src.utils.selector_manager import SelectorManager
from src.services.scrapy_service import ScrapyService
from src.utils.log_manager import get_log_manager

log = logging.getLogger(__name__)

# Obtém o gerenciador de logs universal
log_manager = get_log_manager()


class PromoHunterServiceScrapy:
    """Serviço para operações relacionadas ao PromoHunter usando Scrapy."""

    def __init__(self, selector_manager: Optional[SelectorManager] = None):
        """
        Inicializa o serviço do PromoHunter.

        Args:
            selector_manager: Gerenciador de seletores (opcional)
        """
        # Obtém o gerenciador de logs
        self.log_manager = log_manager
        self.log_manager.info("Inicializando PromoHunterServiceScrapy")

        self.selector_manager = selector_manager or SelectorManager()

        # Inicializar o ScrapyService com log detalhado
        self.scrapy_service = ScrapyService()

        # Configurar diretórios
        self.base_dir = os.path.dirname(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        )
        self.categories_dir = os.path.join(
            self.base_dir, "src", "data", "offer_categories"
        )
        self.output_dir = os.path.join(self.base_dir, "output")

        # Criar diretórios se não existirem
        os.makedirs(self.categories_dir, exist_ok=True)
        os.makedirs(self.output_dir, exist_ok=True)

        # Carregar categorias
        self.categories = {}
        self._load_categories()

        # Variáveis de controle para o scraper
        self._is_running = False
        self.current_status = "Não iniciado"
        self.lock = threading.Lock()
        self.scraper_stats = {
            "start_time": None,
            "end_time": None,
            "total_products": 0,
            "processed_pages": 0,
            "errors": 0,
        }

        # Configurar logging detalhado
        log.setLevel(logging.DEBUG)

        self.log_manager.info("PromoHunterServiceScrapy inicializado com sucesso")

    def _load_categories(self):
        """Carrega as categorias de ofertas dos arquivos JSON."""
        self.categories = {"mercadolivre": [], "magalu": [], "amazon": []}

        # Verificar e criar arquivos de categorias se não existirem
        for store in self.categories.keys():
            file_path = os.path.join(self.categories_dir, f"{store}_categories.json")
            if not os.path.exists(file_path):
                with open(file_path, "w", encoding="utf-8") as f:
                    json.dump([], f, ensure_ascii=False, indent=4)

            # Carregar categorias
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    self.categories[store] = json.load(f)
            except Exception as e:
                log.error(f"Erro ao carregar categorias de {store}: {e}")
                self.categories[store] = []

    async def get_categories(self, store: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Retorna as categorias de ofertas de uma loja.

        Args:
            store: Loja (mercadolivre, magalu, amazon) ou None para todas as lojas

        Returns:
            List[Dict[str, Any]]: Lista de categorias
        """
        if store is None:
            # Se nenhuma loja for especificada, retornar todas as categorias
            all_categories = []
            for store_name, categories in self.categories.items():
                for category in categories:
                    category_copy = category.copy()
                    category_copy["store"] = store_name
                    all_categories.append(category_copy)
            return all_categories
        elif store in self.categories:
            # Retornar categorias da loja especificada
            result = []
            for category in self.categories[store]:
                category_copy = category.copy()
                category_copy["store"] = store
                result.append(category_copy)
            return result
        else:
            # Loja não encontrada
            return []

    async def create_category(self, category: Dict[str, Any]) -> Dict[str, Any]:
        """
        Cria uma nova categoria de ofertas.

        Args:
            category: Dados da categoria

        Returns:
            Dict[str, Any]: Categoria criada
        """
        store = category.get("store")
        if not store or store not in self.categories:
            raise ValueError(f"Loja inválida: {store}")

        # Gerar ID para a nova categoria
        max_id = 0
        for cat in self.categories[store]:
            if cat.get("id", 0) > max_id:
                max_id = cat.get("id", 0)

        new_category = {
            "id": max_id + 1,
            "name": category.get("name"),
            "url_template": category.get("url_template"),
            "max_page": category.get("max_page", 2),
            "active": category.get("active", True),
        }

        # Adicionar à lista de categorias
        self.categories[store].append(new_category)

        # Salvar no arquivo
        file_path = os.path.join(self.categories_dir, f"{store}_categories.json")
        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(self.categories[store], f, ensure_ascii=False, indent=4)

        return new_category

    async def update_category(self, category: Dict[str, Any]) -> Dict[str, Any]:
        """
        Atualiza uma categoria de ofertas.

        Args:
            category: Dados da categoria

        Returns:
            Dict[str, Any]: Categoria atualizada
        """
        store = category.get("store")
        category_id = category.get("id")
        if not store or store not in self.categories:
            raise ValueError(f"Loja inválida: {store}")
        if not category_id:
            raise ValueError("ID da categoria não fornecido")

        # Encontrar a categoria
        for i, cat in enumerate(self.categories[store]):
            if cat.get("id") == category_id:
                # Atualizar campos
                self.categories[store][i].update(
                    {
                        "name": category.get("name", cat.get("name")),
                        "url_template": category.get(
                            "url_template", cat.get("url_template")
                        ),
                        "max_page": category.get("max_page", cat.get("max_page", 2)),
                        "active": category.get("active", cat.get("active", True)),
                    }
                )

                # Salvar no arquivo
                file_path = os.path.join(
                    self.categories_dir, f"{store}_categories.json"
                )
                with open(file_path, "w", encoding="utf-8") as f:
                    json.dump(self.categories[store], f, ensure_ascii=False, indent=4)

                return self.categories[store][i]

        raise ValueError(f"Categoria com ID {category_id} não encontrada")

    async def delete_category(self, store: str, category_id: int) -> bool:
        """
        Remove uma categoria de ofertas.

        Args:
            store: Loja (mercadolivre, magalu, amazon)
            category_id: ID da categoria

        Returns:
            bool: True se a categoria foi removida, False caso contrário
        """
        if store not in self.categories:
            raise ValueError(f"Loja inválida: {store}")

        # Encontrar a categoria
        for i, cat in enumerate(self.categories[store]):
            if cat.get("id") == category_id:
                # Remover da lista
                self.categories[store].pop(i)

                # Salvar no arquivo
                file_path = os.path.join(
                    self.categories_dir, f"{store}_categories.json"
                )
                with open(file_path, "w", encoding="utf-8") as f:
                    json.dump(self.categories[store], f, ensure_ascii=False, indent=4)

                return True

        return False

    async def get_scraper_status(self) -> Dict[str, Any]:
        """
        Retorna o status atual do scraper.

        Returns:
            Dict[str, Any]: Dicionário com o status e estatísticas.
        """
        with self.lock:
            status_data = {
                "is_running": self._is_running,
                "current_status": self.current_status,
                "stats": self.scraper_stats.copy(),  # Retorna uma cópia para evitar modificações externas
                "is_completed": not self._is_running and self.scraper_stats.get("end_time") is not None,
                "has_error": len(self.scraper_stats.get("errors", [])) > 0,
                "message": self.current_status,
                "total_products": self.scraper_stats.get("total_products", 0),
                "processed_categories": self.scraper_stats.get("processed_categories", 0),
                "processed_pages": self.scraper_stats.get("processed_pages", 0),
                "errors": self.scraper_stats.get("errors", [])
            }
            # Adicionar tempo decorrido se estiver rodando
            if self._is_running and self.scraper_stats.get("start_time"):
                start_time = datetime.fromisoformat(self.scraper_stats["start_time"])
                elapsed_time = datetime.now() - start_time
                status_data["elapsed_time_seconds"] = elapsed_time.total_seconds()

            return status_data

    async def stop_scraper(self) -> Dict[str, Any]:
        """
        Para a execução do scraper.

        Returns:
            Dict[str, Any]: Status após parar o scraper
        """
        with self.lock:
            if not self._is_running:
                return {
                    "is_running": False,
                    "is_completed": True,
                    "has_error": False,
                    "message": "O scraper já está parado",
                    "status": "Parado",
                    "total_products": self.scraper_stats.get("total_products", 0),
                    "processed_categories": self.scraper_stats.get("processed_categories", 0),
                    "processed_pages": self.scraper_stats.get("processed_pages", 0),
                    "errors": []
                }

            # Parar o scraper
            self._is_running = False
            self.current_status = "Scraper parado pelo usuário"
            self.scraper_stats["end_time"] = datetime.now().isoformat()

            # Adicionar mensagem de parada manual
            if "errors" not in self.scraper_stats:
                self.scraper_stats["errors"] = []
            self.scraper_stats["errors"].append("Scraper parado manualmente pelo usuário")

            # Registrar no log
            self.log_manager.warning(f"Scraper parado manualmente pelo usuário")
            log.warning(f"Scraper parado manualmente pelo usuário")

            # Forçar a parada de qualquer processo em execução
            try:
                # Tentar parar o serviço Scrapy se estiver em execução
                if hasattr(self.scrapy_service, 'stop_scraper'):
                    self.scrapy_service.stop_scraper()
            except Exception as e:
                log.error(f"Erro ao parar o serviço Scrapy: {e}")

            # Retornar status completo
            return {
                "is_running": False,
                "is_completed": True,
                "has_error": False,
                "status": "Parado pelo usuário",
                "message": "Scraper parado manualmente pelo usuário",
                "total_products": self.scraper_stats.get("total_products", 0),
                "processed_categories": self.scraper_stats.get("processed_categories", 0),
                "processed_pages": self.scraper_stats.get("processed_pages", 0),
                "errors": self.scraper_stats.get("errors", [])
            }

    async def run_scraper(
        self, store: str, category_id: Optional[int] = None, max_pages: int = 2
    ) -> Dict[str, Any]:
        """
        Executa o scraper para uma loja e categoria específicas.

        Args:
            store: Loja (mercadolivre, magalu, amazon)
            category_id: ID da categoria (opcional)
            max_pages: Número máximo de páginas a serem raspadas

        Returns:
            Dict[str, Any]: Estatísticas do scraper
        """
        with self.lock:
            if self._is_running:
                return {
                    "status": "already_running",
                    "message": "O scraper já está em execução",
                }

            self._is_running = True
            self.current_status = f"Iniciando scraper para {store}"
            self.scraper_stats = {
                "start_time": datetime.now().isoformat(),
                "end_time": None,
                "total_products": 0,
                "processed_pages": 0,
                "processed_categories": 0,
                "errors": [],
            }

            # Atualizar status na interface
            self.log_manager.info(f"Status: {self.current_status}")

        try:
            # Verificar se a loja é válida
            if store not in self.categories:
                raise ValueError(f"Loja inválida: {store}")

            # Obter categorias a processar
            categories_to_process = []
            if category_id:
                # Processar apenas a categoria especificada
                for cat in self.categories[store]:
                    if cat.get("id") == category_id and cat.get("active", True):
                        categories_to_process.append(cat)
                        break
                if not categories_to_process:
                    raise ValueError(
                        f"Categoria {category_id} não encontrada ou inativa"
                    )
            else:
                # Processar todas as categorias ativas
                categories_to_process = [
                    cat for cat in self.categories[store] if cat.get("active", True)
                ]

            # Processar cada categoria
            for category in categories_to_process:
                if not self._is_running:
                    break

                self.current_status = f"Processando categoria: {category['name']}"
                self.log_manager.info(f"Processando categoria: {category['name']}")
                log.info(f"Processando categoria: {category['name']}")

                # Limitar o número máximo de páginas
                max_page = min(category.get("max_page", 2), max_pages)

                # Criar arquivo CSV para a categoria
                output_file = os.path.join(
                    self.output_dir, f"{store}_{category['name'].replace(' ', '_')}.csv"
                )

                # Cabeçalho do CSV - removido campo 'url' redundante
                headers = [
                    "plataforma",
                    "url_produto",
                    "url_afiliado",
                    "url_imagem",
                    "titulo",
                    "categoria",
                    "subcategoria",
                    "descricao",
                    "preco_atual",
                    "preco_antigo",
                    "preco_alternativo",
                    "ativo",
                    "cupom",
                    "menor_preco",
                    "indicamos",
                    "disparar_whatsapp",
                    "grupo_whatsapp",
                    "frete",
                    "invalidProduct",
                    "isStory",
                ]

                # Criar arquivo com cabeçalho
                with open(output_file, "w", encoding="utf-8-sig", newline="") as f:
                    f.write(",".join([f'"{h}"' for h in headers]) + "\n")

                # Processar páginas
                for page in range(1, max_page + 1):
                    if not self._is_running:
                        break

                    self.current_status = f"Processando categoria: {category['name']} - Página {page}/{max_page}"
                    self.log_manager.info(f"Processando página {page} de {max_page}")

                    # Atualizar as estatísticas do scraper
                    self.scraper_stats["processed_pages"] = page

                    # Atualizar status na interface
                    self.log_manager.info(f"Status: {self.current_status}")

                    # Substituir {i} na URL pelo número da página
                    url = category["url_template"].format(i=page)

                    try:
                        # Obter produtos da página usando Scrapy
                        self.log_manager.info(f"Iniciando raspagem da URL: {url}")

                        # Atualizar status para mostrar que está raspando
                        self.current_status = f"Raspando produtos da categoria: {category['name']} - Página {page}/{max_page}"

                        # Executar o scraper
                        products = await self.scrapy_service.scrape_url(
                            url, max_pages=1
                        )

                        if products:
                            self.log_manager.info(f"Produtos encontrados: {len(products)}")
                            log.info(f"Produtos encontrados: {len(products)}")

                            # Atualizar as estatísticas do scraper com os produtos encontrados
                            self.scraper_stats["total_products"] += len(products)

                            # Enriquecer produtos com categorias usando AI
                            self.log_manager.info("Enriquecendo produtos com categorias usando AI...")
                            log.info("Enriquecendo produtos com categorias usando AI...")

                            # Atualizar status para mostrar que está enriquecendo os produtos
                            self.current_status = f"Enriquecendo {len(products)} produtos da categoria: {category['name']} - Página {page}/{max_page}"

                            # Chama o método enrich_products para adicionar categorias e subcategorias
                            enriched_products = await self.scrapy_service.enrich_products(products)

                            self.log_manager.info(f"Produtos enriquecidos: {len(enriched_products)}")
                            log.info(f"Produtos enriquecidos: {len(enriched_products)}")

                            # Converter produtos para formato CSV
                            csv_products = []
                            for product in enriched_products:
                                # Log detalhado para debug
                                self.log_manager.debug(f"Produto: {product.get('title')[:30]}...")
                                self.log_manager.debug(f"Categoria: {product.get('category')}")
                                self.log_manager.debug(f"Subcategoria: {product.get('subcategory')}")
                                self.log_manager.debug(f"Categoria ID: {product.get('category_id')}")
                                self.log_manager.debug(f"Subcategoria ID: {product.get('subcategory_id')}")
                                self.log_manager.debug(f"URL da imagem: {product.get('image_url')[:50] if product.get('image_url') else 'Sem imagem'}")

                                # Log detalhado para o logger padrão
                                log.debug(f"Produto: {product.get('title')[:30]}...")
                                log.debug(f"Categoria: {product.get('category')}")
                                log.debug(f"Subcategoria: {product.get('subcategory')}")
                                log.debug(f"Categoria ID: {product.get('category_id')}")
                                log.debug(f"Subcategoria ID: {product.get('subcategory_id')}")
                                log.debug(f"URL da imagem: {product.get('image_url')[:50] if product.get('image_url') else 'Sem imagem'}")

                                # Garantir que categoria e subcategoria estejam presentes
                                categoria = product.get("category", "")
                                subcategoria = product.get("subcategory", "")

                                # Se categoria ou subcategoria estiverem vazias, tentar usar os campos alternativos
                                if not categoria and product.get("categoria"):
                                    categoria = product.get("categoria")
                                    log.info(
                                        f"Usando campo 'categoria' alternativo: {categoria}"
                                    )

                                if not subcategoria and product.get("subcategoria"):
                                    subcategoria = product.get("subcategoria")
                                    log.info(
                                        f"Usando campo 'subcategoria' alternativo: {subcategoria}"
                                    )

                                # Se ainda estiverem vazias, usar valores padrão
                                if not categoria:
                                    categoria = "Outros"
                                    log.warning(
                                        f"Categoria não encontrada para produto: {product.get('title')[:30]}... Usando padrão: {categoria}"
                                    )

                                if not subcategoria:
                                    subcategoria = "Geral"
                                    log.warning(
                                        f"Subcategoria não encontrada para produto: {product.get('title')[:30]}... Usando padrão: {subcategoria}"
                                    )

                                # Extrair cupom do produto
                                cupom = product.get("coupon", "")
                                if not cupom and product.get("coupon_code"):
                                    cupom = product.get("coupon_code")

                                # Verificar se há informação de desconto no preço
                                if not cupom and product.get("old_price") and product.get("price"):
                                    try:
                                        old_price = float(product.get("old_price").replace(".", "").replace(",", "."))
                                        price = float(product.get("price").replace(".", "").replace(",", "."))
                                        if old_price > price:
                                            discount_pct = int(100 - (price * 100 / old_price))
                                            cupom = f"{discount_pct}% OFF"
                                    except (ValueError, TypeError, ZeroDivisionError):
                                        pass

                                csv_product = {
                                    "plataforma": store,
                                    "url_produto": product.get("url", ""),
                                    "url_afiliado": "",  # Deixar em branco conforme solicitado
                                    "url_imagem": product.get("image_url", ""),
                                    "titulo": product.get("title", ""),
                                    "categoria": categoria,
                                    "subcategoria": subcategoria,
                                    "descricao": "",  # Deixar em branco conforme solicitado
                                    "preco_atual": product.get("price", ""),
                                    "preco_antigo": product.get("old_price", ""),
                                    "preco_alternativo": "0",
                                    "ativo": "True",  # Valor booleano correto
                                    "cupom": cupom,
                                    "menor_preco": "False",  # Valor booleano correto
                                    "indicamos": "False",  # Valor booleano correto
                                    "disparar_whatsapp": "False",  # Valor booleano correto
                                    "grupo_whatsapp": "",  # Deixar em branco
                                    "frete": "False",  # Valor booleano correto
                                    "invalidProduct": "False",  # Valor booleano correto
                                    "isStory": "False",  # Valor booleano correto
                                }
                                csv_products.append(csv_product)

                            # Atualizar status para mostrar que está salvando os produtos
                            self.current_status = f"Salvando {len(csv_products)} produtos da categoria: {category['name']} - Página {page}/{max_page}"
                            self.log_manager.info(f"Salvando {len(csv_products)} produtos no arquivo CSV")

                            # Salvar produtos no CSV
                            with open(
                                output_file, "a", encoding="utf-8-sig", newline=""
                            ) as f:
                                for product in csv_products:
                                    # Converter produto para linha CSV
                                    csv_line = (
                                        ",".join(
                                            [
                                                f'"{str(product.get(field, "")).replace("\"", "\"\"")}"'
                                                for field in headers
                                            ]
                                        )
                                        + "\n"
                                    )
                                    f.write(csv_line)

                            self.scraper_stats["total_products"] += len(products)

                        self.scraper_stats["processed_pages"] += 1

                    except Exception as e:
                        log.error(f"Erro ao processar página {url}: {e}")
                        self.scraper_stats["errors"].append(f"Erro ao processar página {url}: {e}")

                # Incrementar contador de categorias processadas
                self.scraper_stats["processed_categories"] += 1

            # Atualizar estatísticas finais
            self.scraper_stats["end_time"] = datetime.now().isoformat()

            # Calcular tempo de execução
            start_time = datetime.fromisoformat(self.scraper_stats["start_time"])
            end_time = datetime.fromisoformat(self.scraper_stats["end_time"])
            duration_seconds = (end_time - start_time).total_seconds()

            # Formatar mensagem com mais detalhes
            message = f"Scraper concluído em {duration_seconds:.1f} segundos. "
            message += f"Coletados {self.scraper_stats['total_products']} produtos "
            message += f"em {self.scraper_stats['processed_categories']} categorias "
            message += f"e {self.scraper_stats['processed_pages']} páginas."

            self.current_status = "Concluído"

            # Atualizar status na interface
            self.log_manager.info(f"Status: {self.current_status}")
            self.log_manager.info(message)

            return {
                "status": "success",
                "stats": self.scraper_stats,
                "message": message,
            }

        except Exception as e:
            log.error(f"Erro ao executar scraper: {e}")
            self.scraper_stats["errors"].append(f"Erro ao executar scraper: {str(e)}")
            self.scraper_stats["end_time"] = datetime.now().isoformat()
            self.current_status = f"Erro: {str(e)}"

            # Atualizar status na interface
            self.log_manager.error(f"Status: {self.current_status}")
            self.log_manager.error(f"Erro ao executar scraper: {str(e)}")

            return {
                "status": "error",
                "stats": self.scraper_stats,
                "message": f"Erro ao executar scraper: {str(e)}",
            }

        finally:
            with self.lock:
                self._is_running = False

    def stop_scraper(self) -> Dict[str, Any]:
        """
        Para o scraper em execução.

        Returns:
            Dict[str, Any]: Status do scraper
        """
        with self.lock:
            if not self._is_running:
                return {
                    "status": "not_running",
                    "message": "O scraper não está em execução",
                }

            self._is_running = False
            self.current_status = "Interrompido pelo usuário"
            self.scraper_stats["end_time"] = datetime.now().isoformat()

            # Atualizar status na interface
            self.log_manager.warning(f"Status: {self.current_status}")
            self.log_manager.warning("Scraper interrompido pelo usuário")

            return {
                "status": "stopped",
                "stats": self.scraper_stats,
                "message": "Scraper interrompido pelo usuário",
            }

    def get_status(self) -> Dict[str, Any]:
        """
        Retorna o status atual do scraper.

        Returns:
            Dict[str, Any]: Status do scraper
        """
        with self.lock:
            # Calcular tempo decorrido se estiver em execução
            elapsed_time = ""
            if self._is_running and "start_time" in self.scraper_stats:
                start_time = datetime.fromisoformat(self.scraper_stats["start_time"])
                elapsed_seconds = (datetime.now() - start_time).total_seconds()
                elapsed_time = f" (em execução há {elapsed_seconds:.1f} segundos)"

            # Adicionar informação de tempo ao status
            status = self.current_status + elapsed_time if self._is_running else self.current_status

            # Verificar se está concluído ou com erro
            is_completed = not self._is_running and self.current_status == "Concluído"
            has_error = not self._is_running and self.current_status.startswith("Erro")

            # Formatar mensagem para o usuário
            message = status
            if "start_time" in self.scraper_stats and "end_time" in self.scraper_stats and self.scraper_stats["end_time"]:
                try:
                    start_time = datetime.fromisoformat(self.scraper_stats["start_time"])
                    end_time = datetime.fromisoformat(self.scraper_stats["end_time"])
                    duration_seconds = (end_time - start_time).total_seconds()
                    message += f" (duração: {duration_seconds:.1f} segundos)"
                except Exception:
                    pass

            return {
                "is_running": self._is_running,
                "is_completed": is_completed,
                "has_error": has_error,
                "status": status,
                "message": message,
                "total_products": self.scraper_stats.get("total_products", 0),
                "processed_categories": len(self.scraper_stats.get("processed_categories", [])) if isinstance(self.scraper_stats.get("processed_categories"), list) else 0,
                "processed_pages": self.scraper_stats.get("processed_pages", 0),
                "errors": self.scraper_stats.get("errors", []) if isinstance(self.scraper_stats.get("errors"), list) else [],
            }

    async def validate_selectors(
        self, store: str, url: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Valida os seletores do PromoHunter para uma loja específica.

        Args:
            store: Loja para validar os seletores
            url: URL opcional para testar os seletores

        Returns:
            Dict[str, Any]: Resultado da validação
        """
        # Verificar se a loja é válida
        if store not in ["mercadolivre", "magalu", "amazon"]:
            raise ValueError(f"Loja inválida: {store}")

        # Se não houver URL, usar uma URL padrão para a loja
        if not url:
            if store == "mercadolivre":
                url = "https://www.mercadolivre.com.br/ofertas"
            elif store == "magalu":
                url = "https://www.magazineluiza.com.br/ofertas/l/of/"
            elif store == "amazon":
                url = "https://www.amazon.com.br/deals"

        # Inicializar o validador de seletores
        try:
            from src.utils.selector_validator import SelectorValidator
            validator = SelectorValidator(store)
            self.log_manager.info(f"Iniciando validação de seletores para {store} usando URL: {url}")

            # Executar a validação de seletores
            validation_stats = validator.validate_selectors(url)

            # Formatar mensagem com detalhes adicionais
            valid_count = validation_stats.get('valid', 0)
            tested_count = validation_stats.get('tested', 0)
            new_count = validation_stats.get('new', 0)
            updated_count = validation_stats.get('updated', 0)

            # Obter detalhes dos seletores
            selector_details = validation_stats.get('selector_details', {})
            invalid_selectors = selector_details.get('invalid_selectors', [])
            new_selectors = selector_details.get('new_selectors', [])
            updated_selectors = selector_details.get('updated_selectors', [])

            # Criar mensagem detalhada
            message = f"Validação concluída. Testados {tested_count} seletores, {valid_count} válidos, {len(invalid_selectors)} inválidos, {new_count} novos, {updated_count} atualizados."

            # Adicionar detalhes sobre seletores inválidos se houver
            if invalid_selectors:
                message += "\n\nSeletores inválidos encontrados. Considere atualizá-los."

            # Adicionar detalhes sobre novos seletores se houver
            if new_selectors:
                message += "\n\nNovos seletores foram adicionados para melhorar a raspagem."

            # Adicionar detalhes sobre seletores atualizados se houver
            if updated_selectors:
                message += "\n\nSeletores existentes foram atualizados para melhorar a compatibilidade."

            # Retornar os resultados da validação com detalhes
            return {
                "status": "success",
                "store": store,
                "url": url,
                "validation_stats": validation_stats,
                "selector_details": selector_details,
                "message": message,
            }
        except Exception as e:
            self.log_manager.error(f"Erro ao validar seletores: {e}")

            # Fallback: usar o método antigo (scrape_url)
            self.log_manager.info(f"Usando método alternativo para validar seletores")
            products = await self.scrapy_service.scrape_url(url, max_pages=1)

            # Contar quantos produtos foram encontrados com cada tipo de seletor
            selector_stats = {}
            for product in products:
                for field, value in product.items():
                    if value:
                        selector_stats[field] = selector_stats.get(field, 0) + 1

            return {
                "status": "partial_success",
                "store": store,
                "url": url,
                "products_found": len(products),
                "selector_stats": selector_stats,
                "message": f"Validação parcial concluída. Encontrados {len(products)} produtos.",
                "error": str(e)
            }

    async def test_selector(
        self, store: str, selector: str, selector_type: str, url: str
    ) -> Dict[str, Any]:
        """
        Testa um seletor específico do PromoHunter em uma URL.

        Args:
            store: Loja para testar o seletor
            selector: Seletor CSS a ser testado
            selector_type: Tipo do seletor
            url: URL para testar o seletor

        Returns:
            Dict[str, Any]: Resultado do teste do seletor
        """
        # Verificar se a loja é válida
        if store not in ["mercadolivre", "magalu", "amazon"]:
            raise ValueError(f"Loja inválida: {store}")

        # Verificar se o tipo de seletor é válido
        valid_types = ["product", "title", "price", "old_price", "link", "image", "installments", "coupon", "shipping", "product_url"]
        if selector_type not in valid_types:
            raise ValueError(f"Tipo de seletor inválido: {selector_type}. Tipos válidos: {', '.join(valid_types)}")

        try:
            # Usar o ScrapyService para buscar a página
            self.log_manager.info(f"Testando seletor '{selector}' do tipo '{selector_type}' para {store} na URL: {url}")

            # Buscar a página usando o ScrapyService
            from src.scrapers.scrapy_implementation.scrapy_service import ScrapyService
            scrapy_service = ScrapyService()

            # Buscar a página usando técnicas anti-detecção
            html_content = await scrapy_service.fetch_page_content(url)
            if not html_content:
                return {
                    "status": "error",
                    "store": store,
                    "url": url,
                    "selector": selector,
                    "selector_type": selector_type,
                    "message": "Não foi possível obter a página para teste.",
                    "matches": 0,
                    "elements": []
                }

            # Usar BeautifulSoup para analisar o HTML
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_content, "html.parser")

            # Testar o seletor
            try:
                # Tentar usar o seletor como está
                elements = soup.select(selector)
            except Exception as e:
                # Se falhar, tentar corrigir o seletor
                self.log_manager.warning(f"Erro ao usar seletor original '{selector}': {e}. Tentando corrigir.")
                # Verificar se o seletor começa com ponto para classes
                if not selector.startswith(".") and " " in selector:
                    # Tentar converter espaços em pontos para classes
                    fixed_selector = ".".join(selector.split())
                    try:
                        elements = soup.select(fixed_selector)
                        self.log_manager.info(f"Seletor corrigido para '{fixed_selector}'")
                        selector = fixed_selector
                    except Exception:
                        # Se ainda falhar, retornar erro
                        return {
                            "status": "error",
                            "store": store,
                            "url": url,
                            "selector": selector,
                            "selector_type": selector_type,
                            "message": f"Erro ao analisar o seletor: {str(e)}",
                            "matches": 0,
                            "elements": []
                        }
                else:
                    # Se não for um problema de espaços, retornar erro
                    return {
                        "status": "error",
                        "store": store,
                        "url": url,
                        "selector": selector,
                        "selector_type": selector_type,
                        "message": f"Erro ao analisar o seletor: {str(e)}",
                        "matches": 0,
                        "elements": []
                    }

            # Extrair informações dos elementos encontrados
            element_data = []
            for i, element in enumerate(elements[:10]):  # Limitar a 10 elementos para não sobrecarregar
                # Extrair texto e atributos relevantes
                element_info = {
                    "index": i,
                    "tag": element.name,
                    "text": element.get_text(strip=True),
                    "html": str(element)[:500],  # Limitar tamanho do HTML
                }

                # Adicionar atributos específicos para cada tipo de seletor
                if selector_type == "image":
                    element_info["src"] = element.get("src", "")
                    element_info["data-src"] = element.get("data-src", "")
                    element_info["alt"] = element.get("alt", "")
                elif selector_type in ["link", "product_url"]:
                    element_info["href"] = element.get("href", "")

                element_data.append(element_info)

            # Determinar o status com base no número de elementos encontrados
            status = "success" if elements else "warning"
            message = f"Encontrados {len(elements)} elementos com o seletor '{selector}'."

            if not elements:
                message += " O seletor não encontrou nenhum elemento na página."
            elif len(elements) > 10:
                message += f" Mostrando apenas os primeiros 10 de {len(elements)} elementos."

            return {
                "status": status,
                "store": store,
                "url": url,
                "selector": selector,
                "selector_type": selector_type,
                "message": message,
                "matches": len(elements),
                "elements": element_data
            }
        except Exception as e:
            self.log_manager.error(f"Erro ao testar seletor: {e}", exc_info=True)
            return {
                "status": "error",
                "store": store,
                "url": url,
                "selector": selector,
                "selector_type": selector_type,
                "message": f"Erro ao testar seletor: {str(e)}",
                "matches": 0,
                "elements": []
            }
