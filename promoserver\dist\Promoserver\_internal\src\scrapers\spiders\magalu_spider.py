import scrapy
import logging
import re
from datetime import datetime
from urllib.parse import urlparse, parse_qs, urle<PERSON><PERSON>, urlunparse

logger = logging.getLogger(__name__)

class MagaluSpider(scrapy.Spider):
    name = "magalu"
    allowed_domains = ["magazinevoce.com.br", "magazineluiza.com.br", "magazinevoce.com.br"]

    def __init__(self, url=None, *args, **kwargs):
        super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__(*args, **kwargs)
        self.start_urls = [url] if url else []
        self.logger.info(f"Iniciando spider para URL: {url}")

    def parse(self, response):
        self.logger.info(f"Processando página: {response.url}")

        # Extrai o título do produto
        title = response.css('h1[data-testid="heading-product-title"]::text').get()
        if not title:
            title = response.css('h1.sc-kpDqfm::text').get()
        if not title:
            title = response.css('h1.header-product__title::text').get()
        if not title:
            title = response.css('h1::text').get()
        if not title:
            title = response.css('title::text').get()

        if title:
            title = title.strip()

        if not title:
            self.logger.warning(f"Não foi possível extrair o título do produto: {response.url}")
            title = "Título não encontrado"

        price = None
        old_price = None

        # Tenta extrair o preço PIX/à vista primeiro (geralmente o menor)
        price_pix_selectors = [
            'p[data-testid="price-pix"]::text',
            'strong[data-testid="price-avista"]::text',
            '.price-template-price-avista__value::text', # Outro seletor comum para preço à vista
            'span[data-testid="text-pix-price-box"]::text' # Mais um seletor para preço PIX
        ]
        for selector in price_pix_selectors:
            price_text = response.css(selector).get()
            if price_text:
                price = re.sub(r'[^\d,.]', '', price_text.strip()).replace('.', '').replace(',', '.')
                if price: # Garante que não seja uma string vazia após o sub
                    self.logger.info(f"Preço PIX/à vista encontrado: {price} usando seletor: {selector}")
                    break
        
        # Se não encontrou preço PIX, tenta o preço "principal" ou "promocional"
        if not price:
            price_value_selectors = [
                'p[data-testid="price-value"]::text', # Preço "por"
                '.price-template__text::text',
                'span.sc-kpDqfm.price-info__Price-sc-__sc-33au7i-0::text', # Seletor antigo
                '.p-price::text' # Seletor genérico de preço
            ]
            for selector in price_value_selectors:
                price_text = response.css(selector).get()
                if price_text:
                    # Verifica se este não é o preço antigo (ex: R$ 129,90)
                    # O preço antigo geralmente é acompanhado de "de" ou está riscado
                    # Esta verificação é um pouco heurística
                    parent_text_for_value = response.xpath(f'//*[contains(text(), "{price_text.strip()}")]/parent::*/preceding-sibling::*[contains(translate(., "DE", "de"), "de ")]').get()
                    if not parent_text_for_value: # Se não parece ser o preço "de" (antigo)
                        price = re.sub(r'[^\d,.]', '', price_text.strip()).replace('.', '').replace(',', '.')
                        if price:
                            self.logger.info(f"Preço principal/promocional encontrado: {price} usando seletor: {selector}")
                            break
                    else:
                        self.logger.info(f"Preço {price_text.strip()} ignorado pois parece ser preço 'de' (antigo).")


        # Extrai o preço anterior/original
        old_price_selectors = [
            'p[data-testid="price-original"]::text', # Preço "de"
            's[data-testid="price-original"]::text', # Preço "de" riscado
            '.price-template__old-value::text',
            'span.sc-kpDqfm.price-info__PriceInfo-sc-__sc-33au7i-1::text', # Seletor antigo
            '.originalPrice::text' # Outro seletor comum
        ]
        for selector in old_price_selectors:
            old_price_text = response.css(selector).get()
            if old_price_text:
                old_price = re.sub(r'[^\d,.]', '', old_price_text.strip()).replace('.', '').replace(',', '.')
                if old_price:
                    self.logger.info(f"Preço antigo/original encontrado: {old_price} usando seletor: {selector}")
                    break
        
        # Se o preço atual ainda não foi encontrado e o old_price foi,
        # pode ser que o price_value (que não é pix) seja o preço atual.
        if not price and old_price:
            price_value_text = response.css('p[data-testid="price-value"]::text').get()
            if price_value_text:
                potential_price = re.sub(r'[^\d,.]', '', price_value_text.strip()).replace('.', '').replace(',', '.')
                if potential_price and potential_price != old_price:
                    price = potential_price
                    self.logger.info(f"Preço atual definido como price-value (não PIX): {price}")

        # Formatação final para garantir que os preços sejam strings com duas casas decimais
        if price:
            try:
                price_float = float(price)
                price = f"{price_float:.2f}".replace('.', ',') # Formato R$ XX,XX
            except ValueError:
                self.logger.warning(f"Não foi possível converter preço '{price}' para float.")
                price = None # Invalida se não for um número

        if old_price:
            try:
                old_price_float = float(old_price)
                old_price = f"{old_price_float:.2f}".replace('.', ',') # Formato R$ XX,XX
            except ValueError:
                self.logger.warning(f"Não foi possível converter preço antigo '{old_price}' para float.")
                old_price = None # Invalida se não for um número
        
        # Se o preço atual for igual ao preço antigo, e o preço antigo existir,
        # e o preço PIX não foi pego, algo está errado.
        # Neste caso, se o preço PIX existir, ele deve ser o 'price'.
        # Se o 'price' atual (que pode ser o 'price-value') for igual ao 'old_price',
        # e existir um preço PIX diferente, o 'price' deve ser o PIX.
        if price and old_price and price == old_price:
            self.logger.warning(f"Preço atual ({price}) é igual ao preço antigo ({old_price}). Verificando preço PIX novamente.")
            price_pix_text_check = response.css('p[data-testid="price-pix"]::text').get()
            if not price_pix_text_check: # Tenta outro seletor de PIX
                 price_pix_text_check = response.css('strong[data-testid="price-avista"]::text').get()

            if price_pix_text_check:
                pix_price_check = re.sub(r'[^\d,.]', '', price_pix_text_check.strip()).replace('.', '').replace(',', '.')
                if pix_price_check:
                    try:
                        pix_price_check_float = float(pix_price_check)
                        formatted_pix_check = f"{pix_price_check_float:.2f}".replace('.', ',')
                        if formatted_pix_check != old_price:
                            price = formatted_pix_check
                            self.logger.info(f"Preço atual corrigido para preço PIX: {price}")
                        else:
                            self.logger.info(f"Preço PIX ({formatted_pix_check}) é igual ao preço antigo. Mantendo preço: {price}")
                    except ValueError:
                        self.logger.warning(f"Não foi possível converter preço PIX de verificação '{pix_price_check}' para float.")


        # Extrai a imagem
        image_url = None

        # Tenta extrair a imagem principal
        img_selector = response.css('[data-testid="image-selected-thumbnail"]::attr(src)').get()
        if img_selector:
            image_url = img_selector

        # Tenta extrair a imagem do showcase
        if not image_url:
            img_selector = response.css('.showcase-product__big-img::attr(src)').get()
            if img_selector:
                image_url = img_selector

        # Tenta extrair a imagem do novo layout
        if not image_url:
            img_selector = response.css('img.sc-dBmzty::attr(src)').get()
            if img_selector:
                image_url = img_selector

        # Tenta extrair a imagem do carrossel
        if not image_url:
            img_selector = response.css('.carousel-slide img::attr(src)').get()
            if img_selector:
                image_url = img_selector

        # Tenta extrair a imagem do produto
        if not image_url:
            img_selector = response.css('.product-image img::attr(src)').get()
            if img_selector:
                image_url = img_selector

        # Tenta qualquer imagem do domínio Magalu
        if not image_url:
            img_selector = response.css('img[src*="i.mlcdn.com.br"]::attr(src)').get()
            if img_selector:
                image_url = img_selector

        # Tenta qualquer imagem do domínio a-static.mlcdn
        if not image_url:
            img_selector = response.css('img[src*="a-static.mlcdn.com.br"]::attr(src)').get()
            if img_selector:
                image_url = img_selector

        # Extrai informações de parcelamento
        installments = response.css('.installment__value::text').get()
        if not installments:
            installments = "Consulte parcelas no site"

        # Extrai informações de frete
        shipping = "Verificar CEP"
        shipping_selector = response.css('[data-testid="postal-code-input"]')
        if shipping_selector:
            shipping = "Verificar CEP"

        delivery_info = response.css('.product-shipping::text').get()
        if delivery_info:
            delivery_info = delivery_info.lower()
            if "frete grátis" in delivery_info or "entrega grátis" in delivery_info:
                if "exceto" in delivery_info or "confira as regras" in delivery_info:
                    shipping = "Com frete (Verificar Regras)"
                else:
                    shipping = "Com frete"
            elif "retire grátis" in delivery_info or "retire na loja" in delivery_info:
                shipping = "Retire Grátis"

        # Extrai o ID do produto da URL
        product_id = None
        pattern1 = r"/p/([a-zA-Z0-9]+)/"
        match1 = re.search(pattern1, response.url)
        if match1:
            product_id = match1.group(1)
        else:
            pattern2 = r"/produto/[^/]+/(\d{7,})/?|/p/[^/]+/(\d{7,})/?|/([a-zA-Z0-9]{10,})/?$"
            match2 = re.search(pattern2, response.url)
            if match2:
                # Pega o grupo que não for None (ID numérico ou alfanumérico)
                product_id = next((g for g in match2.groups() if g is not None), None)

        # Limpa a URL do produto
        product_url = self.clean_product_url(response.url)

        # Retorna os dados do produto
        return {
            "platform": "Magazine Luiza",
            "product_id": product_id,
            "url_produto": product_url,
            "url_afiliado": "",  # Deixa vazio conforme solicitado
            "title": title, # Já stripado antes
            "description": "",  # Deixa vazio conforme solicitado
            "price": f"R$ {price}" if price else "Preço não disponível",
            "old_price": f"R$ {old_price}" if old_price else None, # Retorna None se não encontrado
            "image_url": image_url,
            "installments": installments.strip() if installments else "Consulte parcelas no site",
            "coupon_info": "",  # Deixa vazio para Magalu conforme solicitado
            "shipping": shipping.strip() if shipping else "Verificar CEP",
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            # Campos adicionais para compatibilidade com PromoHunter
            "ativo": True,
            "menor_preco": False,
            "indicamos": False,
            "disparar_whatsapp": False,
            "frete": False, # Redundante com shipping, mas mantendo
            "invalidProduct": False,
            "isStory": False,
            "cupom": ""  # Deixa vazio para Magalu conforme solicitado
        }

    def clean_product_url(self, url):
        """
        Limpa a URL do produto, removendo parâmetros de tracking
        """
        if not url:
            return ""

        try:
            parsed = urlparse(url)

            # Remove fragmentos (tudo após #)
            clean_url = url.split('#')[0]

            # Remove parâmetros de tracking
            params_to_remove = [
                "ref", "ref_", "tag", "_encoding", "psc", "pd_rd_w", "pd_rd_r",
                "pd_rd_wg", "sprefix", "keywords", "crid", "dchild", "qid", "sr",
                "th", "spm", "trk", "smid", "asc_source", "asc_campaign", "asc_refurl",
                "searchVariation", "position", "tracking_id", "source_id", "component_id",
                "item_id", "category_id", "official_store_id", "pdp_filters", "dealer_id",
                "force_landing_page"
            ]

            query_params = parse_qs(parsed.query, keep_blank_values=True)
            filtered_params = {
                k: v for k, v in query_params.items()
                if k.lower() not in params_to_remove
                and not k.lower().startswith("asc_")
                and not k.lower().startswith("pd_rd_")
                and not k.lower().startswith("pf_rd_")
                and not k.lower().startswith("ref_")
                and not k.lower().startswith("sp_")
            }

            new_query = urlencode(filtered_params, doseq=True)

            clean_url = urlunparse((
                parsed.scheme,
                parsed.netloc,
                parsed.path.rstrip("/"),
                parsed.params,
                new_query,
                ""
            ))

            if clean_url.endswith("?"):
                clean_url = clean_url[:-1]

            return clean_url
        except Exception as e:
            self.logger.error(f"Erro ao limpar URL: {e}")

        return url
