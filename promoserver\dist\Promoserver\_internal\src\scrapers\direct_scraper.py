"""
Implementação de um scraper direto para o Mercado Livre
"""

import logging
import asyncio
import re
import json
from typing import Dict, List, Any, Optional
import requests
from bs4 import BeautifulSoup
from urllib.parse import urlparse, parse_qs

# Configuração de logging
logger = logging.getLogger(__name__)

class DirectScraper:
    """
    Implementação de um scraper direto para o Mercado Livre
    """

    def __init__(self, log_manager=None):
        """
        Inicializa o scraper direto

        Args:
            log_manager: Gerenciador de logs (opcional)
        """
        self.log_manager = log_manager
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept-Language': 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
        })

        # Inicializa o cache de produtos
        self.product_cache = {}

    def log_message(self, message, level='info'):
        """
        Registra uma mensagem no log

        Args:
            message: Mensagem a ser registrada
            level: Nível de log (info, warning, error, debug)
        """
        if self.log_manager:
            if level == 'info':
                self.log_manager.info(message)
            elif level == 'warning':
                self.log_manager.warning(message)
            elif level == 'error':
                self.log_manager.error(message)
            elif level == 'debug':
                self.log_manager.debug(message)

        # Também registra no logger padrão
        if level == 'info':
            logger.info(message)
        elif level == 'warning':
            logger.warning(message)
        elif level == 'error':
            logger.error(message)
        elif level == 'debug':
            logger.debug(message)

    def scrape_url(self, url: str, max_pages: int = 2) -> List[Dict[str, Any]]:
        """
        Raspa produtos a partir de uma URL

        Args:
            url: URL para raspar
            max_pages: Número máximo de páginas a serem raspadas

        Returns:
            Lista de produtos
        """
        self.log_message(f"Iniciando scraping da URL: {url}")

        # Verifica se a URL é do Mercado Livre
        if "mercadolivre.com.br" not in url and "mercadolibre.com" not in url:
            self.log_message(f"URL não suportada: {url}", level='error')
            return []

        # Verifica se é uma URL de perfil social ou link de compartilhamento
        if "/social/" in url:
            self.log_message(f"URL de perfil social detectada: {url}", level='warning')
            return [{
                "plataforma": "Mercadolivre",
                "title": "URL de perfil social não suportada",
                "url": url,
                "url_produto": url,
                "error": True,
                "description": "Esta URL é um link de perfil social ou compartilhamento, não um produto específico. Por favor, use uma URL direta de produto do Mercado Livre."
            }]

        products = []
        current_page = 1

        while current_page <= max_pages:
            self.log_message(f"Raspando página {current_page} de {max_pages}")

            # Adiciona parâmetro de página à URL, se necessário
            page_url = url
            if current_page > 1:
                # Verifica se a URL já tem parâmetros
                if "?" in url:
                    page_url = f"{url}&page={current_page}"
                else:
                    page_url = f"{url}?page={current_page}"

            try:
                # Faz a requisição HTTP
                self.log_message(f"Fazendo requisição para: {page_url}")
                response = self.session.get(page_url, timeout=30)

                if response.status_code != 200:
                    self.log_message(f"Erro ao acessar a página: {response.status_code}", level='error')
                    break

                # Parseia o HTML
                soup = BeautifulSoup(response.text, 'html.parser')

                # Extrai os produtos
                page_products = self._extract_products(soup)

                if not page_products:
                    self.log_message("Nenhum produto encontrado na página", level='warning')
                    break

                self.log_message(f"Encontrados {len(page_products)} produtos na página {current_page}")
                products.extend(page_products)

                # Verifica se há mais páginas
                next_page = self._has_next_page(soup)
                if not next_page:
                    self.log_message("Não há mais páginas para raspar")
                    break

                # Avança para a próxima página
                current_page += 1

                # Pequena pausa para evitar sobrecarga no servidor
                import time
                time.sleep(2)

            except Exception as e:
                self.log_message(f"Erro ao raspar a página {current_page}: {e}", level='error')
                break

        self.log_message(f"Scraping concluído. Coletados {len(products)} produtos.")
        return products

    def _extract_products(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """
        Extrai produtos de uma página HTML

        Args:
            soup: Objeto BeautifulSoup com o HTML da página

        Returns:
            Lista de produtos
        """
        products = []

        # Tenta encontrar os produtos na página
        # Seletor para ofertas do Mercado Livre
        product_cards = soup.select('li.promotion-item')

        if not product_cards:
            # Tenta seletor alternativo
            product_cards = soup.select('li.ui-search-layout__item')

        if not product_cards:
            # Tenta outro seletor alternativo para a página de ofertas
            product_cards = soup.select('div.andes-card')

        self.log_message(f"Encontrados {len(product_cards)} cards de produtos")

        for card in product_cards:
            try:
                product = {}

                # Extrai o título
                title_elem = card.select_one('h2.promotion-item__title, h2.ui-search-item__title')
                if title_elem:
                    product['title'] = title_elem.text.strip()
                else:
                    # Tenta outro seletor
                    title_elem = card.select_one('h2')
                    if title_elem:
                        product['title'] = title_elem.text.strip()

                # Extrai o link
                link_elem = card.select_one('a.promotion-item__link-container, a.ui-search-link')
                if link_elem and 'href' in link_elem.attrs:
                    product['url'] = link_elem['href']
                    product['url_produto'] = link_elem['href']

                # Extrai a imagem
                img_elem = card.select_one('img.promotion-item__img, img.ui-search-result-image__element')
                if img_elem and 'src' in img_elem.attrs:
                    product['image_url'] = img_elem['src']
                    product['url_imagem'] = img_elem['src']
                elif img_elem and 'data-src' in img_elem.attrs:
                    product['image_url'] = img_elem['data-src']
                    product['url_imagem'] = img_elem['data-src']

                # Extrai o preço atual
                price_elem = card.select_one('span.andes-money-amount__fraction')
                if price_elem:
                    price_text = price_elem.text.strip().replace('.', '')
                    cents_elem = card.select_one('span.andes-money-amount__cents')
                    if cents_elem:
                        cents_text = cents_elem.text.strip()
                        price_text = f"{price_text},{cents_text}"

                    try:
                        price = float(price_text.replace(',', '.'))
                        product['price'] = price
                        product['preco_atual'] = price
                    except ValueError:
                        self.log_message(f"Erro ao converter preço: {price_text}", level='warning')

                # Extrai o preço antigo
                old_price_elem = card.select_one('span.ui-search-price__original-value, span.andes-money-amount__discount')
                if old_price_elem:
                    old_price_text = old_price_elem.text.strip()
                    # Remove símbolos de moeda e espaços
                    old_price_text = re.sub(r'[^\d,]', '', old_price_text)

                    try:
                        old_price = float(old_price_text.replace(',', '.'))
                        product['old_price'] = old_price
                        product['preco_antigo'] = old_price
                    except ValueError:
                        self.log_message(f"Erro ao converter preço antigo: {old_price_text}", level='warning')

                # Adiciona campos padrão
                product['plataforma'] = 'Mercadolivre'
                product['cupom'] = ''

                # Adiciona o produto à lista se tiver pelo menos título e URL
                if 'title' in product and 'url' in product:
                    products.append(product)
                    self.log_message(f"Produto extraído: {product['title'][:50]}...", level='debug')

            except Exception as e:
                self.log_message(f"Erro ao extrair produto: {e}", level='error')

        return products

    def _has_next_page(self, soup: BeautifulSoup) -> bool:
        """
        Verifica se há uma próxima página

        Args:
            soup: Objeto BeautifulSoup com o HTML da página

        Returns:
            True se houver próxima página, False caso contrário
        """
        # Verifica se há um botão de próxima página
        next_button = soup.select_one('a.andes-pagination__link[title="Seguinte"]')
        if next_button:
            return True

        # Verifica outro seletor para paginação
        pagination = soup.select('li.andes-pagination__button--next')
        if pagination:
            return True

        return False
