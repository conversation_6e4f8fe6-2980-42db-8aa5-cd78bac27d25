"""
Definição dos itens do Scrapy para o PromoScraper
"""

import scrapy


class ProductItem(scrapy.Item):
    """
    Item para armazenar dados de um produto
    """
    # Campos obrigatórios
    platform = scrapy.Field()
    product_id = scrapy.Field()
    url_produto = scrapy.Field()
    title = scrapy.Field()
    price = scrapy.Field()

    # Campos opcionais
    old_price = scrapy.Field()
    image_url = scrapy.Field()
    url_afiliado = scrapy.Field()
    description = scrapy.Field()
    category = scrapy.Field()
    subcategory = scrapy.Field()
    coupon_info = scrapy.Field()
    installments = scrapy.Field()
    shipping = scrapy.Field()
    
    # Campos para controle interno
    timestamp = scrapy.Field()
    
    # Campos adicionais para compatibilidade com PromoHunter
    ativo = scrapy.Field()
    menor_preco = scrapy.Field()
    indicamos = scrapy.Field()
    disparar_whatsapp = scrapy.Field()
    frete = scrapy.Field()
    invalidProduct = scrapy.Field()
    isStory = scrapy.Field()
    cupom = scrapy.Field()

    def __repr__(self):
        """Representação do item para debug"""
        title = self.get('title', '')[:30]
        price = self.get('price', '')
        return f"ProductItem(title={title}..., price={price})"
