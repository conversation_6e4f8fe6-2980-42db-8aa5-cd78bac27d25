"""
Módulo para executar spiders do Scrapy programaticamente
"""

import logging
import os
import sys
import json
import asyncio
import tempfile
from typing import List, Dict, Any, Optional
from datetime import datetime

# Verifica se o Scrapy está instalado
try:
    from scrapy.crawler import CrawlerProcess
    from scrapy.utils.project import get_project_settings
    SCRAPY_AVAILABLE = True
except ImportError:
    SCRAPY_AVAILABLE = False
    logging.warning("Scrapy não está instalado. Usando o scraper padrão.")

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

# Informa que estamos usando a implementação Scrapy pura
logger.info("Usando implementação Scrapy pura para raspagem de dados.")


class ScrapyRunner:
    """
    Classe para executar spiders do Scrapy programaticamente
    """

    def __init__(self):
        """Inicializa o runner"""
        if not SCRAPY_AVAILABLE:
            logger.error("Scrapy não está instalado. Não é possível executar spiders.")
            return

        # Obtém as configurações do projeto
        self.settings = get_project_settings()

        # Inicializa o processo
        self.process = None

        # Lista para armazenar os itens coletados
        self.items = []

    async def run_spider(self, spider_name: str, **kwargs) -> List[Dict[str, Any]]:
        """
        Executa um spider do Scrapy

        Args:
            spider_name: Nome do spider a ser executado
            **kwargs: Argumentos adicionais para o spider

        Returns:
            Lista de itens coletados
        """
        if not SCRAPY_AVAILABLE:
            logger.error("Scrapy não está instalado. Não é possível executar spiders.")
            return []

        logger.info(f"Executando spider {spider_name} com argumentos: {kwargs}")

        # Cria um arquivo temporário para armazenar os resultados
        with tempfile.NamedTemporaryFile(suffix='.json', delete=False) as tmp_file:
            output_file = tmp_file.name

        try:
            # Executa o Scrapy em um processo separado usando subprocess
            import subprocess
            import sys

            # Cria um script temporário para executar o Scrapy
            with tempfile.NamedTemporaryFile(suffix='.py', delete=False, mode='w', encoding='utf-8') as script_file:
                script_path = script_file.name

                # Prepara os argumentos para o script
                url_arg = f'"{kwargs.get("url", "")}"' if kwargs.get("url") else "None"
                category_arg = f'"{kwargs.get("category", "")}"' if kwargs.get("category") else "None"
                max_pages_arg = str(kwargs.get("max_pages", 1))

                # Escreve o script para executar o spider
                script_content = f"""
import sys
import os
from scrapy.crawler import CrawlerProcess
from scrapy.utils.project import get_project_settings

# Adiciona o diretório atual ao PYTHONPATH
sys.path.insert(0, os.getcwd())

# Carrega as configurações
settings = get_project_settings()
settings.set('FEEDS', {{'{output_file.replace('\\', '\\\\')}': {{'format': 'json', 'encoding': 'utf8'}}}})
settings.set('LOG_ENABLED', False)
settings.set('TELNETCONSOLE_ENABLED', False)

# Cria o processo
process = CrawlerProcess(settings)

# Adiciona o spider com argumentos explícitos
url = {url_arg}
category = {category_arg}
max_pages = {max_pages_arg}

# Cria um dicionário de argumentos apenas com valores não None
spider_kwargs = {{'url': url}} if url is not None else {{}}
if category is not None:
    spider_kwargs['category'] = category
spider_kwargs['max_pages'] = max_pages

# Executa o spider
process.crawl('{spider_name}', **spider_kwargs)
process.start()
"""
                script_file.write(script_content)

            # Executa o script em um processo separado
            cmd = [sys.executable, script_path]
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )

            # Aguarda a conclusão do processo
            _, stderr = await process.communicate()

            if process.returncode != 0:
                logger.error(f"Erro ao executar spider {spider_name}: {stderr.decode('utf-8')}")
                return []

            # Remove o script temporário
            try:
                os.unlink(script_path)
            except Exception as e:
                logger.error(f"Erro ao remover script temporário: {e}")

            # Carrega os resultados
            try:
                if os.path.exists(output_file) and os.path.getsize(output_file) > 0:
                    with open(output_file, 'r', encoding='utf-8') as f:
                        content = f.read().strip()
                        if content:
                            items = json.loads(content)
                        else:
                            items = []
                else:
                    items = []
                logger.info(f"Spider {spider_name} coletou {len(items)} itens")
            except Exception as e:
                logger.error(f"Erro ao carregar resultados: {e}")
                items = []
        except Exception as e:
            logger.error(f"Erro ao executar spider {spider_name}: {e}")
            items = []
        finally:
            # Remove o arquivo temporário
            try:
                if os.path.exists(output_file):
                    os.unlink(output_file)
            except Exception as e:
                logger.error(f"Erro ao remover arquivo temporário: {e}")

        return items

    async def run_mercadolivre_spider(self, url: Optional[str] = None, category: Optional[str] = None, max_pages: int = 2) -> List[Dict[str, Any]]:
        """
        Executa o spider do Mercado Livre

        Args:
            url: URL específica para iniciar o scraping
            category: ID da categoria para raspar
            max_pages: Número máximo de páginas a serem raspadas

        Returns:
            Lista de produtos coletados
        """
        logger.info(f"Executando spider do Mercado Livre: url={url}, category={category}, max_pages={max_pages}")

        # Executa o spider
        items = await self.run_spider(
            'mercadolivre',  # Nome do spider definido na classe MercadoLivreSpider
            url=url,
            category=category,
            max_pages=max_pages
        )

        logger.info(f"Spider do Mercado Livre concluído. Coletados {len(items)} produtos.")

        return items

    async def run_magalu_spider(self, url: Optional[str] = None, category: Optional[str] = None, max_pages: int = 2) -> List[Dict[str, Any]]:
        """
        Executa o spider do Magalu

        Args:
            url: URL específica para iniciar o scraping
            category: ID da categoria para raspar
            max_pages: Número máximo de páginas a serem raspadas

        Returns:
            Lista de produtos coletados
        """
        logger.info(f"Executando spider do Magalu: url={url}, category={category}, max_pages={max_pages}")

        # Executa o spider
        items = await self.run_spider(
            'magalu',  # Nome do spider definido na classe MagaluSpider
            url=url,
            category=category,
            max_pages=max_pages
        )

        logger.info(f"Spider do Magalu concluído. Coletados {len(items)} produtos.")

        return items

    async def run_amazon_spider(self, url: Optional[str] = None, category: Optional[str] = None, max_pages: int = 2) -> List[Dict[str, Any]]:
        """
        Executa o spider da Amazon

        Args:
            url: URL específica para iniciar o scraping
            category: ID da categoria para raspar
            max_pages: Número máximo de páginas a serem raspadas

        Returns:
            Lista de produtos coletados
        """
        logger.info(f"Executando spider da Amazon: url={url}, category={category}, max_pages={max_pages}")

        # Executa o spider
        items = await self.run_spider(
            'amazon',  # Nome do spider definido na classe AmazonSpider
            url=url,
            category=category,
            max_pages=max_pages
        )

        logger.info(f"Spider da Amazon concluído. Coletados {len(items)} produtos.")

        return items


# Implementação alternativa para quando o Scrapy não está disponível ou falha
async def fallback_scraper(url: str, store: str) -> List[Dict[str, Any]]:
    """
    Implementação alternativa para quando o Scrapy não está disponível ou falha

    Args:
        url: URL para raspar
        store: Nome da loja (mercadolivre, magalu, amazon)

    Returns:
        Lista de produtos coletados (vazia ou com dados básicos)
    """
    logger.warning(f"Usando scraper alternativo para {store} (URL: {url})")

    if not SCRAPY_AVAILABLE:
        logger.error("O Scrapy não está instalado. Por favor, instale o Scrapy com 'pip install scrapy' para usar esta funcionalidade.")

    try:
        # Tenta fazer uma requisição básica para obter pelo menos algumas informações
        import requests
        from bs4 import BeautifulSoup
        import re
        from datetime import datetime

        logger.info(f"Tentando obter informações básicas de {url} usando requests")

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept-Language': 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
        }

        response = requests.get(url, headers=headers, timeout=30)

        if response.status_code != 200:
            logger.warning(f"Erro ao acessar a URL: {response.status_code}")
            return []

        soup = BeautifulSoup(response.text, 'html.parser')

        # Extrai o título
        title = soup.title.text.strip() if soup.title else "Título não encontrado"

        # Extrai a primeira imagem relevante
        image_url = None
        for img in soup.find_all('img'):
            src = img.get('src', '')
            if src and (store in src.lower() or 'product' in src.lower() or 'produto' in src.lower()):
                image_url = src
                break

        # Extrai o preço (busca por padrões comuns)
        price = None
        price_patterns = [
            r'R\$\s*[\d.,]+',  # R$ 123,45
            r'[\d.,]+\s*reais',  # 123,45 reais
            r'[\d.,]+,[\d]{2}'   # 123,45
        ]

        for pattern in price_patterns:
            price_match = re.search(pattern, response.text)
            if price_match:
                price = price_match.group(0)
                break

        # Cria um item básico
        item = {
            "platform": store.capitalize(),
            "product_id": None,
            "url_produto": url,
            "url_afiliado": "",
            "title": title,
            "description": "",
            "price": price if price else "Preço não disponível",
            "old_price": None,
            "image_url": image_url,
            "installments": "Consulte parcelas no site",
            "coupon_info": "",
            "shipping": "Verificar frete",
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            # Campos adicionais para compatibilidade com PromoHunter
            "ativo": True,
            "menor_preco": False,
            "indicamos": False,
            "disparar_whatsapp": False,
            "frete": False,
            "invalidProduct": False,
            "isStory": False,
            "cupom": ""
        }

        logger.info(f"Obtidas informações básicas: Título={title}, Imagem={image_url}, Preço={price}")
        return [item]

    except Exception as e:
        logger.error(f"Erro ao usar scraper alternativo: {e}")
        return []

# Funções para uso direto
async def scrape_mercadolivre(url: Optional[str] = None, category: Optional[str] = None, max_pages: int = 2) -> List[Dict[str, Any]]:
    """
    Função para raspar produtos do Mercado Livre

    Args:
        url: URL específica para iniciar o scraping
        category: ID da categoria para raspar
        max_pages: Número máximo de páginas a serem raspadas

    Returns:
        Lista de produtos coletados
    """
    if not SCRAPY_AVAILABLE:
        return await fallback_scraper(url or "https://www.mercadolivre.com.br/ofertas", "mercadolivre")

    try:
        runner = ScrapyRunner()
        items = await runner.run_mercadolivre_spider(url=url, category=category, max_pages=max_pages)

        # Se não encontrou itens, tenta o fallback
        if not items:
            logger.warning("Scrapy não retornou resultados. Tentando scraper alternativo.")
            return await fallback_scraper(url or "https://www.mercadolivre.com.br/ofertas", "mercadolivre")

        return items
    except Exception as e:
        logger.error(f"Erro ao executar Scrapy: {e}")
        return await fallback_scraper(url or "https://www.mercadolivre.com.br/ofertas", "mercadolivre")

async def scrape_magalu(url: Optional[str] = None, category: Optional[str] = None, max_pages: int = 2) -> List[Dict[str, Any]]:
    """
    Função para raspar produtos do Magalu

    Args:
        url: URL específica para iniciar o scraping
        category: ID da categoria para raspar
        max_pages: Número máximo de páginas a serem raspadas

    Returns:
        Lista de produtos coletados
    """
    if not SCRAPY_AVAILABLE:
        return await fallback_scraper(url or "https://www.magazineluiza.com.br/selecao/ofertasdodia/", "magalu")

    try:
        runner = ScrapyRunner()
        items = await runner.run_magalu_spider(url=url, category=category, max_pages=max_pages)

        # Se não encontrou itens, tenta o fallback
        if not items:
            logger.warning("Scrapy não retornou resultados. Tentando scraper alternativo.")
            return await fallback_scraper(url or "https://www.magazineluiza.com.br/selecao/ofertasdodia/", "magalu")

        return items
    except Exception as e:
        logger.error(f"Erro ao executar Scrapy: {e}")
        return await fallback_scraper(url or "https://www.magazineluiza.com.br/selecao/ofertasdodia/", "magalu")

async def scrape_amazon(url: Optional[str] = None, category: Optional[str] = None, max_pages: int = 2) -> List[Dict[str, Any]]:
    """
    Função para raspar produtos da Amazon

    Args:
        url: URL específica para iniciar o scraping
        category: ID da categoria para raspar
        max_pages: Número máximo de páginas a serem raspadas

    Returns:
        Lista de produtos coletados
    """
    if not SCRAPY_AVAILABLE:
        return await fallback_scraper(url or "https://www.amazon.com.br/deals", "amazon")

    try:
        runner = ScrapyRunner()
        items = await runner.run_amazon_spider(url=url, category=category, max_pages=max_pages)

        # Se não encontrou itens, tenta o fallback
        if not items:
            logger.warning("Scrapy não retornou resultados. Tentando scraper alternativo.")
            return await fallback_scraper(url or "https://www.amazon.com.br/deals", "amazon")

        return items
    except Exception as e:
        logger.error(f"Erro ao executar Scrapy: {e}")
        return await fallback_scraper(url or "https://www.amazon.com.br/deals", "amazon")


# Função para executar o spider a partir da linha de comando
def main():
    """Função principal para execução via linha de comando"""
    import argparse

    parser = argparse.ArgumentParser(description='Executa spiders do Scrapy')
    parser.add_argument('--spider', choices=['mercadolivre', 'magalu', 'amazon'], required=True, help='Spider a ser executado')
    parser.add_argument('--url', help='URL específica para iniciar o scraping')
    parser.add_argument('--category', help='ID da categoria para raspar')
    parser.add_argument('--max-pages', type=int, default=2, help='Número máximo de páginas a serem raspadas')
    parser.add_argument('--output', help='Arquivo para salvar os resultados (JSON)')

    args = parser.parse_args()

    if not args.url and not args.category:
        parser.error('É necessário fornecer --url ou --category')

    # Executa o spider
    if args.spider == 'mercadolivre':
        items = asyncio.run(scrape_mercadolivre(url=args.url, category=args.category, max_pages=args.max_pages))
    elif args.spider == 'magalu':
        items = asyncio.run(scrape_magalu(url=args.url, category=args.category, max_pages=args.max_pages))
    elif args.spider == 'amazon':
        items = asyncio.run(scrape_amazon(url=args.url, category=args.category, max_pages=args.max_pages))

    # Salva os resultados
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(items, f, ensure_ascii=False, indent=2)
        print(f"Resultados salvos em {args.output}")
    else:
        print(json.dumps(items, ensure_ascii=False, indent=2))


if __name__ == '__main__':
    main()
