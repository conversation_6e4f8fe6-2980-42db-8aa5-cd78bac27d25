"""
Adaptador para o scraper do Mercado Livre usando Scrapy
"""

import logging
import asyncio
import re
from typing import Dict, Optional

from .runner import scrape_mercadolivre
from .base.base_scraper import BaseScraper

logger = logging.getLogger(__name__)


class MercadoLivreScraper(BaseScraper):
    """
    Scraper para o Mercado Livre usando Scrapy
    """

    def __init__(self):
        super().__init__(store_id="mercadolivre")
        self.scrapy_available = True
        logger.info("MercadoLivreScraper inicializado com Scrapy")

    def extract_product_id(self, url: str) -> Optional[str]:
        """
        Extrai o ID do produto da URL do Mercado Livre
        """
        if not url:
            return None

        patterns = [
            r"MLB-(\d+)",  # MLB-12345678
            r"/p/(MLB\d+)",  # /p/MLB12345678
            r"p/(MLB\d+)",  # p/MLB12345678
            r"/(MLB\d+)",  # /MLB12345678
            r"mercadolivre\.com\.br/([A-Za-z0-9-]+)-([A-Za-z0-9]+)",  # mercadolivre.com.br/produto-MLB12345678
            r"_Jm#position=(\d+)",  # _Jm#position=12345678
            r"mercadolivre\.com\.br/.*?-([A-Za-z0-9]{10,})",  # mercadolivre.com.br/produto-qualquer-MLB12345678
            r"mercadolivre\.com\.br/.*?-([A-Za-z0-9]{7,})",  # mercadolivre.com.br/produto-qualquer-MLB1234567
            r"mercadolivre\.com\.br/.*?-(MLB\d+)",  # mercadolivre.com.br/produto-qualquer-MLB12345678
            r"mercadolivre\.com\.br/.*?-(\d{7,})",  # mercadolivre.com.br/produto-qualquer-12345678
            r"mercadolivre\.com\.br/.*?-([A-Za-z0-9-]{10,})",  # mercadolivre.com.br/produto-qualquer-MLB-12345678
            r"mercadolivre\.com\.br/social/.*?(MLB\d+)",  # mercadolivre.com.br/social/...MLB12345678
        ]

        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                product_id = match.group(1)
                if product_id and product_id.isdigit():
                    product_id = f"MLB-{product_id}"
                elif product_id and not product_id.startswith("MLB"):
                    product_id = f"MLB-{product_id}"
                return product_id

        logger.warning(f"Não foi possível extrair o ID do produto da URL: {url}")
        return None

    async def scrape(self, url: str) -> Dict:
        """
        Raspa os detalhes do produto do Mercado Livre usando Scrapy
        """
        logger.info(f"Iniciando raspagem com Scrapy para URL: {url}")

        # Verificar se é uma URL de perfil social ou link de compartilhamento
        if "/social/" in url:
            logger.info(f"URL de perfil social detectada: {url}. Tentando extrair produtos da página social.")

            try:
                # Importa o módulo requests para fazer uma requisição HTTP
                import requests
                from bs4 import BeautifulSoup

                # Faz uma requisição HTTP para a página social
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36',
                    'Accept-Language': 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                }

                response = requests.get(url, headers=headers, timeout=30)

                if response.status_code != 200:
                    logger.warning(f"Erro ao acessar a página social: {response.status_code}")
                    return self._error_response(
                        "Não foi possível acessar a página social. Por favor, verifique se a URL está correta.",
                        url
                    )

                # Parseia o HTML
                soup = BeautifulSoup(response.text, 'html.parser')

                # Tenta encontrar links de produtos na página social
                product_links = []

                # Seletor para links de produto
                for selector in ['a[href*="/p/"]', 'a.poly-component__title', 'a.ui-pdp-link']:
                    links = soup.select(f'{selector}')
                    for link in links:
                        href = link.get('href')
                        if href and '/p/MLB' in href:
                            product_links.append(href)

                if not product_links:
                    logger.warning(f"Nenhum link de produto encontrado na página social: {url}")
                    return self._error_response(
                        "Esta URL é um link de perfil social ou compartilhamento, e não foi possível encontrar produtos nela. "
                        "Por favor, use uma URL direta de produto do Mercado Livre.",
                        url
                    )

                # Usa o primeiro link de produto encontrado
                product_url = product_links[0]
                logger.info(f"Link de produto encontrado na página social: {product_url}")

                # Raspa o produto encontrado
                return await self.scrape(product_url)

            except Exception as e:
                logger.error(f"Erro ao processar página social: {e}")
                return self._error_response(
                    f"Erro ao processar página social: {str(e)}",
                    url
                )

        try:
            # Usa o runner do Scrapy para raspar o produto
            products = await scrape_mercadolivre(url=url, max_pages=1)

            if not products:
                logger.warning(f"Nenhum produto encontrado para a URL: {url}")
                return self._error_response("Nenhum produto encontrado", url)

            # Pega o primeiro produto (deve ser o único para URLs de produto)
            product = products[0]

            # Extrai o ID do produto se não estiver presente
            if not product.get("product_id"):
                product_id = self.extract_product_id(url)
                if product_id:
                    product["product_id"] = product_id

            # Garante que todos os campos necessários estão presentes
            product.setdefault("platform", "Mercado Livre")
            product.setdefault("url_produto", url)
            # product.setdefault("url_afiliado", "") # Spider já define
            # product.setdefault("description", "") # Será preenchido pela IA
            product.setdefault("error", False)

            # --- Adicionar Enriquecimento com IA ---
            product_title = product.get("title")
            # Garante que os campos existam mesmo se a IA falhar ou o título não for encontrado
            product["description"] = self._ai_generator.fallback_description if self._ai_generator else ""
            product["categoryKey"] = None
            product["category"] = None
            product["subcategoryIndex"] = None
            product["subcategory"] = None

            if product_title and product_title != "Título não encontrado":
                logger.info(f"Tentando gerar conteúdo com IA para: {product_title}")
                
                try:
                    # Gerar Descrição
                    generated_description = await self.generate_product_description(product_title)
                    product["description"] = generated_description
                    logger.info(f"Descrição da IA: {generated_description}")

                    # Gerar Categoria
                    category_key = await self.generate_product_category(product_title)
                    product["categoryKey"] = category_key 
                    product["category"] = self.get_category_name(category_key) if category_key else None
                    logger.info(f"Chave da Categoria da IA: {category_key}, Nome: {product['category']}")

                    # Gerar Subcategoria (apenas se a categoria foi encontrada)
                    if category_key:
                        subcategory_idx = await self.generate_product_subcategory(product_title, category_key)
                        product["subcategoryIndex"] = subcategory_idx 
                        product["subcategory"] = self.get_subcategory_name(category_key, subcategory_idx) if subcategory_idx else None
                        logger.info(f"Índice da Subcategoria da IA: {subcategory_idx}, Nome: {product['subcategory']}")
                    else:
                        # Já definidos como None acima, mas para clareza
                        product["subcategoryIndex"] = None
                        product["subcategory"] = None
                        logger.info("Subcategoria não gerada pois a categoria não foi encontrada.")
                except Exception as ai_exc:
                    logger.error(f"Erro durante a geração de conteúdo com IA para '{product_title}': {ai_exc}", exc_info=True)
                    # Mantém os valores de fallback/None definidos anteriormente
            else:
                logger.warning("Título do produto não disponível ou inválido para geração de conteúdo com IA.")
                # Fallbacks já estão definidos

            logger.info(f"Raspagem com Scrapy e IA (se aplicável) concluída com sucesso para: {url}")
            return product

        except Exception as e:
            logger.error(f"Erro ao raspar com Scrapy: {e}")
            return self._error_response(f"Erro ao raspar com Scrapy: {str(e)}", url)
