import scrapy
import logging
import re
from datetime import datetime
from urllib.parse import urlparse, parse_qs, urlencode, urlunparse

logger = logging.getLogger(__name__)

class MercadoLivreSpider(scrapy.Spider):
    name = "mercadolivre"
    allowed_domains = ["mercadolivre.com.br", "mercadolibre.com", "mercadolibre.com.br"]

    def __init__(self, url=None, *args, **kwargs):
        super(MercadoLivreSpider, self).__init__(*args, **kwargs)
        self.start_urls = [url] if url else []
        self.logger.info(f"Iniciando spider para URL: {url}")

    def parse(self, response):
        self.logger.info(f"Processando página: {response.url}")

        # Verifica se é uma página social e extrai o link do produto
        if "/social/" in response.url:
            # Tenta encontrar links de produtos na página social
            product_links = response.css('a[href*="/p/"]::attr(href)').getall()
            if product_links:
                product_url = product_links[0]
                self.logger.info(f"URL do produto extraído da página social: {product_url}")
                yield scrapy.Request(product_url, callback=self.parse_product)
                return
            else:
                # Se não encontrou links de produtos, retorna um erro
                self.logger.warning(f"Não foi possível encontrar produtos na página social: {response.url}")
                yield {
                    "platform": "Mercado Livre",
                    "url_produto": response.url,
                    "url_afiliado": "",
                    "title": "URL de perfil social não suportada",
                    "description": "Esta URL é um link de perfil social ou compartilhamento, não um produto específico. Por favor, use uma URL direta de produto do Mercado Livre.",
                    "error": True,
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                return

        # Se não for página social, processa como produto
        yield self.parse_product(response)

    def parse_product(self, response):
        # Extrai o título do produto
        title = response.css('h1.ui-pdp-title::text').get()
        if not title:
            title = response.css('a.poly-component__title::text').get()
        if not title:
            title = response.css('h2.ui-search-item__title::text').get()
        if not title:
            title = response.css('h2.promotion-item__title::text').get()
        if not title:
            title = response.css('span.ui-search-item__title::text').get()
        if not title:
            title = response.css('.poly-component__title::text').get()
        if not title:
            title = response.css('title::text').get()

        if not title:
            self.logger.warning(f"Não foi possível extrair o título do produto: {response.url}")
            title = "Título não encontrado"

        # Extrai o preço atual
        price = None
        # Tenta um seletor mais específico para o preço principal, que não seja o riscado.
        # Ex: dentro de um container que não tenha a classe de preço antigo.
        # Prioriza seletores dentro de 'ui-pdp-price__main-container' ou 'ui-pdp-price__second-line'
        current_price_container = response.css('.ui-pdp-price__main-container')
        if not current_price_container:
            # Fallback para um container de preço mais genérico que não seja o de preço antigo
            current_price_container = response.css('.ui-pdp-price:not(:has(.andes-money-amount--previous))') # Exemplo, pode precisar de ajuste
            if not current_price_container: # Último fallback para o seletor original, mas com mais contexto
                 current_price_container = response.css('div.ui-pdp-price__second-line') # Outro comum

        if current_price_container:
            price_fraction = current_price_container.css('.andes-money-amount__fraction::text').get()
            price_cents = current_price_container.css('.andes-money-amount__cents::text').get()
            if price_fraction:
                price = f"R$ {price_fraction.strip()}"
                if price_cents:
                    price = f"{price},{price_cents.strip()}"
        
        if not price: # Fallback para o seletor original se os mais específicos falharem
            price_fraction = response.css('span.andes-money-amount:not(.andes-money-amount--previous) .andes-money-amount__fraction::text').get()
            price_cents = response.css('span.andes-money-amount:not(.andes-money-amount--previous) .andes-money-amount__cents::text').get()
            if price_fraction:
                price = f"R$ {price_fraction.strip()}"
                if price_cents:
                    price = f"{price},{price_cents.strip()}"
        
        if not price: # Fallback para price-tag-fraction, comum em algumas listagens
            price_fraction = response.css('.price-tag-fraction::text').get()
            if price_fraction:
                 price = f"R$ {price_fraction.strip()}"
                 # Tenta pegar os centavos se existirem próximos a esta estrutura
                 price_cents_sibling = response.xpath('//span[@class="price-tag-fraction"]/following-sibling::span[@class="price-tag-cents"]/text()').get()
                 if price_cents_sibling:
                     price = f"{price},{price_cents_sibling.strip()}"


        if not price: # Fallback final para o seletor mais genérico de price-tag
            price_text_list = response.css('span.price-tag-text-sr-only ~ span.price-tag-amount span.price-tag-fraction::text').getall()
            if not price_text_list: # Tenta sem o span.price-tag-amount
                 price_text_list = response.css('span.price-tag-text-sr-only ~ span.price-tag-fraction::text').getall()

            if price_text_list: # Pega o primeiro elemento que não seja o preço antigo
                for p_text in price_text_list:
                    # Verifica se o pai não é um elemento de preço antigo
                    parent_classes = response.xpath(f'//span[contains(text(), "{p_text}")]/ancestor::*[contains(@class, "ui-pdp-price__original-value") or contains(@class, "price-tag-amount-previous")]').get()
                    if not parent_classes:
                        price_fraction = p_text
                        price = f"R$ {price_fraction.strip()}"
                        # Tenta pegar os centavos
                        price_cents_node = response.xpath(f'//span[contains(text(), "{price_fraction}")]/following-sibling::span[@class="price-tag-cents"]/text()').get()
                        if price_cents_node:
                            price = f"{price},{price_cents_node.strip()}"
                        break # Encontrou o preço atual

        if not price: # Se ainda não encontrou, usa o seletor original como último recurso
            price_fraction = response.css('.andes-money-amount__fraction::text').get()
            price_cents = response.css('.andes-money-amount__cents::text').get()
            if price_fraction:
                price = f"R$ {price_fraction.strip()}"
                if price_cents:
                    price = f"{price},{price_cents.strip()}"
            else: # Fallback para span.price-tag-amount se tudo mais falhar
                 price_from_span = response.css('span.price-tag-amount::text').get()
                 if price_from_span:
                     price = price_from_span.strip()


        # Extrai o preço anterior
        old_price = None
        # Seletor para o preço antigo, geralmente dentro de um elemento com 'ui-pdp-price__original-value' ou similar
        old_price_container = response.css('.ui-pdp-price__original-value')
        if old_price_container:
            old_price_fraction = old_price_container.css('.andes-money-amount__fraction::text').get()
            old_price_cents = old_price_container.css('.andes-money-amount__cents::text').get()
            if old_price_fraction:
                old_price = f"R$ {old_price_fraction.strip()}"
                if old_price_cents:
                    old_price = f"{old_price},{old_price_cents.strip()}"
        
        if not old_price: # Fallback para o seletor original de preço antigo
            old_price_fraction = response.css('.andes-money-amount--previous .andes-money-amount__fraction::text').get()
            old_price_cents = response.css('.andes-money-amount--previous .andes-money-amount__cents::text').get()
            if old_price_fraction:
                old_price = f"R$ {old_price_fraction.strip()}"
                if old_price_cents:
                    old_price = f"{old_price},{old_price_cents.strip()}"

        if not old_price: # Fallback para span.price-tag-amount-previous
            old_price_from_span = response.css('span.price-tag-amount-previous::text').get()
            if old_price_from_span:
                old_price = old_price_from_span.strip()


        # Se o preço atual for igual ao preço antigo, e o preço antigo existir, tenta pegar o preço atual de um seletor diferente
        # Isso pode acontecer se o primeiro seletor de preço atual pegou o preço antigo por engano.
        if price and old_price and price == old_price:
            self.logger.info(f"Preço atual ({price}) igual ao preço antigo ({old_price}). Tentando seletor alternativo para preço atual.")
            alt_price_fraction = response.xpath('//div[contains(@class, "ui-pdp-price__second-line")]//span[@class="andes-money-amount__fraction"]/text()').get()
            alt_price_cents = response.xpath('//div[contains(@class, "ui-pdp-price__second-line")]//span[@class="andes-money-amount__cents"]/text()').get()
            if alt_price_fraction:
                new_price = f"R$ {alt_price_fraction.strip()}"
                if alt_price_cents:
                    new_price = f"{new_price},{alt_price_cents.strip()}"
                if new_price != old_price: # Garante que o novo preço seja diferente do antigo
                    price = new_price
                    self.logger.info(f"Preço atual corrigido para: {price}")
                else:
                    self.logger.warning(f"Seletor alternativo para preço atual resultou no mesmo valor do preço antigo: {new_price}")
            else: # Tenta outro seletor comum para preço principal
                price_from_price_tag = response.css('span.price-tag:not(:has(.price-tag-text-sr-only[aria-label*="Preço anterior"])) .price-tag-fraction::text').get()
                if price_from_price_tag:
                    new_price = f"R$ {price_from_price_tag.strip()}"
                    price_cents_from_tag = response.css('span.price-tag:not(:has(.price-tag-text-sr-only[aria-label*="Preço anterior"])) .price-tag-cents::text').get()
                    if price_cents_from_tag:
                        new_price = f"{new_price},{price_cents_from_tag.strip()}"
                    if new_price != old_price:
                        price = new_price
                        self.logger.info(f"Preço atual corrigido (price-tag) para: {price}")


        # Extrai a imagem
        image_url = None
        img_selector = response.css('.ui-pdp-gallery__figure img::attr(src)').get()
        if img_selector:
            image_url = img_selector

        if not image_url:
            img_selector = response.css('img.ui-pdp-image::attr(src)').get()
            if img_selector:
                image_url = img_selector

        if not image_url:
            img_selector = response.css('img.promotion-item__img::attr(src)').get()
            if img_selector:
                image_url = img_selector

        if not image_url:
            img_selector = response.css('img.poly-component__picture::attr(src)').get()
            if img_selector:
                image_url = img_selector

        if not image_url:
            img_selector = response.css('.poly-card__portada img::attr(src)').get()
            if img_selector:
                image_url = img_selector

        if not image_url:
            # Tenta qualquer imagem com D_NQ_NP
            img_selector = response.css('img[src*="D_NQ_NP"]::attr(src)').get()
            if img_selector:
                image_url = img_selector

        # Verifica se a imagem é o placeholder do carrinho de compras
        if image_url and "928445-MLA" in image_url:
            self.logger.warning(f"Imagem detectada como placeholder do carrinho: {image_url}")
            image_url = None

        # Extrai informações de parcelamento
        installments = response.css('span.poly-price__installments::text').get()
        if not installments:
            installments = response.css('span.ui-search-installments::text').get()
        if not installments:
            installments = response.css('span.promotion-item__installments::text').get()
        if not installments:
            installments = response.css('.poly-price__installments::text').get()
        if not installments:
            installments = "Consulte parcelas no site"

        # Extrai informações de cupom - Deixar em branco conforme solicitado
        coupon_info = ""

        # Extrai informações de frete
        shipping = "Verificar frete"
        shipping_selector = response.css('.poly-shipping--next_day::text').get()
        if shipping_selector:
            shipping_text = shipping_selector.lower()
            if "frete grátis" in shipping_text or "envio grátis" in shipping_text:
                shipping = "Com frete"

        # Extrai o ID do produto da URL
        product_id = None
        patterns = [
            r"MLB-(\d+)",  # MLB-12345678
            r"/p/(MLB\d+)",  # /p/MLB12345678
            r"p/(MLB\d+)",  # p/MLB12345678
            r"/(MLB\d+)",  # /MLB12345678
            r"mercadolivre\.com\.br/([A-Za-z0-9-]+)-([A-Za-z0-9]+)",  # mercadolivre.com.br/produto-MLB12345678
            r"_Jm#position=(\d+)",  # _Jm#position=12345678
            r"mercadolivre\.com\.br/.*?-([A-Za-z0-9]{10,})",  # mercadolivre.com.br/produto-qualquer-MLB12345678
            r"mercadolivre\.com\.br/.*?-([A-Za-z0-9]{7,})",  # mercadolivre.com.br/produto-qualquer-MLB1234567
            r"mercadolivre\.com\.br/.*?-(MLB\d+)",  # mercadolivre.com.br/produto-qualquer-MLB12345678
            r"mercadolivre\.com\.br/.*?-(\d{7,})",  # mercadolivre.com.br/produto-qualquer-12345678
            r"mercadolivre\.com\.br/.*?-([A-Za-z0-9-]{10,})",  # mercadolivre.com.br/produto-qualquer-MLB-12345678
            r"mercadolivre\.com\.br/social/.*?(MLB\d+)",  # mercadolivre.com.br/social/...MLB12345678
        ]

        for pattern in patterns:
            match = re.search(pattern, response.url)
            if match:
                product_id = match.group(1)
                if product_id and product_id.isdigit():
                    product_id = f"MLB-{product_id}"
                elif product_id and not product_id.startswith("MLB"):
                    product_id = f"MLB-{product_id}"
                break

        # Extrai a URL do produto
        product_url = response.css('link[rel="canonical"]::attr(href)').get()
        if not product_url:
            product_url = response.url

        # Limpa a URL do produto
        product_url = self.clean_product_url(product_url)

        # Retorna os dados do produto
        return {
            "platform": "Mercado Livre",
            "product_id": product_id,
            "url_produto": product_url,
            "url_afiliado": "",  # Deixa vazio conforme solicitado
            "title": title.strip() if title else "Título não encontrado",
            "description": "",  # Deixa vazio conforme solicitado
            "price": price.strip() if price else "Preço não disponível",
            "old_price": old_price.strip() if old_price else None,
            "image_url": image_url,
            "installments": installments.strip() if installments else "Consulte parcelas no site",
            "coupon_info": coupon_info, # Já está ""
            "shipping": shipping.strip() if shipping else "Verificar frete",
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            # Campos adicionais para compatibilidade com PromoHunter
            "ativo": True,
            "menor_preco": False,
            "indicamos": False,
            "disparar_whatsapp": False,
            "frete": False, # Este campo parece redundante com 'shipping', mas mantendo por compatibilidade
            "invalidProduct": False,
            "isStory": False,
            "cupom": coupon_info # Já está ""
        }

    def clean_product_url(self, url):
        """
        Limpa a URL do produto, removendo parâmetros de tracking e convertendo links de redirecionamento
        """
        if not url:
            return ""

        # Se for um link de redirecionamento do Mercado Livre
        if any(pattern in url for pattern in ["click1.mercadolivre.com", "click.mercadolivre.com", "clicks/external", "mclics"]):
            try:
                # Tenta extrair a URL real do parâmetro 'u'
                u_match = re.search(r'[?&]u=([^&]+)', url)
                if u_match:
                    real_url = u_match.group(1)
                    # Decodifica a URL
                    import urllib.parse
                    real_url = urllib.parse.unquote(real_url)
                    # Limpa a URL resultante
                    return self.clean_product_url(real_url)

                # Tenta extrair o ID do produto do parâmetro wid
                wid_match = re.search(r'wid=([^&]+)', url)
                if wid_match:
                    product_id = wid_match.group(1)
                    # Constrói a URL direta do produto
                    clean_url = f"https://www.mercadolivre.com.br/p/{product_id}"
                    return clean_url

                # Tenta extrair o ID do produto do parâmetro MLB
                mlb_match = re.search(r'MLB[\d-]+', url)
                if mlb_match:
                    product_id = mlb_match.group(0)
                    # Constrói a URL direta do produto
                    clean_url = f"https://www.mercadolivre.com.br/p/{product_id}"
                    return clean_url
            except Exception as e:
                self.logger.error(f"Erro ao processar URL de redirecionamento: {e}")
                return url

        # Para URLs diretas, remove parâmetros desnecessários
        try:
            parsed = urlparse(url)

            # Remove fragmentos (tudo após #)
            clean_url = url.split('#')[0]

            # Se for uma URL de produto do Mercado Livre, simplifica
            if "/p/MLB" in parsed.path:
                return clean_url

            # Remove parâmetros de tracking
            params_to_remove = [
                "ref", "ref_", "tag", "_encoding", "psc", "pd_rd_w", "pd_rd_r",
                "pd_rd_wg", "sprefix", "keywords", "crid", "dchild", "qid", "sr",
                "th", "spm", "trk", "smid", "asc_source", "asc_campaign", "asc_refurl",
                "searchVariation", "position", "tracking_id", "source_id", "component_id",
                "item_id", "category_id", "official_store_id", "pdp_filters", "dealer_id",
                "force_landing_page"
            ]

            query_params = parse_qs(parsed.query, keep_blank_values=True)
            filtered_params = {
                k: v for k, v in query_params.items()
                if k.lower() not in params_to_remove
                and not k.lower().startswith("asc_")
                and not k.lower().startswith("pd_rd_")
                and not k.lower().startswith("pf_rd_")
                and not k.lower().startswith("ref_")
                and not k.lower().startswith("sp_")
            }

            new_query = urlencode(filtered_params, doseq=True)

            clean_url = urlunparse((
                parsed.scheme,
                parsed.netloc,
                parsed.path.rstrip("/"),
                parsed.params,
                new_query,
                ""
            ))

            if clean_url.endswith("?"):
                clean_url = clean_url[:-1]

            return clean_url
        except Exception as e:
            self.logger.error(f"Erro ao limpar URL: {e}")

        return url
