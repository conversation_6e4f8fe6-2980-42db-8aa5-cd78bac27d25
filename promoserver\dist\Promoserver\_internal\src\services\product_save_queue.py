import asyncio
import logging
from typing import Dict, Any, Optional, List, Callable
from src.models.product_supabase_get import ProdutoSupabaseGet

log = logging.getLogger(__name__)


class ProductSaveQueue:
    """
    Classe para gerenciar uma fila de salvamento de produtos.
    Garante que os produtos sejam salvos sequencialmente, evitando problemas de concorrência.
    """

    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ProductSaveQueue, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if not hasattr(self, "_initialized") or not self._initialized:
            self._queue = asyncio.Queue()
            self._processing = False
            self._lock = asyncio.Lock()
            self._initialized = True
            log.info("Fila de salvamento de produtos inicializada.")

    async def add_to_queue(
        self,
        product_data: Dict[str, Any],
        save_function: Callable[[Dict[str, Any]], ProdutoSupabaseGet],
        callback: Optional[
            Callable[[Optional[ProdutoSupabaseGet], Optional[Exception]], None]
        ] = None,
    ) -> None:
        """
        Adiciona um produto à fila de salvamento.

        Args:
            product_data: Dados do produto a ser salvo
            save_function: Função que será chamada para salvar o produto
            callback: Função opcional que será chamada quando o produto for salvo
        """
        await self._queue.put((product_data, save_function, callback))
        log.info(
            f"Produto '{product_data.get('titulo', 'Sem título')}' adicionado à fila de salvamento."
        )

        # Inicia o processamento da fila se ainda não estiver em andamento
        if not self._processing:
            asyncio.create_task(self._process_queue())

    async def _process_queue(self) -> None:
        """
        Processa a fila de salvamento, garantindo que apenas um produto seja salvo por vez.
        """
        async with self._lock:
            if self._processing:
                return
            self._processing = True

        try:
            while not self._queue.empty():
                product_data, save_function, callback = await self._queue.get()

                try:
                    log.info(
                        f"Processando salvamento do produto: {product_data.get('titulo', 'Sem título')}"
                    )
                    result = await save_function(product_data)

                    if callback:
                        callback(result, None)

                    log.info(
                        f"Produto salvo com sucesso: {product_data.get('titulo', 'Sem título')}"
                    )
                except Exception as e:
                    log.exception(f"Erro ao salvar produto: {e}")
                    if callback:
                        callback(None, e)
                    # Registrar explicitamente que o produto falhou ao ser salvo
                    log.error(
                        f"Falha ao salvar o produto: {product_data.get('titulo', 'Sem título')}"
                    )

                self._queue.task_done()

                # Pequena pausa para garantir que o próximo produto não seja processado imediatamente
                await asyncio.sleep(0.5)
        finally:
            self._processing = False

            # Se novos itens foram adicionados durante o processamento, reinicia o processamento
            if not self._queue.empty():
                asyncio.create_task(self._process_queue())

    async def get_queue_size(self) -> int:
        """
        Retorna o tamanho atual da fila.

        Returns:
            int: Número de produtos na fila
        """
        return self._queue.qsize()

    async def is_processing(self) -> bool:
        """
        Verifica se a fila está sendo processada.

        Returns:
            bool: True se a fila estiver sendo processada, False caso contrário
        """
        return self._processing
