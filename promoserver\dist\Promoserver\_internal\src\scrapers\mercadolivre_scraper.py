"""Spider rápido do Scrapy para o Mercado Livre

Este spider é uma versão simplificada e otimizada para raspagem rápida de produtos do Mercado Livre.
Baseado no spider original do PromoHunter antigo.
"""

import scrapy
import logging
import re
from datetime import datetime
from scrapy import Request
from ..items import ProductItem

logger = logging.getLogger(__name__)


class MercadoLivreFastSpider(scrapy.Spider):
    """Spider rápido para extrair produtos do Mercado Livre"""

    name = "mercadolivre_fast"
    allowed_domains = ["mercadolivre.com.br", "mercadolibre.com"]

    custom_settings = {
        "DOWNLOAD_DELAY": 3,  # Aumentado para 3 segundos como no ml.py original
        "RANDOMIZE_DOWNLOAD_DELAY": True,
        "REDIRECT_ENABLED": True,
        "REDIRECT_MAX_TIMES": 5,
        "LOG_FORMAT": "%(message)s",  # Simplificado como no ml.py original
        "CONCURRENT_REQUESTS": 4,  # Reduzido para evitar bloqueios
        "CONCURRENT_REQUESTS_PER_DOMAIN": 2,  # Reduzido para evitar bloqueios
        "DOWNLOAD_TIMEOUT": 30,  # Aumentado para dar mais tempo para carregar
        "HTTPCACHE_ENABLED": True,
        "HTTPCACHE_EXPIRATION_SECS": 86400,  # 24 horas
        "COOKIES_ENABLED": True,  # Habilitado como no ml.py original
        "USER_AGENT": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36",
        "DEFAULT_REQUEST_HEADERS": {
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7",
            "Referer": "https://www.mercadolivre.com.br/",
            "Sec-Ch-Ua": '"Google Chrome";v="123", "Not:A-Brand";v="8"',
            "Sec-Ch-Ua-Mobile": "?0",
            "Sec-Ch-Ua-Platform": '"Windows"',
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-User": "?1",
            "Upgrade-Insecure-Requests": "1"
        }
    }

    def __init__(self, url=None, category=None, max_pages=2, *args, **kwargs):
        """Inicializa o spider

        Args:
            url: URL específica para iniciar o scraping
            category: Categoria para iniciar o scraping
            max_pages: Número máximo de páginas a serem raspadas
        """
        super(MercadoLivreFastSpider, self).__init__(*args, **kwargs)
        self.max_pages = int(max_pages)
        self.products_processed = 0
        self.pages_processed = 0

        # Inicializa a lista de URLs
        self.start_urls = []

        # Se uma URL específica foi fornecida, usa apenas ela
        if url:
            # Usa diretamente a URL fornecida sem modificações
            self.start_urls.append(url)
            logger.info(f"URL específica adicionada: {url}")

        # Se uma categoria foi fornecida, usa-a diretamente
        # Assume que category já é uma URL completa do Mercado Livre
        elif category:
            self.start_urls.append(category)
            logger.info(f"URL de categoria adicionada: {category}")

        # Se nenhuma URL foi fornecida, usa uma URL padrão de ofertas
        if not self.start_urls:
            default_url = 'https://www.mercadolivre.com.br/ofertas'
            self.start_urls.append(default_url)
            logger.info(f"Usando URL padrão de ofertas: {default_url}")

        logger.info(f"Spider iniciado com {len(self.start_urls)} URLs: {self.start_urls}")
        self.is_single_product_scrape = len(self.start_urls) == 1 and 'produto.mercadolivre.com.br' in self.start_urls[0]

    def start_requests(self):
        """Inicia as requisições"""
        # Primeiro, faz uma requisição para registrar os headers usados (como no ml.py original)
        yield scrapy.Request(
            url='https://www.mercadolivre.com.br/',
            callback=self.parse,
            dont_filter=True,
            meta={'log_headers': True}
        )

        # Se for raspagem de produto único, usa parse_product_page como callback
        if self.is_single_product_scrape:
            url = self.start_urls[0]
            logger.info(f"Iniciando raspagem de produto único para: {url}")
            yield Request(
                url=url,
                callback=self.parse_product_page,
                errback=self.errback_httpbin,
                dont_filter=True
            )
        else:
            # Processa cada URL inicial para raspagem de listagem
            for url in self.start_urls:
                # Substitui & por & para garantir que a URL seja processada corretamente
                url = url.replace("&", "&")

                # Remove caracteres especiais e espaços que possam estar na URL
                url = url.strip()

                # Remove o &amp no final da URL se existir
                if url.endswith("&amp"):
                    url = url[:-4]

                # Limpa a URL para garantir que seja válida
                url = url.replace(" ", "%20")

                # Log detalhado da URL formatada
                logger.info(f"URL formatada: {url}")

                # Verifica se a URL já tem o parâmetro de página
                if "page=" not in url:
                    # Adiciona o parâmetro de página se não existir
                    if "?" in url:
                        url = f"{url}&page=1"
                    else:
                        url = f"{url}?page=1"

                logger.info(f"Iniciando requisição para URL formatada: {url}")

                # Processa cada página até o limite definido (como no ml.py original)
                for page in range(1, self.max_pages + 1):
                    # Constrói a URL da página
                    page_url = url
                    if "page=" in page_url:
                        page_url = re.sub(r"page=\d+", f"page={page}", page_url)
                    else:
                        if "?" in page_url:
                            page_url = f"{page_url}?page={page}"
                        else:
                            page_url = f"{page_url}?page={page}"

                    logger.info(f"Processando página {page}: {page_url}")

                    yield Request(
                        url=page_url,
                        callback=self.parse,
                        errback=self.errback_httpbin,
                        meta={"page": page},
                        dont_filter=True
                    )

    def parse(self, response):
        """Processa uma página de listagem de produtos"""
        # Verifica se é uma requisição para registrar headers
        if response.meta.get('log_headers'):
            logger.info(f"Headers usados: {response.request.headers}")
            return

        page = response.meta.get("page", 1)
        logger.info(f"Recebida página de listagem {page} - Status: {response.status}")

        # Salva o HTML da página para debug
        with open(f"ml_page_{page}.html", "w", encoding="utf-8") as f:
            f.write(response.text)
        logger.info(f"HTML da página {page} salvo em ml_page_{page}.html")

        # Extrai produtos da página usando seletores específicos para páginas de ofertas do Mercado Livre
        products = response.css('div.promotion-item')
        logger.info(f"Seletor 'div.promotion-item' encontrou {len(products)} produtos")

        # Se não encontrou produtos, tenta o seletor do ml.py original
        if not products or len(products) == 0:
            products = response.css('div.ui-search-result__wrapper')
            logger.info(f"Seletor 'div.ui-search-result__wrapper' encontrou {len(products)} produtos")

        # Se ainda não encontrou produtos, tenta outros seletores conhecidos
        if not products or len(products) == 0:
            products = response.css(
                "div.ui-search-result, div.andes-card.poly-card, li.ui-search-layout__item"
            )
            logger.info(f"Seletores alternativos encontraram {len(products)} produtos")

        # Log detalhado para debug
        logger.info(f"Seletores para layout 2024 encontraram {len(products)} produtos")

        # Se não encontrou produtos, tenta seletores do layout anterior
        if not products or len(products) == 0:
            logger.warning("Tentando seletores para layout anterior...")
            products = response.css(
                ".ui-search-layout__item, .ui-search-result__wrapper, .andes-card, div.ui-search-result, .ui-search-layout__item, .ui-search-result, .shops__item, .promotion-item"
            )
            logger.info(
                f"Seletores para layout anterior encontraram {len(products)} produtos"
            )

        # Se não encontrou produtos, tenta seletores específicos para o novo layout do ML
        if not products or len(products) == 0:
            logger.warning("Tentando seletores para o novo layout do ML...")
            products = response.css(
                "div.poly-card, div.andes-card--flat, div.andes-card--animated"
            )
            logger.info(
                f"Seletores para novo layout encontraram {len(products)} produtos"
            )

        # Se ainda não encontrou produtos, tenta outros seletores
        if not products or len(products) == 0:
            logger.warning("Tentando seletores alternativos...")
            products = response.css(
                'div[class*="search-result"], div[class*="promotion"], div[class*="item"], div[class*="poly-card"], div[class*="andes-card"]'
            )
            logger.info(f"Seletores alternativos encontraram {len(products)} produtos")

        # Se ainda não encontrou produtos, tenta seletores mais genéricos
        if not products or len(products) == 0:
            logger.warning("Tentando seletores mais genéricos...")
            products = response.css(
                "div.item, div.promotion, div.product, div.offer, div.card, div.result, div.search-result, div.product-item, div.product-card, div.product-container"
            )
            logger.info(f"Seletores genéricos encontraram {len(products)} produtos")

        # Se ainda não encontrou produtos, tenta qualquer div com link e imagem
        if not products or len(products) == 0:
            logger.warning("Tentando qualquer div com link e imagem...")
            products = response.css("div:has(a):has(img)")
            logger.info(
                f"Seletores de div com link e imagem encontraram {len(products)} produtos"
            )

        # Se ainda não encontrou produtos, tenta extrair diretamente do HTML
        if not products or len(products) == 0:
            logger.warning("Tentando extrair produtos diretamente do HTML...")
            products = self.extract_products_from_html(response.text)
            logger.info(f"Extração direta do HTML encontrou {len(products)} produtos")

        # Log detalhado para debug
        logger.info(f"URL da página: {response.url}")
        logger.info(f"Status da página: {response.status}")
        logger.info(f"Tamanho do HTML: {len(response.text)} bytes")

        # Verifica se há produtos na página
        if len(products) > 0:
            logger.info(f"Encontrados {len(products)} produtos na página {page}")

            # Log de exemplo do primeiro produto
            if len(products) > 0:
                first_product = products[0]
                logger.info(
                    f"Exemplo de produto: {first_product.css('h2::text').get() or first_product.css('.ui-search-item__title::text').get() or 'Sem título'}"
                )
                logger.info(
                    f"URL do produto: {first_product.css('a::attr(href)').get() or 'Sem URL'}"
                )

        # Verifica se a página contém um redirecionamento ou uma página de erro
        if (
            "Ops! Página não encontrada." in response.text
            or "Algo deu errado" in response.text
        ):
            logger.error(f"Página de erro detectada: {response.url}")
            # Tenta uma URL alternativa
            alternative_url = (
                "https://www.mercadolivre.com.br/ofertas/celulares-telefones"
            )
            logger.info(f"Tentando URL alternativa: {alternative_url}")
            yield Request(
                url=alternative_url,
                callback=self.parse,
                errback=self.errback_httpbin,
                meta={"page": page},
                headers={
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36",
                },
                dont_filter=True,
            )
        logger.info(f"Seletores encontrados: {len(products)}")
        logger.debug(f"HTML da página: {response.text[:1000]}...")

        # Salva o HTML para debug
        with open(f"debug_page_{page}.html", "w", encoding="utf-8") as f:
            f.write(response.text)

        if not products:
            logger.warning(f"Nenhum produto encontrado na página {page}")
            logger.debug(f"URL: {response.url}")
            return

        logger.info(f"Encontrados {len(products)} produtos na página {page}")

        for idx, product in enumerate(products, 1):
            try:
                # Extrai informações básicas do produto
                # Primeiro tenta os seletores para páginas de ofertas
                title = product.css('.promotion-item__title::text').get()
                url = product.css('.promotion-item__link-container::attr(href)').get()

                # Se não encontrou, tenta os seletores do ml.py original
                if not title:
                    title = product.css('h2.ui-search-item__title::text').get()

                if not url:
                    url = product.css('a.ui-search-item__group__element::attr(href)').get()

                # Se ainda não encontrou, tenta outros seletores conhecidos
                if not title:
                    title = (
                        product.css(".ui-search-item__title::text").get()
                        or product.css(".shops__item-title::text").get()
                        or product.css(".poly-component__title::text").get()
                        or product.css("h2::text").get()
                    )

                if not url:
                    url = (
                        product.css(".ui-search-link::attr(href)").get()
                        or product.css(".ui-search-result__content a::attr(href)").get()
                        or product.css(".poly-component__title::attr(href)").get()
                        or product.css("a::attr(href)").get()
                    )

                # Log detalhado para debug
                logger.info(f"Extraindo produto: Título={title}, URL={url}")

                # Log detalhado para debug
                logger.info(f"Produto {idx}: {title if title else 'Sem título'}")
                logger.debug(f"HTML do produto: {product.get()}")

                if not title or not url:
                    logger.warning(f"Produto {idx}: Dados incompletos")
                    continue

                # Extrai preço atual - primeiro tenta seletores para páginas de ofertas
                price = product.css('.promotion-item__price .andes-money-amount__fraction::text').get()

                # Se não encontrou, tenta outros seletores conhecidos
                if not price:
                    price = (
                        product.css(".andes-money-amount__fraction::text").get()
                        or product.css(".price-tag-fraction::text").get()
                        or product.css(
                            ".poly-price__current .andes-money-amount__fraction::text"
                        ).get()
                        or product.css(
                            ".ui-search-price__part--medium .andes-money-amount__fraction::text"
                        ).get()
                    )

                # Extrai preço antigo - primeiro tenta seletores para páginas de ofertas
                old_price = product.css('.promotion-item__price .andes-money-amount--previous .andes-money-amount__fraction::text').get()

                # Se não encontrou, tenta outros seletores conhecidos
                if not old_price:
                    old_price = (
                        product.css("s .andes-money-amount__fraction::text").get()
                        or product.css(".price-tag-fraction-old::text").get()
                        or product.css(
                            ".andes-money-amount--previous .andes-money-amount__fraction::text"
                        ).get()
                        or product.css(
                            ".ui-search-price__discount .price-tag-fraction::text"
                        ).get()
                    )

                # Extrai URL da imagem - primeiro tenta seletores para páginas de ofertas
                image_url = product.css('.promotion-item__img::attr(src)').get()

                # Se não encontrou, tenta outros seletores conhecidos
                if not image_url:
                    image_url = (
                        product.css(
                            ".ui-search-result-image__element img::attr(data-src)"
                        ).get()
                        or product.css(
                            ".ui-search-result-image__element img::attr(src)"
                        ).get()
                        or product.css(".slick-slide.slick-active img::attr(src)").get()
                        or product.css(".poly-component__picture::attr(src)").get()
                        or product.css("img::attr(data-src)").get()
                        or product.css("img::attr(src)").get()
                    )

                # Log detalhado para debug
                logger.info(f"Preço: {price}, Preço antigo: {old_price}, Imagem: {image_url}")

                # Extrai informações de frete - tenta todos os seletores conhecidos
                shipping = (
                    product.css(".ui-search-item__shipping-free::text").get()
                    or product.css(".poly-component__shipping::text").get()
                    or product.css(
                        ".ui-search-item__shipping .ui-search-item__shipping-free::text"
                    ).get()
                )

                # Extrai informações de cupom/desconto - tenta todos os seletores conhecidos
                coupon = (
                    product.css(".ui-search-price__discount::text").get()
                    or product.css(".andes-money-amount__discount::text").get()
                    or product.css(
                        ".poly-price__current .andes-money-amount__discount::text"
                    ).get()
                )

                logger.info(f"Processando produto {idx}/{len(products)}: {title}")
                self.products_processed += 1

                # Verifica se é uma URL de produto válida
                if url and ("mercadolivre.com.br" in url or "mercadolibre.com" in url):
                    # Cria um item do produto com informações básicas da listagem
                    item = ProductItem()
                    item["title"] = title.strip() if title else ""
                    item["url"] = url
                    item["price"] = price.strip() if price else ""
                    item["old_price"] = old_price.strip() if old_price else ""
                    item["image_url"] = image_url
                    item["shipping"] = shipping.strip() if shipping else ""
                    item["coupon"] = coupon.strip() if coupon else ""
                    item["store"] = "mercadolivre"
                    item["timestamp"] = datetime.now().isoformat()
                    item["spider"] = self.name
                    item["page"] = page
                    item["position"] = idx

                    # Retorna o item diretamente da listagem para ser mais rápido
                    yield item

                    # Opcional: seguir o link para a página do produto para obter mais detalhes
                    # Descomente as linhas abaixo se quiser obter mais detalhes de cada produto
                    # yield Request(
                    #     url=url,
                    #     callback=self.parse_product_page,
                    #     errback=self.errback_httpbin,
                    #     meta={'page': page, 'position': idx},
                    #     dont_filter=True
                    # )

            except Exception as e:
                logger.error(f"Erro ao processar produto {idx}: {str(e)}")

        self.pages_processed += 1
        logger.info(
            f"Página {page} concluída. Progresso: {self.products_processed} produtos, {self.pages_processed} páginas"
        )

        # Segue para a próxima página se não atingiu o limite
        if page < self.max_pages:
            # Tenta encontrar o botão de próxima página
            next_page = response.css(
                ".andes-pagination__button--next a::attr(href)"
            ).get()

            # Se não encontrou o botão, tenta construir a URL da próxima página
            if not next_page:
                # Extrai a URL base
                current_url = response.url
                # Verifica se já tem o parâmetro page
                if "page=" in current_url:
                    # Substitui o número da página
                    next_page = re.sub(r"page=\d+", f"page={page+1}", current_url)
                else:
                    # Adiciona o parâmetro page
                    if "?" in current_url:
                        next_page = f"{current_url}&page={page+1}"
                    else:
                        next_page = f"{current_url}?page={page+1}"

            if next_page:
                logger.info(f"Seguindo para a página {page+1}: {next_page}")
                yield Request(
                    url=next_page,
                    callback=self.parse,
                    errback=self.errback_httpbin,
                    meta={"page": page + 1},
                    dont_filter=True,
                )
            else:
                logger.info(f"Não há mais páginas para processar após a página {page}")
        else:
            logger.info(f"Atingido o limite de {self.max_pages} páginas. Finalizando.")

    def parse_product_page(self, response):
        """Processa uma página de produto individual"""
        logger.info(f"Processando página de produto: {response.url}")

        # Cria um item do produto
        item = ProductItem()

        # Extrai informações básicas do produto - primeiro tenta o layout mais recente (abril 2024)
        title = response.css(".ui-pdp-title::text").get()
        if not title:
            title = response.css(
                "h1.ui-pdp-title::text, .item-title::text, .andes-card h1::text"
            ).get()

        # Extrai preço atual - primeiro tenta o layout mais recente (abril 2024)
        price = response.css(".ui-pdp-price .andes-money-amount__fraction::text").get()
        if not price:
            price = response.css(
                ".andes-money-amount__fraction::text, .price-tag-fraction::text, .ui-pdp-price__second-line .andes-money-amount__fraction::text"
            ).get()

        # Extrai preço antigo - primeiro tenta o layout mais recente (abril 2024)
        old_price = response.css("s .andes-money-amount__fraction::text").get()
        if not old_price:
            old_price = response.css(
                ".andes-money-amount--previous .andes-money-amount__fraction::text, .price-tag-fraction-old::text, .ui-pdp-price__original-value .andes-money-amount__fraction::text"
            ).get()

        # Extrai URL da imagem - primeiro tenta o layout mais recente (abril 2024)
        image_url = response.css(".ui-pdp-image img::attr(data-zoom)").get()
        if not image_url:
            image_url = response.css(
                ".ui-pdp-gallery__figure img::attr(src), .ui-pdp-image img::attr(src), .ui-pdp-gallery img::attr(src), img::attr(data-zoom), img::attr(src)"
            ).get()

        # Extrai informações de cupom/desconto - primeiro tenta o layout mais recente (abril 2024)
        coupon = response.css(".ui-pdp-price__discount::text").get()
        if not coupon:
            coupon = response.css(
                ".ui-pdp-price__discount-label::text, .andes-money-amount__discount::text"
            ).get()

        # Extrai informações de frete - primeiro tenta o layout mais recente (abril 2024)
        shipping = response.css(".ui-pdp-color--GREEN::text").get()
        if not shipping:
            shipping = response.css(".ui-pdp-shipping--free::text").get()

        # Preenche o item
        item["title"] = title.strip() if title else ""
        item["url"] = response.url
        item["price"] = price.strip() if price else ""
        item["old_price"] = old_price.strip() if old_price else ""
        item["image_url"] = image_url
        item["coupon"] = coupon.strip() if coupon else ""
        item["shipping"] = shipping.strip() if shipping else ""
        item["store"] = "mercadolivre"
        item["timestamp"] = datetime.now().isoformat()
        item["spider"] = self.name

        logger.info(f"Produto processado: {title}")
        return item

    def errback_httpbin(self, failure):
        """Trata erros de requisição"""
        logger.error(f"Falha na requisição: {str(failure.value)}")
        logger.error(f"URL: {failure.request.url}")

    def closed(self, reason):
        """Método chamado quando o spider é finalizado"""
        logger.info(f"Spider finalizado: {reason}")
        logger.info(f"Resumo: {self.pages_processed} páginas, {self.products_processed} produtos")

    def extract_products_from_html(self, html_text):
        """Extrai produtos diretamente do HTML usando expressões regulares"""
        import re
        from scrapy.selector import Selector

        # Cria uma lista para armazenar os produtos
        products = []

        # Procura por divs de produtos no formato mais recente do ML (abril 2024)
        poly_card_divs = re.findall(
            r'<div class="andes-card poly-card[^>]*>.*?</div></div>',
            html_text,
            re.DOTALL,
        )

        logger.info(
            f"Encontrados {len(poly_card_divs)} divs de produtos no formato mais recente do ML"
        )

        # Cria seletores para cada div de produto encontrada
        for i, div in enumerate(poly_card_divs):
            try:
                # Cria um seletor a partir do HTML
                selector = Selector(text=div)
                product_selector = selector.css("div")
                if product_selector:
                    products.append(product_selector[0])
            except Exception as e:
                logger.error(f"Erro ao criar seletor para produto {i}: {str(e)}")

        # Se não encontrou produtos no formato mais recente, tenta extrair títulos, links, preços e imagens
        if not products:
            # Procura por links de produtos
            product_links = re.findall(
                r'href="(https://[^"]*mercadoli[^"]*?)"', html_text
            )

            # Procura por títulos de produtos no formato mais recente (abril 2024)
            product_titles = re.findall(
                r'class="poly-component__title"[^>]*>([^<]+)</a>', html_text
            )

            # Se não encontrou títulos no formato mais recente, tenta o formato anterior
            if not product_titles:
                product_titles = re.findall(r"<h2[^>]*>([^<]+)</h2>", html_text)

            # Procura por preços no formato mais recente (abril 2024)
            product_prices = re.findall(
                r'class="poly-price__current".*?class="andes-money-amount__fraction"[^>]*>([^<]+)</span>',
                html_text,
                re.DOTALL,
            )

            # Se não encontrou preços no formato mais recente, tenta o formato anterior
            if not product_prices:
                product_prices = re.findall(
                    r'class="andes-money-amount__fraction"[^>]*>([^<]+)</span>',
                    html_text,
                )

            if not product_prices:
                product_prices = re.findall(
                    r'class="[^"]*price[^"]*"[^>]*>([^<]+)</span>', html_text
                )

            # Procura por imagens no formato mais recente (abril 2024)
            product_images = re.findall(
                r'class="poly-component__picture"[^>]*src="([^"]+)"', html_text
            )

            # Se não encontrou imagens no formato mais recente, tenta o formato anterior
            if not product_images:
                product_images = re.findall(r'<img[^>]*src="([^"]+)"[^>]*>', html_text)

            logger.info(
                f"Encontrados via regex: {len(product_links)} links, {len(product_titles)} títulos, {len(product_prices)} preços, {len(product_images)} imagens"
            )

            # Cria seletores para cada produto encontrado
            max_products = (
                min(len(product_links), 100) if product_links else 0
            )  # Aumentado para 100 produtos
            for i in range(max_products):
                try:
                    # Cria um HTML simples para o produto
                    product_html = f"<div class='product'>"
                    if i < len(product_titles):
                        product_html += f"<h2>{product_titles[i]}</h2>"
                    if i < len(product_links):
                        product_html += f"<a href='{product_links[i]}'>Link</a>"
                    if i < len(product_prices):
                        product_html += (
                            f"<span class='price'>{product_prices[i]}</span>"
                        )
                    if i < len(product_images):
                        product_html += f"<img src='{product_images[i]}' />"
                    product_html += "</div>"

                    # Cria um seletor a partir do HTML
                    selector = Selector(text=product_html)
                    product_selector = selector.css("div.product")
                    if product_selector:
                        products.append(product_selector[0])

                except Exception as e:
                    logger.error(f"Erro ao criar seletor para produto {i}: {str(e)}")

        return products

    def closed(self, reason):
        """Executado quando o spider é fechado"""
        logger.info(f"Spider finalizado: {reason}")
        logger.info(
            f"Resumo: {self.pages_processed} páginas, {self.products_processed} produtos"
        )
