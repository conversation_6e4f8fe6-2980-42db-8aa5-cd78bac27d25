import time
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, simpledialog
import subprocess
import threading
import queue
import os
import sys
import json
import socket
from pathlib import Path
import pystray
import psutil
from PIL import Image, ImageTk
from src.utils.log_manager import get_log_manager

# Adicionar o diretório base ao sys.path
if getattr(sys, "frozen", False):
    # Rodando empacotado: pega a pasta do executável
    BASE_DIR = Path(sys.executable).parent.resolve()
    # No executável empacotado, o api_main.py está em _internal
    API_MAIN_SCRIPT = BASE_DIR / "_internal" / "api_main.py"
else:
    BASE_DIR = Path(__file__).parent.resolve()
    API_MAIN_SCRIPT = BASE_DIR / "api_main.py"
sys.path.insert(0, str(BASE_DIR))

# Importar o gerenciador de logs

VENV_PYTHON_WIN = BASE_DIR / ".venv" / "Scripts" / "python.exe"
VENV_PYTHON_POSIX = BASE_DIR / "venv" / "bin" / "python"  # Linux/macOS

# Corrige: quando empacotado, usar o Python do venv ou do sistema, nunca o próprio executável
if getattr(sys, "frozen", False):
    # Só permite rodar se o venv existir
    if VENV_PYTHON_WIN.exists():
        PYTHON_EXE = str(VENV_PYTHON_WIN)
    else:
        PYTHON_EXE = None
else:
    PYTHON_EXE = str(VENV_PYTHON_WIN if os.name == "nt" else VENV_PYTHON_POSIX)
# Porta padrão para o servidor
SERVER_PORT = 8000


# Função para construir argumentos do uvicorn
def get_uvicorn_command():
    """Constrói o comando uvicorn baseado no ambiente."""

    # Determinar o módulo correto baseado no ambiente
    if getattr(sys, "frozen", False):
        # Executável empacotado: usar caminho relativo ao _internal
        module_name = "_internal.api_main:app"
    else:
        # Desenvolvimento: usar nome do módulo normal
        module_name = f"{API_MAIN_SCRIPT.stem}:app"

    if os.name == "nt":
        # Windows: detectar se está empacotado
        if getattr(sys, "frozen", False):
            # Executável empacotado: SEMPRE usar python -m uvicorn para garantir o ambiente correto
            python_exe = BASE_DIR / ".venv" / "Scripts" / "python.exe"

            # Verificar se o python empacotado existe
            if not python_exe.exists():
                raise FileNotFoundError(
                    f"Python empacotado não encontrado: {python_exe}"
                )

            return [
                str(python_exe),
                "-m",
                "uvicorn",
                module_name,
                "--host",
                "0.0.0.0",
                "--port",
                str(SERVER_PORT),
                "--log-level",
                "debug",
                "--reload",
                "--reload-dir",
                str(BASE_DIR),
            ]
        else:
            # Desenvolvimento: tentar uvicorn.exe primeiro, depois python -m uvicorn
            uvicorn_exe = BASE_DIR / ".venv" / "Scripts" / "uvicorn.exe"

            if uvicorn_exe.exists():
                return [
                    str(uvicorn_exe),
                    module_name,
                    "--host",
                    "0.0.0.0",
                    "--port",
                    str(SERVER_PORT),
                    "--log-level",
                    "debug",
                    "--reload",
                    "--reload-dir",
                    str(BASE_DIR),
                ]
            else:
                # Fallback: usar python -m uvicorn
                python_exe = VENV_PYTHON_WIN
                return [
                    str(python_exe),
                    "-m",
                    "uvicorn",
                    module_name,
                    "--host",
                    "0.0.0.0",
                    "--port",
                    str(SERVER_PORT),
                    "--log-level",
                    "debug",
                    "--reload",
                    "--reload-dir",
                    str(BASE_DIR),
                ]
    else:
        # Linux/macOS
        return [
            "uvicorn",
            module_name,
            "--host",
            "0.0.0.0",
            "--port",
            str(SERVER_PORT),
            "--log-level",
            "debug",
            "--reload",
            "--reload-dir",
            str(BASE_DIR),
        ]


# Função para verificar se a porta está disponível
def is_port_available(port, host="0.0.0.0"):
    """Verifica se uma porta está disponível para uso."""
    try:
        # Cria um socket TCP/IP
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        # Define um timeout curto para a conexão
        sock.settimeout(1)
        # Tenta vincular o socket à porta
        sock.bind((host, port))
        # Se chegou aqui, a porta está disponível
        sock.close()
        return True
    except socket.error:
        # Se ocorreu um erro, a porta provavelmente já está em uso
        return False


# Caminho para o arquivo de configuração de LLM
LLM_CONFIG_PATH = BASE_DIR / "src" / "scrapers" / "base" / "llm_config.json"


# Função para carregar a configuração de LLM
def load_llm_config():
    default_config = {
        "selected": "OpenAI",  # Definir OpenAI como padrão
        "providers": {
            "Gemini": {
                "api_key": os.getenv("GOOGLE_AI_STUDIO_KEY", ""),
                "model": "gemini-2.5-flash-preview-04-17",
            },
            "OpenAI": {
                "api_key": os.getenv("OPENAI_API_KEY", ""),
                "model": "gpt-3.5-turbo",
                "endpoint": "",
            },
        },
    }

    try:
        if LLM_CONFIG_PATH.exists():
            with open(LLM_CONFIG_PATH, "r") as f:
                return json.load(f)
        else:
            # Criar arquivo de configuração padrão se não existir
            with open(LLM_CONFIG_PATH, "w") as f:
                json.dump(default_config, f, indent=4)
            return default_config
    except Exception as e:
        print(f"Erro ao carregar configurações de LLM: {e}")
        return default_config


# Função para salvar a configuração de LLM
def save_llm_config(config):
    try:
        with open(LLM_CONFIG_PATH, "w") as f:
            json.dump(config, f, indent=4)
    except Exception as e:
        print(f"Erro ao salvar configurações de LLM: {e}")


class ServerControlUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Promoserver")

        # Definir o tamanho da janela
        window_width = 700
        window_height = 550  # Aumentado para acomodar o frame de LLM

        # Obter as dimensões da tela
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()

        # Calcular a posição para centralizar a janela
        x_position = (screen_width - window_width) // 2
        y_position = (screen_height - window_height) // 2

        # Definir a geometria da janela (tamanho e posição)
        self.root.geometry(f"{window_width}x{window_height}+{x_position}+{y_position}")

        # Configurar o comportamento de minimização para a bandeja do sistema
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.bind("<Unmap>", self.on_minimize)

        # Variável para controlar se a janela está minimizada na bandeja
        self.is_minimized_to_tray = False

        # Inicializar variáveis do servidor antes de configurar o ícone da bandeja
        self.server_process: subprocess.Popen | None = None
        self.log_queue = queue.Queue()
        self.log_thread: threading.Thread | None = None
        self.stop_thread = threading.Event()

        # Inicializar o ícone da bandeja do sistema
        self.setup_tray_icon()

        # Carregar configuração de LLM
        self.llm_config = load_llm_config()
        print("[DEBUG] Configuração carregada com sucesso")
        self.selected_llm = tk.StringVar(
            value=self.llm_config.get("selected", "Gemini")
        )

        # Inicializar o gerenciador de logs
        self.log_manager = get_log_manager()

        # Remover o arquivo de log existente
        from src.utils.log_manager import remove_log_file

        remove_log_file()

        style = ttk.Style()
        style.theme_use("clam")

        # Frame de configuração de LLM (movido para cima)
        llm_frame = ttk.LabelFrame(root, text="Configuração de LLM", padding="10")
        llm_frame.pack(fill=tk.X, padx=10, pady=5)

        # Dropdown para seleção de LLM
        llm_label = ttk.Label(llm_frame, text="Modelo de IA:")
        llm_label.pack(side=tk.LEFT, padx=5)

        providers = list(self.llm_config.get("providers", {}).keys())
        self.llm_dropdown = ttk.Combobox(
            llm_frame,
            textvariable=self.selected_llm,
            values=providers,
            state="readonly",
            width=15,
        )
        self.llm_dropdown.pack(side=tk.LEFT, padx=5)
        self.llm_dropdown.bind("<<ComboboxSelected>>", self.on_llm_selected)

        # Botão para configurar LLM
        self.config_llm_button = ttk.Button(
            llm_frame, text="Configurar", command=self.configure_llm
        )
        self.config_llm_button.pack(side=tk.LEFT, padx=5)

        # Botão para adicionar novo LLM
        self.add_llm_button = ttk.Button(
            llm_frame, text="Adicionar LLM", command=self.add_llm
        )
        self.add_llm_button.pack(side=tk.LEFT, padx=5)

        # Frame de controle do servidor (movido para baixo)
        control_frame = ttk.Frame(root, padding="10")
        control_frame.pack(fill=tk.X)

        self.start_button = ttk.Button(
            control_frame, text="Iniciar Servidor", command=self.start_server
        )
        self.start_button.pack(side=tk.LEFT, padx=5)

        self.stop_button = ttk.Button(
            control_frame,
            text="Parar Servidor",
            command=self.stop_server,
            state=tk.DISABLED,
        )
        self.stop_button.pack(side=tk.LEFT, padx=5)

        # Botão de copiar logs
        self.copy_button = ttk.Button(
            control_frame, text="Copiar Logs", command=self.copy_logs
        )
        self.copy_button.pack(side=tk.LEFT, padx=5)

        # Botão de limpar logs
        self.clear_button = ttk.Button(
            control_frame, text="Limpar Logs", command=self.clear_logs
        )
        self.clear_button.pack(side=tk.LEFT, padx=5)

        self.status_label = ttk.Label(
            control_frame,
            text="Servidor: Parado",
            foreground="red",
            font=("Segoe UI", 10),
        )
        self.status_label.pack(side=tk.LEFT, padx=10)

        log_frame = ttk.LabelFrame(root, text="Logs do Servidor", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        self.log_text = scrolledtext.ScrolledText(
            log_frame,
            wrap=tk.WORD,
            height=10,
            state=tk.DISABLED,
            bg="white",
            fg="grey",
            font=("Consolas", 9),
        )
        self.log_text.pack(fill=tk.BOTH, expand=True)

        # Configurar o widget de log no gerenciador de logs
        self.log_manager.set_log_widget(self.log_text)

        self.poll_log_queue()

        # Verificar se o Python e o script existem
        if PYTHON_EXE is None or not Path(PYTHON_EXE).exists():
            self.log_message(
                "ERRO: Ambiente virtual não encontrado em .venv. Crie o ambiente virtual antes de rodar o Promoserver.",
                "red",
            )
            self.start_button.config(state=tk.DISABLED)
        elif not API_MAIN_SCRIPT.exists():
            self.log_message(
                f"ERRO: Script {API_MAIN_SCRIPT.name} não encontrado", "red"
            )
            self.start_button.config(state=tk.DISABLED)
        else:
            self.log_message(
                "Pronto para iniciar. Verificando configurações...", "black"
            )
            # Garante apenas uma instância usando socket na porta do servidor
            if is_port_available(SERVER_PORT):
                self.root.after(1000, self.start_server)
            # Minimizar para a bandeja após 2 segundos
            self.root.after(2000, self.minimize_to_tray)

    def log_message(self, message, tag=None):
        # Usar o gerenciador de logs universal
        if tag == "red":
            self.log_manager.error(message, tag)
        elif tag == "orange":
            self.log_manager.warning(message, tag)
        elif tag == "green":
            self.log_manager.info(message, tag)
        elif tag == "blue":
            self.log_manager.info(message, tag)
        else:
            self.log_manager.info(message, tag)

    def poll_log_queue(self):
        try:
            while True:
                msg = self.log_queue.get_nowait()
                self.log_message(msg)
        except queue.Empty:
            pass
        finally:
            self.root.after(100, self.poll_log_queue)

    def read_output(self, pipe, is_stderr=False):
        try:
            for line in iter(pipe.readline, ""):
                if self.stop_thread.is_set():
                    break
                line = line.strip()
                if line:
                    prefix = "[stderr] " if is_stderr else "[stdout] "
                    self.log_queue.put(prefix + line)
            pipe.close()
            self.log_queue.put(
                f"--- {'Stderr' if is_stderr else 'Stdout'} pipe closed ---"
            )
        except Exception as e:
            self.log_queue.put(
                f"--- Error reading {'stderr' if is_stderr else 'stdout'}: {e} ---"
            )

    def start_server(self):
        try:
            # Primeiro encerra qualquer vestígio de execução anterior
            try:
                if self.server_process:
                    self.stop_server()
            except:
                pass

            # Agora verifica se a porta está livre (opcional)
            if not is_port_available(SERVER_PORT):
                result = messagebox.askyesno(
                    "Porta ocupada",
                    "Uma instância do Promoserver parece estar sendo executada em segundo plano.\nDeseja tentar encerrar automaticamente?"
                )
                if result:
                    os.system(f"taskkill /F /T /PID $(Get-NetTCPConnection -LocalPort {SERVER_PORT} | Select-Object -ExpandProperty OwningProcess)")
                else:
                    self.log_message(f"ERRO: Porta {SERVER_PORT} está ocupada.", "red")
                    return

            self.log_message("Iniciando servidor FastAPI...", "blue")
            self.stop_thread.clear()

            try:
                # Preparar ambiente
                env = os.environ.copy()
                env["PYTHONPATH"] = str(BASE_DIR)
                env["PYTHONUNBUFFERED"] = "1"  # Força output sem buffer
                env["PYTHONIOENCODING"] = "utf-8" # Sugere UTF-8 para o subprocesso
                env["PYTHONDONTWRITEBYTECODE"] = "1"  # Evita criar __pycache__

                # Obter comando uvicorn usando a nova função
                command = get_uvicorn_command()

                self.log_message(f"Executando: {' '.join(command)}", "blue")
                self.log_message(f"Diretório de trabalho: {BASE_DIR}", "blue")
                self.log_message(
                    f"PYTHONPATH: {env.get('PYTHONPATH', 'Não definido')}", "blue"
                )

                # Verificar se o arquivo api_main.py existe
                if not API_MAIN_SCRIPT.exists():
                    self.log_message(
                        f"ERRO: Arquivo {API_MAIN_SCRIPT} não encontrado!", "red"
                    )
                    return

                self.log_message(
                    f"Arquivo api_main.py encontrado em: {API_MAIN_SCRIPT}", "green"
                )

                self.server_process = subprocess.Popen(
                    command,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    encoding="utf-8",
                    creationflags=(subprocess.CREATE_NO_WINDOW if os.name == "nt" else 0),
                    cwd=str(BASE_DIR),
                    env=env,
                    bufsize=1,
                )

                self.log_message(
                    f"Servidor iniciado (PID: {self.server_process.pid})", "green"
                )

                self.log_thread_stdout = threading.Thread(
                    target=self.read_output,
                    args=(self.server_process.stdout, False),
                    daemon=True,
                )
                self.log_thread_stderr = threading.Thread(
                    target=self.read_output,
                    args=(self.server_process.stderr, True),
                    daemon=True,
                )
                self.log_thread_stdout.start()
                self.log_thread_stderr.start()

                self.update_ui_state(running=True)

                # Atualizar o menu da bandeja
                self.update_tray_menu()

            except FileNotFoundError as e:
                self.log_message(
                    f"ERRO: Comando não encontrado: {e}. Verifique o ambiente virtual.",
                    "red",
                )
                self.server_process = None
            except Exception as e:
                self.log_message(f"ERRO ao iniciar o servidor: {e}", "red")
                self.server_process = None
        except Exception as e:
            self.log_message(f"ERRO ao iniciar o servidor: {e}", "red")
    def kill_process_tree(self, pid):
        """Mata o processo e todos os filhos no Windows."""
        try:
            subprocess.run(f"taskkill /F /T /PID {pid}", stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL, shell=True)
            self.log_message(f"Processo {pid} e filhos foram encerrados.", "green")
        except Exception as e:
            self.log_message(f"Falha ao matar processo {pid}: {e}", "red")

    def stop_server(self):
        if not self.server_process:
            messagebox.showwarning("Aviso", "O servidor não está em execução.")
            return

        self.log_message("Parando servidor...", "orange")
        self.stop_thread.set()

        try:
            pid = self.server_process.pid
            self.server_process.terminate()
            try:
                self.server_process.wait(timeout=5)
                self.log_message(f"Servidor (PID: {pid}) terminado.", "green")
            except subprocess.TimeoutExpired:
                self.log_message("Servidor não respondeu ao terminate, forçando kill...", "orange")
                self.server_process.kill()
                self.server_process.wait()
                self.log_message(f"Servidor (PID: {pid}) forçado a parar.", "green")

                # Se timeout, usar taskkill no Windows com força total
                self.kill_process_tree(pid)

        except Exception as e:
            self.log_message(f"ERRO ao parar o servidor: {e}", "red")
            messagebox.showerror("Erro", f"Falha ao parar servidor:\n{e}")
        finally:
            self.server_process = None
            self.log_thread_stdout = None
            self.log_thread_stderr = None
            self.update_ui_state(running=False)

            # Atualizar o menu da bandeja
            self.update_tray_menu()

            self.log_message("Processo do servidor finalizado.", "blue")

    def update_ui_state(self, running: bool):
        if running:
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            self.status_label.config(text="Servidor: Rodando", foreground="green")
        else:
            python_ok = Path(PYTHON_EXE).exists()
            script_ok = API_MAIN_SCRIPT.exists()
            self.start_button.config(
                state=tk.NORMAL if (python_ok and script_ok) else tk.DISABLED
            )
            self.stop_button.config(state=tk.DISABLED)
            self.status_label.config(text="Servidor: Parado", foreground="red")

    def setup_tray_icon(self):
        """Configura o ícone da bandeja do sistema."""
        # Usar o ícone personalizado da pasta assets
        icon_path = os.path.join(BASE_DIR, "assets", "icon-promoserver-blue.ico")
        if not os.path.exists(icon_path):
            # Fallback para o ícone padrão do Python se o personalizado não existir
            icon_path = os.path.join(sys.prefix, "DLLs", "py.ico")
            if not os.path.exists(icon_path):
                # Alternativa: usar um ícone embutido no Python
                icon_path = os.path.join(
                    sys.prefix, "Lib", "tkinter", "images", "python.gif"
                )
                if not os.path.exists(icon_path):
                    # Se não encontrar nenhum ícone, usar um ícone em branco
                    self.tray_icon_image = Image.new(
                        "RGBA", (16, 16), color=(0, 0, 0, 0)
                    )
                else:
                    self.tray_icon_image = Image.open(icon_path)
            else:
                self.tray_icon_image = Image.open(icon_path)
        else:
            self.tray_icon_image = Image.open(icon_path)

        # Criar o menu de contexto para o ícone da bandeja
        self.tray_icon = pystray.Icon("promoserver")
        self.tray_icon.title = "Promoserver"
        self.tray_icon.icon = self.tray_icon_image

        # Definir um menu inicial padrão (caso update_tray_menu falhe)
        self.tray_icon.menu = pystray.Menu(
            pystray.MenuItem("Mostrar", self.show_window),
            pystray.MenuItem("Iniciar Servidor", self.start_server_from_tray),
            pystray.MenuItem(
                "Parar Servidor", self.stop_server_from_tray, enabled=False
            ),
            pystray.MenuItem("Sair", self.exit_app),
        )

        # Atualizar o menu com base no estado atual
        try:
            self.update_tray_menu()
        except Exception as e:
            self.log_message(f"Erro ao atualizar menu da bandeja: {e}", "red")

        # Iniciar o ícone da bandeja em uma thread separada
        self.tray_thread = threading.Thread(target=self.run_tray_icon, daemon=True)
        self.tray_thread.start()

    def update_tray_menu(self):
        """Atualiza o menu da bandeja com base no estado atual do servidor."""
        # Verificar se o servidor está rodando
        server_running = self.server_process is not None

        # Criar menu com itens habilitados/desabilitados conforme o estado
        self.tray_icon.menu = pystray.Menu(
            pystray.MenuItem("Mostrar", self.show_window),
            pystray.MenuItem(
                "Iniciar Servidor",
                self.start_server_from_tray,
                enabled=not server_running,
            ),
            pystray.MenuItem(
                "Parar Servidor", self.stop_server_from_tray, enabled=server_running
            ),
            pystray.MenuItem("Sair", self.exit_app),
        )

    def run_tray_icon(self):
        """Executa o ícone da bandeja em uma thread separada."""
        try:
            self.tray_icon.run()
        except Exception as e:
            print(f"Erro ao executar ícone da bandeja: {e}")

    def minimize_to_tray(self):
        """Minimiza a janela para a bandeja do sistema."""
        self.is_minimized_to_tray = True
        self.root.withdraw()  # Ocultar a janela

    def on_minimize(self, event=None):
        """Manipula o evento de minimização da janela."""
        # Verificar se a janela está sendo minimizada (não iconificada)
        if self.root.state() == "iconic" and not self.is_minimized_to_tray:
            self.minimize_to_tray()
            return "break"  # Impedir a minimização padrão

    def show_window(self, icon=None, item=None):
        """Mostra a janela principal."""
        self.is_minimized_to_tray = False
        self.root.deiconify()  # Mostrar a janela
        self.root.lift()  # Trazer para frente
        self.root.focus_force()  # Dar foco

    def start_server_from_tray(self, icon=None, item=None):
        """Inicia o servidor a partir do menu da bandeja."""
        if not self.server_process:
            self.start_server()

    def stop_server_from_tray(self, icon=None, item=None):
        """Para o servidor a partir do menu da bandeja."""
        if self.server_process:
            self.stop_server()

    def exit_app(self, icon=None, item=None):
        """Sai do aplicativo a partir do menu da bandeja."""
        if self.server_process:
            self.stop_server()
        if icon:
            icon.stop()
        self.root.quit()
        self.root.destroy()

    def on_closing(self):
        """Manipula o evento de fechamento da janela."""
        if self.server_process:
            self.log_message("Servidor está rodando. Finalizando antes de sair...", "orange")
            self.stop_server()  # Garante que o servidor e filhos sejam encerrados
            while self.server_process is not None:  # Aguarda até que o processo seja eliminado
                self.root.update()
                time.sleep(0.1)
        self.root.quit()
        self.root.destroy()

    def copy_logs(self):
        """Copia todos os logs para a área de transferência."""
        # Obtém o texto dos logs
        self.log_text.config(state=tk.NORMAL)
        log_content = self.log_text.get(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)

        # Copia para a área de transferência
        self.root.clipboard_clear()
        self.root.clipboard_append(log_content)

        # Muda o texto e o ícone do botão
        original_text = self.copy_button["text"]
        self.copy_button.config(text="✓ Copiado", style="Green.TButton")

        # Cria um estilo verde para o botão
        style = ttk.Style()
        style.configure("Green.TButton", foreground="green")

        # Agenda a restauração do texto original após 3 segundos
        def restore_button():
            self.copy_button.config(text=original_text, style="")

        self.root.after(3000, restore_button)

    def clear_logs(self):
        """Limpa todos os logs da interface."""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)

    def on_llm_selected(self, event):
        """Atualiza o LLM selecionado quando o usuário escolhe uma opção no dropdown."""
        selected = self.selected_llm.get()
        if selected and selected in self.llm_config.get("providers", {}):
            self.llm_config["selected"] = selected
            save_llm_config(self.llm_config)
            self.log_message(f"LLM alterado para: {selected}", "green")

    def configure_llm(self):
        """Abre uma janela para configurar o LLM selecionado."""
        selected = self.selected_llm.get()
        if not selected or selected not in self.llm_config.get("providers", {}):
            messagebox.showerror("Erro", "Selecione um LLM válido para configurar.")
            return

        provider_config = self.llm_config["providers"][selected]

        # Criar uma janela de diálogo para configuração
        config_dialog = tk.Toplevel(self.root)
        config_dialog.title(f"Configurar {selected}")
        config_dialog.geometry("400x250")
        config_dialog.resizable(False, False)
        config_dialog.transient(self.root)
        config_dialog.grab_set()

        # Frame principal
        main_frame = ttk.Frame(config_dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Campos de configuração
        ttk.Label(main_frame, text="API Key:").grid(
            row=0, column=0, sticky=tk.W, pady=5
        )
        api_key_entry = ttk.Entry(main_frame, width=40)
        api_key_entry.grid(row=0, column=1, pady=5, padx=5)
        api_key_entry.insert(0, provider_config.get("api_key", ""))

        ttk.Label(main_frame, text="Modelo:").grid(row=1, column=0, sticky=tk.W, pady=5)
        model_entry = ttk.Entry(main_frame, width=40)
        model_entry.grid(row=1, column=1, pady=5, padx=5)
        model_entry.insert(0, provider_config.get("model", ""))

        # Campo de endpoint (apenas para OpenAI e outros, não para Gemini)
        endpoint_entry = None
        if selected != "Gemini":
            ttk.Label(main_frame, text="Endpoint (opcional):").grid(
                row=2, column=0, sticky=tk.W, pady=5
            )
            endpoint_entry = ttk.Entry(main_frame, width=40)
            endpoint_entry.grid(row=2, column=1, pady=5, padx=5)
            endpoint_entry.insert(0, provider_config.get("endpoint", ""))

        # Botões
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=20)

        def save_config():
            provider_config["api_key"] = api_key_entry.get()
            provider_config["model"] = model_entry.get()

            if endpoint_entry:
                provider_config["endpoint"] = endpoint_entry.get()

            self.llm_config["providers"][selected] = provider_config
            save_llm_config(self.llm_config)
            self.log_message(f"Configuração de {selected} atualizada", "green")
            config_dialog.destroy()

        ttk.Button(button_frame, text="Salvar", command=save_config).pack(
            side=tk.LEFT, padx=5
        )
        ttk.Button(button_frame, text="Cancelar", command=config_dialog.destroy).pack(
            side=tk.LEFT, padx=5
        )

    def add_llm(self):
        """Abre uma janela para adicionar um novo LLM."""
        # Criar uma janela de diálogo para adicionar LLM
        add_dialog = tk.Toplevel(self.root)
        add_dialog.title("Adicionar Novo LLM")
        add_dialog.geometry("400x300")
        add_dialog.resizable(False, False)
        add_dialog.transient(self.root)
        add_dialog.grab_set()

        # Frame principal
        main_frame = ttk.Frame(add_dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Campos para o novo LLM
        ttk.Label(main_frame, text="Nome do LLM:").grid(
            row=0, column=0, sticky=tk.W, pady=5
        )
        name_entry = ttk.Entry(main_frame, width=40)
        name_entry.grid(row=0, column=1, pady=5, padx=5)

        ttk.Label(main_frame, text="API Key:").grid(
            row=1, column=0, sticky=tk.W, pady=5
        )
        api_key_entry = ttk.Entry(main_frame, width=40)
        api_key_entry.grid(row=1, column=1, pady=5, padx=5)

        ttk.Label(main_frame, text="Modelo:").grid(row=2, column=0, sticky=tk.W, pady=5)
        model_entry = ttk.Entry(main_frame, width=40)
        model_entry.grid(row=2, column=1, pady=5, padx=5)

        ttk.Label(main_frame, text="Endpoint (opcional):").grid(
            row=3, column=0, sticky=tk.W, pady=5
        )
        endpoint_entry = ttk.Entry(main_frame, width=40)
        endpoint_entry.grid(row=3, column=1, pady=5, padx=5)

        # Botões
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=20)

        def save_new_llm():
            name = name_entry.get().strip()
            if not name:
                messagebox.showerror("Erro", "O nome do LLM é obrigatório.")
                return

            if name in self.llm_config.get("providers", {}):
                messagebox.showerror("Erro", f"Já existe um LLM com o nome '{name}'.")
                return

            # Adicionar novo LLM à configuração
            self.llm_config.setdefault("providers", {})[name] = {
                "api_key": api_key_entry.get(),
                "model": model_entry.get(),
                "endpoint": endpoint_entry.get(),
            }

            save_llm_config(self.llm_config)

            # Atualizar o dropdown
            self.llm_dropdown["values"] = list(
                self.llm_config.get("providers", {}).keys()
            )

            self.log_message(f"Novo LLM '{name}' adicionado", "green")
            add_dialog.destroy()

        ttk.Button(button_frame, text="Adicionar", command=save_new_llm).pack(
            side=tk.LEFT, padx=5
        )
        ttk.Button(button_frame, text="Cancelar", command=add_dialog.destroy).pack(
            side=tk.LEFT, padx=5
        )


def already_running(port=SERVER_PORT):
    # Usa SO_EXCLUSIVEADDRUSE para garantir exclusividade no Windows
    import platform

    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    if platform.system() == "Windows":
        try:
            s.setsockopt(socket.SOL_SOCKET, 0x4, 1)  # SO_EXCLUSIVEADDRUSE
        except Exception:
            pass
    try:
        s.bind(("127.0.0.1", port))
        s.listen(1)
        s.close()
        return False
    except OSError:
        return True


if __name__ == "__main__":
    import os
    import atexit
    import tempfile
    import hashlib

    # Cria um arquivo de lock exclusivo por usuário e projeto
    lock_id = hashlib.md5(str(BASE_DIR).encode()).hexdigest()
    lockfile = os.path.join(tempfile.gettempdir(), f"promoserver_{lock_id}.lock")

    def remove_lock():
        try:
            os.remove(lockfile)
        except Exception:
            pass

    try:
        if os.path.exists(lockfile):
            sys.exit(0)
        with open(lockfile, "w") as f:
            f.write(str(os.getpid()))
        atexit.register(remove_lock)
        root = tk.Tk()
        app = ServerControlUI(root)
        root.mainloop()
    finally:
        remove_lock()
