from .. import Provider as DateTimeProvider


class Provider(DateTimeProvider):
    # Source: https://vi.wikipedia.org/wiki/%C4%90%E1%BB%8Bnh_d%E1%BA%A1ng_ng%C3%A0y_v%C3%A0_gi%E1%BB%9D_%E1%BB%9F_Vi%E1%BB%87t_Nam  # NOQA
    DAY_NAMES = {
        "0": "Chủ Nhật",
        "1": "Th<PERSON> Hai",
        "2": "Th<PERSON> Ba",
        "3": "<PERSON><PERSON><PERSON>",
        "4": "<PERSON><PERSON><PERSON>",
        "5": "<PERSON><PERSON><PERSON>",
        "6": "<PERSON><PERSON><PERSON>",
    }

    MONTH_NAMES = {
        "01": "Tháng <PERSON>",
        "02": "Tháng <PERSON>",
        "03": "Tháng Ba",
        "04": "Tháng Tư",
        "05": "<PERSON>háng <PERSON>",
        "06": "<PERSON>háng <PERSON>",
        "07": "<PERSON><PERSON><PERSON><PERSON>",
        "08": "<PERSON>h<PERSON>g <PERSON>",
        "09": "<PERSON><PERSON><PERSON><PERSON>",
        "10": "<PERSON><PERSON><PERSON>g <PERSON>",
        "11": "Tháng <PERSON>ột",
        "12": "Tháng <PERSON>ời Hai",
    }

    def day_of_week(self):
        day = self.date("%w")
        return self.DAY_NAMES[day]

    def month_name(self):
        month = self.month()
        return self.MONTH_NAMES[month]
