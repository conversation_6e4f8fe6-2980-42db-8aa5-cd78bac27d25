"""
Configuração para o Digital Ocean Spaces.
"""
import os
import logging

log = logging.getLogger(__name__)

class DigitalOceanConfig:
    """Configuração para o Digital Ocean Spaces."""
    
    def __init__(self):
        """Inicializa a configuração a partir das variáveis de ambiente."""
        self.region = os.environ.get("DO_REGION", "").strip(",")
        self.bucket_name = os.environ.get("DO_BUCKET_NAME", "").strip(",")
        self.access_key = os.environ.get("DO_ACCESS_KEY", "").strip(",")
        self.secret_key = os.environ.get("DO_SECRET_KEY", "").strip(",")
        self.spaces_endpoint = os.environ.get("DO_SPACES_ENDPOINT", "").strip(",")
        
        # Validar configuração
        self._validate_config()
    
    def _validate_config(self):
        """Valida se todas as configurações necessárias estão presentes."""
        missing_configs = []
        
        if not self.region:
            missing_configs.append("DO_REGION")
        if not self.bucket_name:
            missing_configs.append("DO_BUCKET_NAME")
        if not self.access_key:
            missing_configs.append("DO_ACCESS_KEY")
        if not self.secret_key:
            missing_configs.append("DO_SECRET_KEY")
        if not self.spaces_endpoint:
            missing_configs.append("DO_SPACES_ENDPOINT")
        
        if missing_configs:
            log.warning(f"Configurações do Digital Ocean incompletas. Faltando: {', '.join(missing_configs)}")
            
    def is_valid(self):
        """Verifica se a configuração é válida para uso."""
        return all([
            self.region,
            self.bucket_name,
            self.access_key,
            self.secret_key,
            self.spaces_endpoint
        ])
