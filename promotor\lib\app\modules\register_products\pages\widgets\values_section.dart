import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../../components/input_field_pattern.dart';
import '../../controllers/tabs_controller.dart';
import '../../models/product_tab.dart';

class ValuesSection extends StatelessWidget {
  final TabsController controller = Modular.get<TabsController>();
  final ThemeData theme;
  final TextInputFormatter currencyFormatter;

  ValuesSection({
    super.key,
    required this.theme,
    required this.currencyFormatter,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, _) {
        final ProductTab? tab = controller.currentTab;
        if (tab == null) {
          return const SizedBox.shrink(); // Não mostra nada se não houver aba selecionada
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Valores e Entrega',
              style: theme.textTheme.titleMedium,
            ),
            const SizedBox(height: 16),

            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Preço original
                Expanded(
                  child: TextFormField(
                    controller: tab.oldPriceController,
                    readOnly: tab.isFetching,
                    decoration: InputFieldPattern.decoration(
                      label: 'Valor Original (com R\$)',
                      prefixIcon: Icons.money_off_outlined,
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [currencyFormatter],
                  ),
                ),
                const SizedBox(width: 16),

                // Preço atual
                Expanded(
                  child: TextFormField(
                    controller: tab.priceController,
                    readOnly: tab.isFetching,
                    decoration: InputFieldPattern.decoration(
                      label: 'Valor Atual (com R\$)',
                      prefixIcon: Icons.attach_money_outlined,
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [currencyFormatter],
                    validator:
                        (v) =>
                            (v == null || v.isEmpty)
                                ? 'Valor atual obrigatório'
                                : null,
                  ),
                ),
                const SizedBox(width: 16),

                // Cupom
                Expanded(
                  child: TextFormField(
                    controller: tab.couponController,
                    readOnly: tab.isFetching,
                    decoration: InputFieldPattern.decoration(
                      label: 'Cupom (se houver)',
                      prefixIcon: Icons.local_offer_outlined,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                // Frete
                SizedBox(
                  width: 240,
                  child: DropdownButtonFormField<bool>(
                    value: tab.hasShipping,
                    items: const [
                      DropdownMenuItem(
                        value: true,
                        child: Text('Com Frete'),
                      ),
                      DropdownMenuItem(
                        value: false,
                        child: Text('Sem Frete/Retirar'),
                      ),
                    ],
                    onChanged:
                        tab.isFetching
                            ? null
                            : (v) =>
                                controller.setHasShipping(v ?? false),
                    decoration: InputFieldPattern.decoration(
                      label: 'Frete',
                      prefixIcon: Icons.local_shipping_outlined,
                    ),
                  ),
                ),
                const SizedBox(width: 24),

                // Melhor preço
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Switch(
                      value: tab.bestPrice,
                      onChanged:
                          tab.isFetching
                              ? null
                              : controller.setBestPrice,
                    ),
                    const Text('Melhor Preço'),
                  ],
                ),
                const SizedBox(width: 24),

                // Indicamos
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Switch(
                      value: tab.recommended,
                      onChanged:
                          tab.isFetching
                              ? null
                              : controller.setRecommended,
                    ),
                    const Text('Indicamos'),
                  ],
                ),
                const SizedBox(width: 24),

                // Story
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Switch(
                      value: tab.isStory,
                      onChanged:
                          tab.isFetching
                              ? null
                              : controller.setIsStory,
                    ),
                    const Text('Story'),
                  ],
                ),
              ],
            ),
          ],
        );
      },
    );
  }
}
