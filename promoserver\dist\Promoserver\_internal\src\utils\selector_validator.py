import logging
import random
import time
import re
from typing import Dict, List, Optional, Set, Any
from bs4 import BeautifulSoup, Tag

from src.utils.selector_manager import SelectorManager
from src.scrapers.base.header_manager import HeaderManager
from src.scrapers.base.ai_content_generator import AIContentGenerator

logger = logging.getLogger(__name__)


class SelectorValidator:
    """
    Classe para validar e atualizar seletores de uma loja específica.
    Usa técnicas anti-detecção para verificar seletores em páginas reais.
    """

    def __init__(self, store_id: str):
        self.store_id = store_id
        self.selector_manager = SelectorManager()
        self.header_manager = HeaderManager()
        self.ai_generator = AIContentGenerator()  # Inicializa o gerador de IA

        # Estatísticas de validação
        self.stats = {"tested": 0, "valid": 0, "invalid": 0, "new": 0, "updated": 0}

    def validate_selectors(self, test_url: str) -> Dict[str, Any]:
        """
        Valida os seletores existentes e descobre novos seletores para a loja.

        Args:
            test_url: URL da página para testar os seletores

        Returns:
            Dict com estatísticas e detalhes da validação
        """
        logger.info(
            f"Iniciando validação de seletores para {self.store_id} usando URL: {test_url}"
        )

        # Inicializa estatísticas
        self.stats = {
            "tested": 0,
            "valid": 0,
            "invalid": 0,
            "new": 0,
            "updated": 0,
        }

        # Rastreia detalhes dos seletores para relatório
        selector_details = {
            "valid_selectors": [],
            "invalid_selectors": [],
            "new_selectors": [],
            "updated_selectors": [],
        }

        try:
            # Busca a página usando técnicas anti-detecção
            soup = self._fetch_page_with_anti_detection(test_url)
            if not soup:
                logger.error(
                    f"Não foi possível obter a página {test_url} para validação"
                )
                return {
                    **self.stats,
                    "error": "Não foi possível obter a página para validação",
                    "store_id": self.store_id,
                    "test_url": test_url,
                }

            # Obtém o HTML da página
            html_content = str(soup)

            # Guarda os seletores originais para comparação posterior
            original_selectors = {}
            for selector_type in self.selector_manager.get_available_types(
                self.store_id
            ):
                original_selectors[selector_type] = [
                    {
                        "id": s.get("id"),
                        "selector": s.get("selector"),
                        "active": s.get("active", False),
                    }
                    for s in self.selector_manager.get_selectors_by_type(
                        self.store_id, selector_type
                    )
                ]

            # Tenta usar a IA para identificar seletores (método síncrono)
            logger.info("Tentando usar IA para identificar seletores")
            ai_selectors = self.ai_generator.identify_selectors_sync(
                html_content, self.store_id
            )

            if ai_selectors:
                # A IA conseguiu identificar seletores
                logger.info(
                    f"IA identificou {sum(len(sels) for sels in ai_selectors.values())} seletores potenciais"
                )
                self._process_ai_selectors(ai_selectors, soup)
            else:
                # Fallback para método manual se a IA falhar
                logger.info(
                    "IA não conseguiu identificar seletores. Usando método manual."
                )

                # Dicionário para armazenar os seletores identificados manualmente
                identified_selectors = {}

                try:
                    # Adicionamos seletores comuns ao dicionário
                    self._add_common_selectors(soup, identified_selectors)

                    if identified_selectors:
                        logger.info(
                            f"Identificados {sum(len(sels) for sels in identified_selectors.values())} seletores potenciais manualmente"
                        )
                        # Processa os seletores identificados
                        self._process_ai_selectors(identified_selectors, soup)
                except Exception as e:
                    logger.error(f"Erro ao identificar seletores manualmente: {e}")

            # Obtém todos os seletores atuais da loja
            selector_types = self.selector_manager.get_available_types(self.store_id)

            # Valida cada tipo de seletor
            for selector_type in selector_types:
                logger.info(f"Validando seletores do tipo '{selector_type}'")
                self._validate_selector_type(selector_type, soup)

            # Busca novos seletores potenciais
            self._discover_new_selectors(soup)

            # Salva as alterações
            self.selector_manager.save_selectors(self.store_id)

            # Compara os seletores antes e depois para identificar mudanças
            for selector_type in self.selector_manager.get_available_types(
                self.store_id
            ):
                current_selectors = [
                    {
                        "id": s.get("id"),
                        "selector": s.get("selector"),
                        "active": s.get("active", False),
                    }
                    for s in self.selector_manager.get_selectors_by_type(
                        self.store_id, selector_type
                    )
                ]

                # Identifica seletores válidos
                for selector in current_selectors:
                    if selector["active"]:
                        selector_details["valid_selectors"].append(
                            {
                                "type": selector_type,
                                "selector": selector["selector"],
                                "id": selector["id"],
                            }
                        )

                # Identifica seletores inválidos
                for original in original_selectors.get(selector_type, []):
                    if original["active"]:
                        # Verifica se o seletor ainda existe e está ativo
                        still_active = False
                        for current in current_selectors:
                            if current["id"] == original["id"] and current["active"]:
                                still_active = True
                                break

                        if not still_active:
                            selector_details["invalid_selectors"].append(
                                {
                                    "type": selector_type,
                                    "selector": original["selector"],
                                    "id": original["id"],
                                }
                            )

                # Identifica seletores novos
                original_ids = {
                    s["id"] for s in original_selectors.get(selector_type, [])
                }
                for current in current_selectors:
                    if current["id"] not in original_ids and current["active"]:
                        selector_details["new_selectors"].append(
                            {
                                "type": selector_type,
                                "selector": current["selector"],
                                "id": current["id"],
                            }
                        )

                # Identifica seletores atualizados
                for original in original_selectors.get(selector_type, []):
                    for current in current_selectors:
                        if (
                            current["id"] == original["id"]
                            and current["selector"] != original["selector"]
                        ):
                            selector_details["updated_selectors"].append(
                                {
                                    "type": selector_type,
                                    "old_selector": original["selector"],
                                    "new_selector": current["selector"],
                                    "id": current["id"],
                                }
                            )

            logger.info(
                f"Validação concluída para {self.store_id}. Estatísticas: {self.stats}"
            )

            # Adiciona detalhes dos seletores às estatísticas
            result = self.stats.copy()
            result["selector_details"] = selector_details
            result["store_id"] = self.store_id
            result["test_url"] = test_url

            return result

        except Exception as e:
            logger.error(f"Erro ao validar seletores: {e}", exc_info=True)
            return {
                "tested": 0,
                "valid": 0,
                "invalid": 0,
                "new": 0,
                "updated": 0,
                "error": str(e),
                "store_id": self.store_id,
                "test_url": test_url,
            }

    def _fetch_page_with_anti_detection(self, url: str) -> Optional[BeautifulSoup]:
        """
        Busca a página usando técnicas avançadas anti-detecção.

        Args:
            url: URL da página a ser buscada

        Returns:
            BeautifulSoup da página ou None se falhar
        """
        logger.info(f"Buscando página {url} com técnicas anti-detecção")

        # Adiciona delays aleatórios para simular comportamento humano
        time.sleep(random.uniform(2.0, 4.0))

        # Tenta obter a página usando o BrowserSimulator
        soup = self.browser_simulator.get_soup(url)
        if not soup:
            logger.error(f"Falha ao obter página {url}")
            return None

        logger.info(f"Página obtida com sucesso: {url}")
        return soup

    def get_selectors_by_type(self, store_id: str, selector_type: str) -> List[Dict]:
        """Método auxiliar para obter seletores de um tipo específico."""
        return self.selector_manager.get_selectors_by_type(store_id, selector_type)

    def _add_common_selectors(
        self, soup: BeautifulSoup, selectors_dict: Dict[str, List[Dict[str, Any]]]
    ) -> None:
        """Adiciona seletores comuns ao dicionário de seletores.

        Args:
            soup: BeautifulSoup da página
            selectors_dict: Dicionário para armazenar os seletores
        """
        # Padrões comuns para cada tipo de seletor
        selector_patterns = {
            "product": [
                ".ui-search-result__wrapper",
                ".ui-search-result",
                ".ui-search-layout__item",
                ".andes-card",
                ".promotion-item",
                ".items_container .item",
            ],
            "title": [
                ".ui-search-item__title",
                ".ui-pdp-title",
                ".item__title",
                ".item-title",
                ".main-title",
                ".product-title",
                "h1.ui-pdp-title",
                ".item-title a",
            ],
            "price": [
                ".ui-search-price__second-line .price-tag-amount",
                ".ui-pdp-price__second-line .price-tag-amount",
                ".price__container .price-tag-amount",
                ".item-price",
                ".price__fraction",
                ".ui-pdp-price__second-line .andes-money-amount__fraction",
            ],
            "old_price": [
                ".ui-search-price__original-value .price-tag-amount",
                ".ui-pdp-price__original-value .price-tag-amount",
                ".price-old",
                ".price__original",
                ".price-tag-fraction-old",
            ],
            "image": [
                ".ui-search-result-image__element",
                ".slick-slide.selected img",
                ".ui-pdp-image",
                ".item__image img",
                ".product-image img",
                ".ui-pdp-gallery__figure img",
            ],
            "link": [
                ".ui-search-link",
                ".ui-search-result__content a",
                ".ui-search-item__group__element a",
                ".item__info-title a",
                ".item-link",
                ".ui-search-result-image a",
            ],
            "installments": [
                ".ui-search-installments",
                ".ui-pdp-installments",
                ".installments",
                ".payment-option",
                ".ui-pdp-payment-methods__installments",
            ],
            "shipping": [
                ".ui-search-item__shipping",
                ".ui-pdp-shipping",
                ".shipping-method",
                ".shipping-info",
                ".ui-pdp-shipping-summary",
            ],
        }

        # Para cada tipo de seletor
        for selector_type, patterns in selector_patterns.items():
            selectors = self._identify_common_selectors_from_patterns(soup, patterns)
            if selectors:
                selectors_dict[selector_type] = selectors

    def _identify_common_selectors_from_patterns(
        self, soup: BeautifulSoup, patterns: List[str]
    ) -> List[Dict[str, Any]]:
        """Identifica seletores comuns a partir de padrões.

        Args:
            soup: BeautifulSoup da página
            patterns: Lista de padrões de seletores CSS

        Returns:
            Lista de seletores identificados
        """
        result = []

        for pattern in patterns:
            try:
                elements = soup.select(pattern)
                if elements:
                    # Encontrou elementos com este seletor
                    result.append(
                        {
                            "selector": pattern,
                            "description": f"Seletor encontrado com {len(elements)} elementos",
                            "confidence": 0.9,  # Alta confiança para seletores comuns
                        }
                    )
            except Exception as e:
                logger.debug(f"Erro ao testar seletor {pattern}: {e}")

        return result

    def _validate_selector_type(self, selector_type: str, soup: BeautifulSoup) -> None:
        """
        Valida todos os seletores de um tipo específico.

        Args:
            selector_type: Tipo de seletor (product, title, price, etc.)
            soup: BeautifulSoup da página para testar
        """
        selectors = self.selector_manager.selectors_by_store.get(self.store_id, {}).get(
            selector_type, []
        )

        for selector in selectors:
            selector_id = selector.get("id")
            selector_text = selector.get("selector")
            was_active = selector.get("active", False)

            self.stats["tested"] += 1

            # Testa o seletor na página
            elements = soup.select(selector_text)
            is_valid = len(elements) > 0

            if is_valid:
                self.stats["valid"] += 1
                if not was_active:
                    logger.info(
                        f"Ativando seletor anteriormente inativo: {selector_text}"
                    )
                    self._update_selector_status(selector_id, True)
                    self.stats["updated"] += 1
            else:
                if was_active:
                    logger.info(
                        f"Desativando seletor que não funciona mais: {selector_text}"
                    )
                    self._update_selector_status(selector_id, False)
                    self.stats["invalid"] += 1
                    self.stats["updated"] += 1

    def _update_selector_status(self, selector_id: int, active: bool) -> None:
        """
        Atualiza o status de um seletor.

        Args:
            selector_id: ID do seletor
            active: Novo status (True=ativo, False=inativo)
        """
        selector = self.selector_manager.find_selector_by_id(selector_id)
        if not selector:
            logger.error(f"Seletor ID {selector_id} não encontrado")
            return

        store_id = selector.get("store")
        selector_type = selector.get("type")
        selector_text = selector.get("selector")
        description = selector.get("description")

        self.selector_manager.update_selector(
            selector_id=selector_id,
            store_id=store_id,
            selector_type=selector_type,
            selector_text=selector_text,
            description=description,
            active=active,
        )

    def _discover_new_selectors(self, soup: BeautifulSoup) -> None:
        """
        Descobre novos seletores potenciais na página.

        Args:
            soup: BeautifulSoup da página para análise
        """
        logger.info("Buscando novos seletores potenciais")

        # Mapeamento de padrões para tipos de seletores (expandido com mais padrões)
        selector_patterns = {
            "product": [
                # Padrões gerais
                r"product(-|_|\.)item",
                r"product(-|_|\.)container",
                r"item(-|_|\.)product",
                r"search(-|_|\.)result(-|_|\.)item",
                r"ui-search(-|_|\.)result",
                # Padrões específicos do Mercado Livre
                r"ui-search-layout__item",
                r"ui-search-result(-|_|\.)",
                r"andes-card",
                r"promotion-item",
                r"poly-card",
                r"poly-component",
                r"poly-item",
                r"poly-grid-item",
                r"promotion-grid-item",
                r"promotion-carousel-item",
            ],
            "title": [
                # Padrões gerais
                r"product(-|_|\.)title",
                r"item(-|_|\.)title",
                r"title(-|_|\.)product",
                r"ui(-|_|\.)pdp(-|_|\.)title",
                r"item__title",
                # Padrões específicos do Mercado Livre
                r"ui-search-item__title",
                r"andes-card__header",
                r"promotion-item__title",
                r"poly-component__title",
                r"poly-card__title",
                r"poly-title",
                r"ui-pdp-title",
            ],
            "price": [
                # Padrões gerais
                r"price(-|_|\.)tag",
                r"product(-|_|\.)price",
                r"price(-|_|\.)fraction",
                r"price(-|_|\.)current",
                r"ui(-|_|\.)pdp(-|_|\.)price",
                # Padrões específicos do Mercado Livre
                r"andes-money-amount__fraction",
                r"price-tag(-|_|\.)",
                r"ui-search-price__part",
                r"poly-component__price",
                r"poly-card__price",
                r"poly-price",
                r"ui-pdp-price",
                r"promotion-item__price",
            ],
            "old_price": [
                # Padrões gerais
                r"price(-|_|\.)old",
                r"price(-|_|\.)previous",
                r"price(-|_|\.)was",
                r"price(-|_|\.)original",
                r"price(-|_|\.)before",
                # Padrões específicos do Mercado Livre
                r"andes-money-amount--previous",
                r"price-tag-previous",
                r"ui-search-price__second-line",
                r"poly-component__previous-price",
                r"poly-card__previous-price",
                r"poly-previous-price",
                r"ui-pdp-price__original-value",
                r"promotion-item__previous-price",
                r"price-tag--strike",
            ],
            "link": [
                # Padrões gerais
                r"product(-|_|\.)link",
                r"item(-|_|\.)link",
                r"link(-|_|\.)product",
                r"ui(-|_|\.)pdp(-|_|\.)link",
                r"item__link",
                # Padrões específicos do Mercado Livre
                r"ui-search-link",
                r"ui-search-result__content",
                r"poly-component__link",
                r"poly-card__link",
                r"poly-link",
                r"promotion-item__link",
            ],
            "image": [
                # Padrões gerais
                r"product(-|_|\.)image",
                r"item(-|_|\.)image",
                r"image(-|_|\.)product",
                r"ui(-|_|\.)pdp(-|_|\.)image",
                r"item__image",
                # Padrões específicos do Mercado Livre
                r"ui-search-result-image",
                r"poly-component__picture",
                r"poly-card__portada",
                r"poly-image",
                r"promotion-item__image",
                r"ui-pdp-gallery__figure",
                r"andes-carousel-snapped__slide",
            ],
            "installments": [
                # Padrões gerais
                r"installments",
                r"payment(-|_|\.)option",
                r"price(-|_|\.)installments",
                # Padrões específicos do Mercado Livre
                r"ui-search-installments",
                r"poly-component__installments",
                r"poly-card__installments",
                r"poly-installments",
                r"promotion-item__installments",
                r"ui-pdp-payment",
            ],
            "coupon": [
                # Padrões gerais
                r"discount",
                r"coupon",
                r"promotion(-|_|\.)tag",
                r"price(-|_|\.)discount",
                # Padrões específicos do Mercado Livre
                r"poly-coupons__pill",
                r"poly-component__coupon",
                r"poly-card__coupon",
                r"poly-coupon",
                r"promotion-item__coupon",
                r"ui-pdp-action--coupon",
            ],
            "shipping": [
                # Padrões gerais
                r"shipping",
                r"delivery",
                r"freight",
                # Padrões específicos do Mercado Livre
                r"ui-search-item__shipping",
                r"poly-component__shipping",
                r"poly-card__shipping",
                r"poly-shipping",
                r"promotion-item__shipping",
                r"ui-pdp-shipping",
                r"shipping-summary",
            ],
            "product_url": [
                # Padrões gerais
                r"canonical",
                r"product(-|_|\.)url",
                r"item(-|_|\.)url",
                # Padrões específicos do Mercado Livre
                r"ui-pdp-share__link",
                r"ui-pdp-share__whatsapp",
                r"ui-pdp-action--share",
            ],
        }

        # Conjunto para armazenar seletores existentes
        existing_selectors = set()
        for selector_type in self.selector_manager.get_available_types(self.store_id):
            selectors = self.selector_manager.get_selectors_by_type(
                self.store_id, selector_type
            )
            for selector in selectors:
                existing_selectors.add(selector.get("selector", ""))

        # Encontra elementos com classes ou IDs que correspondem aos padrões
        for element in soup.find_all(class_=True):
            self._check_element_for_new_selectors(
                element, selector_patterns, existing_selectors
            )

        for element in soup.find_all(id=True):
            self._check_element_for_new_selectors(
                element, selector_patterns, existing_selectors, attr="id"
            )

    def _check_element_for_new_selectors(
        self,
        element: Tag,
        selector_patterns: Dict[str, List[str]],
        existing_selectors: Set[str],
        attr: str = "class",
    ) -> None:
        """
        Verifica se um elemento corresponde a algum padrão de seletor.

        Args:
            element: Elemento HTML a ser verificado
            selector_patterns: Padrões de seletores por tipo
            existing_selectors: Conjunto de seletores existentes
            attr: Atributo a ser verificado (class ou id)
        """
        attr_value = element.get(attr)
        if not attr_value:
            return

        # Converte para string se for uma lista (caso de classes)
        if isinstance(attr_value, list):
            attr_value = " ".join(attr_value)

        # Cria o seletor CSS
        if attr == "class":
            # Cria seletores para cada classe individual e para combinações de classes
            classes = attr_value.split()

            # Seletor com todas as classes
            full_selector = "." + ".".join(classes)
            selectors_to_check = [full_selector]

            # Adiciona seletores para classes individuais importantes
            for cls in classes:
                # Verifica se a classe parece importante (contém palavras-chave)
                important_keywords = [
                    "product",
                    "price",
                    "title",
                    "image",
                    "link",
                    "coupon",
                    "shipping",
                    "item",
                    "card",
                    "poly",
                ]
                if any(keyword in cls.lower() for keyword in important_keywords):
                    selectors_to_check.append(f".{cls}")
        else:
            # Para IDs, usa apenas o seletor completo
            selectors_to_check = [f"#{attr_value}"]

        # Verifica cada seletor candidato
        for selector in selectors_to_check:
            # Verifica se o seletor já existe
            if selector in existing_selectors:
                continue

            # Verifica se o seletor corresponde a algum padrão
            for selector_type, patterns in selector_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, attr_value, re.IGNORECASE):
                        # Testa se o seletor funciona
                        try:
                            # Tenta primeiro no elemento pai para contexto
                            if element.parent:
                                elements = element.parent.select(selector)
                                if len(elements) > 0:
                                    self._add_new_selector(
                                        selector_type,
                                        selector,
                                        f"Novo seletor para {selector_type} (contexto pai)",
                                    )
                                    existing_selectors.add(selector)
                                    break

                            # Tenta no documento inteiro
                            elements = element.find_parent().select(selector)
                            if len(elements) > 0:
                                # Verifica se o seletor retorna muitos elementos (muito genérico)
                                if len(elements) > 50:
                                    logger.debug(
                                        f"Seletor {selector} muito genérico ({len(elements)} elementos). Ignorando."
                                    )
                                    continue

                                self._add_new_selector(
                                    selector_type,
                                    selector,
                                    f"Novo seletor para {selector_type} (documento)",
                                )
                                existing_selectors.add(selector)
                                break
                        except Exception as e:
                            logger.debug(f"Erro ao testar seletor {selector}: {e}")
                            continue

    def _add_new_selector(
        self, selector_type: str, selector_text: str, description: str
    ) -> None:
        """
        Adiciona um novo seletor.

        Args:
            selector_type: Tipo de seletor
            selector_text: Texto do seletor CSS
            description: Descrição do seletor
        """
        logger.info(f"Adicionando novo seletor: {selector_type} -> {selector_text}")

        result = self.selector_manager.add_selector(
            store_id=self.store_id,
            selector_type=selector_type,
            selector_text=selector_text,
            description=description,
            active=True,
        )

        if result:
            self.stats["new"] += 1
        else:
            logger.warning(f"Falha ao adicionar novo seletor: {selector_text}")

    def _process_ai_selectors(
        self, ai_selectors: Dict[str, List[Dict[str, Any]]], soup: BeautifulSoup
    ) -> None:
        """
        Processa os seletores identificados pela IA.

        Args:
            ai_selectors: Dicionário com tipos de seletores e suas informações
            soup: BeautifulSoup da página para testar
        """
        logger.info("Processando seletores identificados pela IA")

        # Para cada tipo de seletor
        for selector_type, selectors_list in ai_selectors.items():
            logger.info(
                f"Processando {len(selectors_list)} seletores do tipo '{selector_type}'"
            )

            # Verifica se o tipo de seletor existe no gerenciador
            if selector_type not in self.selector_manager.selectors_by_store.get(
                self.store_id, {}
            ):
                self.selector_manager.selectors_by_store.setdefault(self.store_id, {})[
                    selector_type
                ] = []

            # Obtém seletores existentes deste tipo
            existing_selectors = self.selector_manager.selectors_by_store.get(
                self.store_id, {}
            ).get(selector_type, [])
            existing_selector_texts = {
                s.get("selector", ""): s for s in existing_selectors
            }

            # Lista para rastrear seletores válidos identificados pela IA
            valid_ai_selectors = []

            # Processa cada seletor identificado pela IA
            for selector_info in selectors_list:
                selector_text = selector_info.get("selector")
                confidence = selector_info.get("confidence", 0.0)
                description = selector_info.get(
                    "description", f"Seletor para {selector_type}"
                )

                if not selector_text:
                    continue

                # Testa o seletor na página
                try:
                    elements = soup.select(selector_text)
                    is_valid = len(elements) > 0

                    # Verifica se o seletor não é muito genérico
                    if is_valid and len(elements) > 50:
                        logger.info(
                            f"Seletor {selector_text} muito genérico ({len(elements)} elementos). Reduzindo confiança."
                        )
                        confidence = (
                            confidence * 0.7
                        )  # Reduz a confiança para seletores muito genéricos

                    self.stats["tested"] += 1

                    if is_valid:
                        self.stats["valid"] += 1
                        valid_ai_selectors.append(
                            (selector_text, confidence, description)
                        )

                        # Verifica se o seletor já existe
                        if selector_text in existing_selector_texts:
                            # Seletor existente - atualiza status se necessário
                            existing_selector = existing_selector_texts[selector_text]
                            selector_id = existing_selector.get("id")
                            was_active = existing_selector.get("active", False)

                            if not was_active:
                                logger.info(
                                    f"Ativando seletor existente: {selector_text}"
                                )
                                self.selector_manager.update_selector(
                                    selector_id=selector_id,
                                    store_id=self.store_id,
                                    selector_type=selector_type,
                                    selector_text=selector_text,
                                    description=description,
                                    active=True,
                                )
                                self.stats["updated"] += 1
                        else:
                            # Novo seletor - adiciona se tiver confiança suficiente
                            if (
                                confidence >= 0.7
                            ):  # Apenas adiciona seletores com alta confiança
                                logger.info(
                                    f"Adicionando novo seletor: {selector_text} (confiança: {confidence})"
                                )
                                result = self.selector_manager.add_selector(
                                    store_id=self.store_id,
                                    selector_type=selector_type,
                                    selector_text=selector_text,
                                    description=description,
                                    active=True,
                                )
                                if result:
                                    self.stats["new"] += 1
                    else:
                        self.stats["invalid"] += 1
                except Exception as e:
                    logger.warning(f"Erro ao testar seletor {selector_text}: {e}")
                    self.stats["invalid"] += 1

            # Verifica seletores inválidos e tenta substituí-los por seletores válidos da IA
            if valid_ai_selectors:
                # Ordena por confiança (maior primeiro)
                valid_ai_selectors.sort(key=lambda x: x[1], reverse=True)

                # Verifica seletores existentes que não funcionam mais
                invalid_selectors = []
                for existing_selector in existing_selectors:
                    selector_id = existing_selector.get("id")
                    selector_text = existing_selector.get("selector")
                    was_active = existing_selector.get("active", False)

                    # Testa apenas seletores ativos
                    if was_active:
                        try:
                            elements = soup.select(selector_text)
                            is_valid = len(elements) > 0

                            self.stats["tested"] += 1

                            if not is_valid:
                                invalid_selectors.append((selector_id, selector_text))
                                self.stats["invalid"] += 1
                        except Exception as e:
                            logger.warning(
                                f"Erro ao testar seletor existente {selector_text}: {e}"
                            )
                            invalid_selectors.append((selector_id, selector_text))
                            self.stats["invalid"] += 1

                # Substitui seletores inválidos por seletores válidos da IA
                for i, (invalid_id, invalid_text) in enumerate(invalid_selectors):
                    if i < len(valid_ai_selectors):
                        valid_text, _, valid_description = valid_ai_selectors[i]

                        # Verifica se o seletor válido já não existe
                        if valid_text not in existing_selector_texts:
                            logger.info(
                                f"Substituindo seletor inválido '{invalid_text}' por '{valid_text}'"
                            )

                            # Atualiza o seletor inválido com o novo seletor válido
                            self.selector_manager.update_selector(
                                selector_id=invalid_id,
                                store_id=self.store_id,
                                selector_type=selector_type,
                                selector_text=valid_text,
                                description=valid_description,
                                active=True,
                            )
                            self.stats["updated"] += 1
                    else:
                        # Desativa seletores inválidos sem substitutos
                        logger.info(
                            f"Desativando seletor inválido sem substituto: {invalid_text}"
                        )
                        self.selector_manager.update_selector(
                            selector_id=invalid_id,
                            store_id=self.store_id,
                            selector_type=selector_type,
                            selector_text=invalid_text,
                            description=existing_selector_texts.get(
                                invalid_text, {}
                            ).get("description", ""),
                            active=False,
                        )
                        self.stats["updated"] += 1

        # Verifica seletores existentes que não foram identificados pela IA
        for selector_type in self.selector_manager.get_available_types(self.store_id):
            existing_selectors = self.selector_manager.get_selectors_by_type(
                self.store_id, selector_type
            )

            for existing_selector in existing_selectors:
                selector_id = existing_selector.get("id")
                selector_text = existing_selector.get("selector")
                was_active = existing_selector.get("active", False)

                # Testa apenas seletores ativos
                if was_active:
                    # Verifica se o seletor funciona na página atual
                    elements = soup.select(selector_text)
                    is_valid = len(elements) > 0

                    self.stats["tested"] += 1

                    if not is_valid:
                        # Desativa seletores que não funcionam mais
                        logger.info(
                            f"Desativando seletor que não funciona mais: {selector_text}"
                        )
                        self.selector_manager.update_selector(
                            selector_id=selector_id,
                            store_id=self.store_id,
                            selector_type=selector_type,
                            selector_text=selector_text,
                            description=existing_selector.get("description", ""),
                            active=False,
                        )
                        self.stats["invalid"] += 1
                        self.stats["updated"] += 1


def validate_mercadolivre_selectors(
    test_url: str = "https://www.mercadolivre.com.br/ofertas",
) -> Dict:
    """
    Função de conveniência para validar seletores do Mercado Livre.

    Args:
        test_url: URL para testar os seletores

    Returns:
        Dict com estatísticas da validação
    """
    validator = SelectorValidator("mercadolivre")
    return validator.validate_selectors(test_url)
