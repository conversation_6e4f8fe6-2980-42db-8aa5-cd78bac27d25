Metadata-Version: 2.1
Name: zope.interface
Version: 7.2
Summary: Interfaces for Python
Home-page: https://github.com/zopefoundation/zope.interface
Author: Zope Foundation and Contributors
Author-email: <EMAIL>
License: ZPL 2.1
Keywords: interface,components,plugins
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Zope Public License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Framework :: Zope :: 3
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.8
Description-Content-Type: text/x-rst
License-File: LICENSE.txt
Requires-Dist: setuptools
Provides-Extra: docs
Requires-Dist: Sphinx; extra == "docs"
Requires-Dist: repoze.sphinx.autointerface; extra == "docs"
Requires-Dist: furo; extra == "docs"
Provides-Extra: test
Requires-Dist: coverage[toml]; extra == "test"
Requires-Dist: zope.event; extra == "test"
Requires-Dist: zope.testing; extra == "test"
Provides-Extra: testing
Requires-Dist: coverage[toml]; extra == "testing"
Requires-Dist: zope.event; extra == "testing"
Requires-Dist: zope.testing; extra == "testing"

====================
 ``zope.interface``
====================

.. image:: https://img.shields.io/pypi/v/zope.interface.svg
    :target: https://pypi.python.org/pypi/zope.interface/
    :alt: Latest Version

.. image:: https://img.shields.io/pypi/pyversions/zope.interface.svg
        :target: https://pypi.org/project/zope.interface/
        :alt: Supported Python versions

.. image:: https://github.com/zopefoundation/zope.interface/actions/workflows/tests.yml/badge.svg
        :target: https://github.com/zopefoundation/zope.interface/actions/workflows/tests.yml

.. image:: https://readthedocs.org/projects/zopeinterface/badge/?version=latest
        :target: https://zopeinterface.readthedocs.io/en/latest/
        :alt: Documentation Status

This package is intended to be independently reusable in any Python
project. It is maintained by the `Zope Toolkit project
<https://zopetoolkit.readthedocs.io/>`_.

This package provides an implementation of "object interfaces" for Python.
Interfaces are a mechanism for labeling objects as conforming to a given
API or contract. So, this package can be considered as implementation of
the `Design By Contract`_ methodology support in Python.

.. _Design By Contract: http://en.wikipedia.org/wiki/Design_by_contract

For detailed documentation, please see https://zopeinterface.readthedocs.io/en/latest/

=========
 Changes
=========

7.2 (2024-11-28)
================

- Add preliminary support for Python 3.14a2, this means that
  ``.common.builtins.IByteString`` and ``.common.collections.IByteString`` are
  no longer available from this Python version onwards as Python 3.14 dropped
  ``collections.abc.ByteString``.


7.1.1 (2024-10-23)
==================

- Fix segmentation faults in `weakrefobject.c` on Python 3.12 and 3.13.
  (`#323 <https://github.com/zopefoundation/zope.interface/issues/323>`_)


7.1.0 (2024-10-10)
==================

- Declare support for Python 3.13.

- Fix segmentation faults on Python 3.13.
  (`#323 <https://github.com/zopefoundation/zope.interface/issues/323>`_)


7.0.3 (2024-08-27)
==================

- Fix `Assertion 'memb->type == T_PYSSIZET' failed.` for Python < 3.12.
  (`#319 <https://github.com/zopefoundation/zope.interface/issues/319>`_)


7.0.2 (2024-08-26)
==================

- Fix reference-counting bug in C module initialization (broken in 7.0).
  (`#316 <https://github.com/zopefoundation/zope.interface/issues/316>`_)


7.0.1 (2024-08-06)
==================

- Fix subclassability of ``ObjectSpecificationDescriptor`` (broken in 7.0).
  (`#312 <https://github.com/zopefoundation/zope.interface/issues/312>`_)


7.0 (2024-08-06)
================

- Enable heap-based types (PEP 384) for Python >= 3.11.

- Adopt multi-phase module initialization (PEP 489).

- Drop support for Python 3.7.


6.4.post2 (2024-05-24)
======================

- Publish missing Windows wheels, second attempt.
  (`#295 <https://github.com/zopefoundation/zope.interface/issues/295>`_)


6.4.post1 (2024-05-23)
======================

- Publish missing Windows wheels.
  (`#295 <https://github.com/zopefoundation/zope.interface/issues/295>`_)


6.4.post0 (2024-05-22)
======================

- The sdist of version 6.4 was uploaded to PyPI as
  ``zope_interface-6.4.tar.gz`` instead of ``zope.interface-6.4-py2.tar.gz``
  which cannot be installed by ``zc.buildout``.  This release is a re-release
  of version 6.4 with the correct sdist name.
  (`#298 <https://github.com/zopefoundation/zope.interface/issues/298>`_)


6.4 (2024-05-15)
================

- Adjust for incompatible changes in Python 3.13b1.
  (`#292 <https://github.com/zopefoundation/zope.interface/issues/292>`_)

- Build windows wheels on GHA.

6.3 (2024-04-12)
================

- Add preliminary support for Python 3.13 as of 3.13a6.


6.2 (2024-02-16)
================

- Add preliminary support for Python 3.13 as of 3.13a3.

- Add support to use the pipe (``|``) syntax for ``typing.Union``.
  (`#280 <https://github.com/zopefoundation/zope.interface/issues/280>`_)


6.1 (2023-10-05)
================

- Build Linux binary wheels for Python 3.12.

- Add support for Python 3.12.

- Fix building of the docs for non-final versions.


6.0 (2023-03-17)
================

- Build Linux binary wheels for Python 3.11.

- Drop support for Python 2.7, 3.5, 3.6.

- Fix test deprecation warning on Python 3.11.

- Add preliminary support for Python 3.12 as of 3.12a5.

- Drop:

    + `zope.interface.implements`
    + `zope.interface.implementsOnly`
    + `zope.interface.classProvides`


5.5.2 (2022-11-17)
==================

- Add support for building arm64 wheels on macOS.


5.5.1 (2022-11-03)
==================

- Add support for final Python 3.11 release.


5.5.0 (2022-10-10)
==================

- Add support for Python 3.10 and 3.11 (as of 3.11.0rc2).

- Add missing Trove classifier showing support for Python 3.9.

- Add some more entries to ``zope.interface.interfaces.__all__``.

- Disable unsafe math optimizations in C code.  See `pull request 262
  <https://github.com/zopefoundation/zope.interface/pull/262>`_.


5.4.0 (2021-04-15)
==================

- Make the C implementation of the ``__providedBy__`` descriptor stop
  ignoring all errors raised when accessing the instance's
  ``__provides__``. Now it behaves like the Python version and only
  catches ``AttributeError``. The previous behaviour could lead to
  crashing the interpreter in cases of recursion and errors. See
  `issue 239 <https://github.com/zopefoundation/zope.interface/issues>`_.

- Update the ``repr()`` and ``str()`` of various objects to be shorter
  and more informative. In many cases, the ``repr()`` is now something
  that can be evaluated to produce an equal object. For example, what
  was previously printed as ``<implementedBy builtins.list>`` is now
  shown as ``classImplements(list, IMutableSequence, IIterable)``. See
  `issue 236 <https://github.com/zopefoundation/zope.interface/issues/236>`_.

- Make ``Declaration.__add__`` (as in ``implementedBy(Cls) +
  ISomething``) try harder to preserve a consistent resolution order
  when the two arguments share overlapping pieces of the interface
  inheritance hierarchy. Previously, the right hand side was always
  put at the end of the resolution order, which could easily produce
  invalid orders. See `issue 193
  <https://github.com/zopefoundation/zope.interface/issues/193>`_.

5.3.0 (2020-03-21)
==================

- No changes from 5.3.0a1


5.3.0a1 (2021-03-18)
====================

- Improve the repr of ``zope.interface.Provides`` to remove ambiguity
  about what is being provided. This is especially helpful diagnosing
  IRO issues.

- Allow subclasses of ``BaseAdapterRegistry`` (including
  ``AdapterRegistry`` and ``VerifyingAdapterRegistry``) to have
  control over the data structures. This allows persistent
  implementations such as those based on ZODB to choose more scalable
  options (e.g., BTrees instead of dicts). See `issue 224
  <https://github.com/zopefoundation/zope.interface/issues/224>`_.

- Fix a reference counting issue in ``BaseAdapterRegistry`` that could
  lead to references to interfaces being kept around even when all
  utilities/adapters/subscribers providing that interface have been
  removed. This is mostly an issue for persistent implementations.
  Note that this only corrects the issue moving forward, it does not
  solve any already corrupted reference counts. See `issue 227
  <https://github.com/zopefoundation/zope.interface/issues/227>`_.

- Add the method ``BaseAdapterRegistry.rebuild()``. This can be used
  to fix the reference counting issue mentioned above, as well as to
  update the data structures when custom data types have changed.

- Add the interface method ``IAdapterRegistry.subscribed()`` and
  implementation ``BaseAdapterRegistry.subscribed()`` for querying
  directly registered subscribers. See `issue 230
  <https://github.com/zopefoundation/zope.interface/issues/230>`_.

- Add the maintenance method
  ``Components.rebuildUtilityRegistryFromLocalCache()``. Most users
  will not need this, but it can be useful if the ``Components.utilities``
  registry is suspected to be out of sync with the ``Components``
  object itself (this might happen to persistent ``Components``
  implementations in the face of bugs).

- Fix the ``Provides`` and ``ClassProvides`` descriptors to stop
  allowing redundant interfaces (those already implemented by the
  underlying class or meta class) to produce an inconsistent
  resolution order. This is similar to the change in ``@implementer``
  in 5.1.0, and resolves inconsistent resolution orders with
  ``zope.proxy`` and ``zope.location``. See `issue 207
  <https://github.com/zopefoundation/zope.interface/issues/207>`_.

5.2.0 (2020-11-05)
==================

- Add documentation section ``Persistency and Equality``
  (`#218 <https://github.com/zopefoundation/zope.interface/issues/218>`_).

- Create arm64 wheels.

- Add support for Python 3.9.


5.1.2 (2020-10-01)
==================

- Make sure to call each invariant only once when validating invariants.
  Previously, invariants could be called multiple times because when an
  invariant is defined in an interface, it's found by in all interfaces
  inheriting from that interface.  See `pull request 215
  <https://github.com/zopefoundation/zope.interface/pull/215/>`_.

5.1.1 (2020-09-30)
==================

- Fix the method definitions of ``IAdapterRegistry.subscribe``,
  ``subscriptions`` and ``subscribers``. Previously, they all were
  defined to accept a ``name`` keyword argument, but subscribers have
  no names and the implementation of that interface did not accept
  that argument. See `issue 208
  <https://github.com/zopefoundation/zope.interface/issues/208>`_.

- Fix a potential reference leak in the C optimizations. Previously,
  applications that dynamically created unique ``Specification``
  objects (e.g., used ``@implementer`` on dynamic classes) could
  notice a growth of small objects over time leading to increased
  garbage collection times. See `issue 216
  <https://github.com/zopefoundation/zope.interface/issues/216>`_.

  .. caution::

     This leak could prevent interfaces used as the bases of
     other interfaces from being garbage collected. Those interfaces
     will now be collected.

     One way in which this would manifest was that ``weakref.ref``
     objects (and things built upon them, like
     ``Weak[Key|Value]Dictionary``) would continue to have access to
     the original object even if there were no other visible
     references to Python and the original object *should* have been
     collected. This could be especially problematic for the
     ``WeakKeyDictionary`` when combined with dynamic or local
     (created in the scope of a function) interfaces, since interfaces
     are hashed based just on their name and module name. See the
     linked issue for an example of a resulting ``KeyError``.

     Note that such potential errors are not new, they are just once
     again a possibility.

5.1.0 (2020-04-08)
==================

- Make ``@implementer(*iface)`` and ``classImplements(cls, *iface)``
  ignore redundant interfaces. If the class already implements an
  interface through inheritance, it is no longer redeclared
  specifically for *cls*. This solves many instances of inconsistent
  resolution orders, while still allowing the interface to be declared
  for readability and maintenance purposes. See `issue 199
  <https://github.com/zopefoundation/zope.interface/issues/199>`_.

- Remove all bare ``except:`` statements. Previously, when accessing
  special attributes such as ``__provides__``, ``__providedBy__``,
  ``__class__`` and ``__conform__``, this package wrapped such access
  in a bare ``except:`` statement, meaning that many errors could pass
  silently; typically this would result in a fallback path being taken
  and sometimes (like with ``providedBy()``) the result would be
  non-sensical. This is especially true when those attributes are
  implemented with descriptors. Now, only ``AttributeError`` is
  caught. This makes errors more obvious.

  Obviously, this means that some exceptions will be propagated
  differently than before. In particular, ``RuntimeError`` raised by
  Acquisition in the case of circular containment will now be
  propagated. Previously, when adapting such a broken object, a
  ``TypeError`` would be the common result, but now it will be a more
  informative ``RuntimeError``.

  In addition, ZODB errors like ``POSKeyError`` could now be
  propagated where previously they would ignored by this package.

  See `issue 200 <https://github.com/zopefoundation/zope.interface/issues/200>`_.

- Require that the second argument (*bases*) to ``InterfaceClass`` is
  a tuple. This only matters when directly using ``InterfaceClass`` to
  create new interfaces dynamically. Previously, an individual
  interface was allowed, but did not work correctly. Now it is
  consistent with ``type`` and requires a tuple.

- Let interfaces define custom ``__adapt__`` methods. This implements
  the other side of the :pep:`246` adaptation protocol: objects being
  adapted could already implement ``__conform__`` if they know about
  the interface, and now interfaces can implement ``__adapt__`` if
  they know about particular objects. There is no performance penalty
  for interfaces that do not supply custom ``__adapt__`` methods.

  This includes the ability to add new methods, or override existing
  interface methods using the new ``@interfacemethod`` decorator.

  See `issue 3 <https://github.com/zopefoundation/zope.interface/issues/3>`_.

- Make the internal singleton object returned by APIs like
  ``implementedBy`` and ``directlyProvidedBy`` for objects that
  implement or provide no interfaces more immutable. Previously an
  internal cache could be mutated. See `issue 204
  <https://github.com/zopefoundation/zope.interface/issues/204>`_.

5.0.2 (2020-03-30)
==================

- Ensure that objects that implement no interfaces (such as direct
  subclasses of ``object``) still include ``Interface`` itself in
  their ``__iro___`` and ``__sro___``. This fixes adapter registry
  lookups for such objects when the adapter is registered for
  ``Interface``. See `issue 197
  <https://github.com/zopefoundation/zope.interface/issues/197>`_.


5.0.1 (2020-03-21)
==================

- Ensure the resolution order for ``InterfaceClass`` is consistent.
  See `issue 192 <https://github.com/zopefoundation/zope.interface/issues/192>`_.

- Ensure the resolution order for ``collections.OrderedDict`` is
  consistent on CPython 2. (It was already consistent on Python 3 and PyPy).

- Fix the handling of the ``ZOPE_INTERFACE_STRICT_IRO`` environment
  variable. Previously, ``ZOPE_INTERFACE_STRICT_RO`` was read, in
  contrast with the documentation. See `issue 194
  <https://github.com/zopefoundation/zope.interface/issues/194>`_.


5.0.0 (2020-03-19)
==================

- Make an internal singleton object returned by APIs like
  ``implementedBy`` and ``directlyProvidedBy`` immutable. Previously,
  it was fully mutable and allowed changing its ``__bases___``. That
  could potentially lead to wrong results in pathological corner
  cases. See `issue 158
  <https://github.com/zopefoundation/zope.interface/issues/158>`_.

- Support the ``PURE_PYTHON`` environment variable at runtime instead
  of just at wheel build time. A value of 0 forces the C extensions to
  be used (even on PyPy) failing if they aren't present. Any other
  value forces the Python implementation to be used, ignoring the C
  extensions. See `PR 151 <https://github.com/zopefoundation/zope.interface/pull/151>`_.

- Cache the result of ``__hash__`` method in ``InterfaceClass`` as a
  speed optimization. The method is called very often (i.e several
  hundred thousand times during Plone 5.2 startup). Because the hash value never
  changes it can be cached. This improves test performance from 0.614s
  down to 0.575s (1.07x faster). In a real world Plone case a reindex
  index came down from 402s to 320s (1.26x faster). See `PR 156
  <https://github.com/zopefoundation/zope.interface/pull/156>`_.

- Change the C classes ``SpecificationBase`` and its subclass
  ``ClassProvidesBase`` to store implementation attributes in their structures
  instead of their instance dictionaries. This eliminates the use of
  an undocumented private C API function, and helps make some
  instances require less memory. See `PR 154 <https://github.com/zopefoundation/zope.interface/pull/154>`_.

- Reduce memory usage in other ways based on observations of usage
  patterns in Zope (3) and Plone code bases.

  - Specifications with no dependents are common (more than 50%) so
    avoid allocating a ``WeakKeyDictionary`` unless we need it.
  - Likewise, tagged values are relatively rare, so don't allocate a
    dictionary to hold them until they are used.
  - Use ``__slots___`` or the C equivalent ``tp_members`` in more
    common places. Note that this removes the ability to set arbitrary
    instance variables on certain objects.
    See `PR 155 <https://github.com/zopefoundation/zope.interface/pull/155>`_.

  The changes in this release resulted in a 7% memory reduction after
  loading about 6,000 modules that define about 2,200 interfaces.

  .. caution::

     Details of many private attributes have changed, and external use
     of those private attributes may break. In particular, the
     lifetime and default value of ``_v_attrs`` has changed.

- Remove support for hashing uninitialized interfaces. This could only
  be done by subclassing ``InterfaceClass``. This has generated a
  warning since it was first added in 2011 (3.6.5). Please call the
  ``InterfaceClass`` constructor or otherwise set the appropriate
  fields in your subclass before attempting to hash or sort it. See
  `issue 157 <https://github.com/zopefoundation/zope.interface/issues/157>`_.

- Remove unneeded override of the ``__hash__`` method from
  ``zope.interface.declarations.Implements``. Watching a reindex index
  process in ZCatalog with on a Py-Spy after 10k samples the time for
  ``.adapter._lookup`` was reduced from 27.5s to 18.8s (~1.5x faster).
  Overall reindex index time shrunk from 369s to 293s (1.26x faster).
  See `PR 161
  <https://github.com/zopefoundation/zope.interface/pull/161>`_.

- Make the Python implementation closer to the C implementation by
  ignoring all exceptions, not just ``AttributeError``, during (parts
  of) interface adaptation. See `issue 163
  <https://github.com/zopefoundation/zope.interface/issues/163>`_.

- Micro-optimization in ``.adapter._lookup`` , ``.adapter._lookupAll``
  and ``.adapter._subscriptions``: By loading ``components.get`` into
  a local variable before entering the loop a bytcode "LOAD_FAST 0
  (components)" in the loop can be eliminated. In Plone, while running
  all tests, average speedup of the "owntime" of ``_lookup`` is ~5x.
  See `PR 167
  <https://github.com/zopefoundation/zope.interface/pull/167>`_.

- Add ``__all__`` declarations to all modules. This helps tools that
  do auto-completion and documentation and results in less cluttered
  results. Wildcard ("*") are not recommended and may be affected. See
  `issue 153
  <https://github.com/zopefoundation/zope.interface/issues/153>`_.

- Fix ``verifyClass`` and ``verifyObject`` for builtin types like
  ``dict`` that have methods taking an optional, unnamed argument with
  no default value like ``dict.pop``. On PyPy3, the verification is
  strict, but on PyPy2 (as on all versions of CPython) those methods
  cannot be verified and are ignored. See `issue 118
  <https://github.com/zopefoundation/zope.interface/issues/118>`_.

- Update the common interfaces ``IEnumerableMapping``,
  ``IExtendedReadMapping``, ``IExtendedWriteMapping``,
  ``IReadSequence`` and ``IUniqueMemberWriteSequence`` to no longer
  require methods that were removed from Python 3 on Python 3, such as
  ``__setslice___``. Now, ``dict``, ``list`` and ``tuple`` properly
  verify as ``IFullMapping``, ``ISequence`` and ``IReadSequence,``
  respectively on all versions of Python.

- Add human-readable ``__str___`` and ``__repr___`` to ``Attribute``
  and ``Method``. These contain the name of the defining interface
  and the attribute. For methods, it also includes the signature.

- Change the error strings raised by ``verifyObject`` and
  ``verifyClass``. They now include more human-readable information
  and exclude extraneous lines and spaces. See `issue 170
  <https://github.com/zopefoundation/zope.interface/issues/170>`_.

  .. caution:: This will break consumers (such as doctests) that
               depended on the exact error messages.

- Make ``verifyObject`` and ``verifyClass`` report all errors, if the
  candidate object has multiple detectable violations. Previously they
  reported only the first error. See `issue
  <https://github.com/zopefoundation/zope.interface/issues/171>`_.

  Like the above, this will break consumers depending on the exact
  output of error messages if more than one error is present.

- Add ``zope.interface.common.collections``,
  ``zope.interface.common.numbers``, and ``zope.interface.common.io``.
  These modules define interfaces based on the ABCs defined in the
  standard library ``collections.abc``, ``numbers`` and ``io``
  modules, respectively. Importing these modules will make the
  standard library concrete classes that are registered with those
  ABCs declare the appropriate interface. See `issue 138
  <https://github.com/zopefoundation/zope.interface/issues/138>`_.

- Add ``zope.interface.common.builtins``. This module defines
  interfaces of common builtin types, such as ``ITextString`` and
  ``IByteString``, ``IDict``, etc. These interfaces extend the
  appropriate interfaces from ``collections`` and ``numbers``, and the
  standard library classes implement them after importing this module.
  This is intended as a replacement for third-party packages like
  `dolmen.builtins <https://pypi.org/project/dolmen.builtins/>`_.
  See `issue 138 <https://github.com/zopefoundation/zope.interface/issues/138>`_.

- Make ``providedBy()`` and ``implementedBy()`` respect ``super``
  objects. For instance, if class ``Derived`` implements ``IDerived``
  and extends ``Base`` which in turn implements ``IBase``, then
  ``providedBy(super(Derived, derived))`` will return ``[IBase]``.
  Previously it would have returned ``[IDerived]`` (in general, it
  would previously have returned whatever would have been returned
  without ``super``).

  Along with this change, adapter registries will unpack ``super``
  objects into their ``__self___`` before passing it to the factory.
  Together, this means that ``component.getAdapter(super(Derived,
  self), ITarget)`` is now meaningful.

  See `issue 11 <https://github.com/zopefoundation/zope.interface/issues/11>`_.

- Fix a potential interpreter crash in the low-level adapter
  registry lookup functions. See issue 11.

- Adopt Python's standard `C3 resolution order
  <https://www.python.org/download/releases/2.3/mro/>`_ to compute the
  ``__iro__`` and ``__sro__`` of interfaces, with tweaks to support
  additional cases that are common in interfaces but disallowed for
  Python classes. Previously, an ad-hoc ordering that made no
  particular guarantees was used.

  This has many beneficial properties, including the fact that base
  interface and base classes tend to appear near the end of the
  resolution order instead of the beginning. The resolution order in
  general should be more predictable and consistent.

  .. caution::
     In some cases, especially with complex interface inheritance
     trees or when manually providing or implementing interfaces, the
     resulting IRO may be quite different. This may affect adapter
     lookup.

  The C3 order enforces some constraints in order to be able to
  guarantee a sensible ordering. Older versions of zope.interface did
  not impose similar constraints, so it was possible to create
  interfaces and declarations that are inconsistent with the C3
  constraints. In that event, zope.interface will still produce a
  resolution order equal to the old order, but it won't be guaranteed
  to be fully C3 compliant. In the future, strict enforcement of C3
  order may be the default.

  A set of environment variables and module constants allows
  controlling several aspects of this new behaviour. It is possible to
  request warnings about inconsistent resolution orders encountered,
  and even to forbid them. Differences between the C3 resolution order
  and the previous order can be logged, and, in extreme cases, the
  previous order can still be used (this ability will be removed in
  the future). For details, see the documentation for
  ``zope.interface.ro``.

- Make inherited tagged values in interfaces respect the resolution
  order (``__iro__``), as method and attribute lookup does. Previously
  tagged values could give inconsistent results. See `issue 190
  <https://github.com/zopefoundation/zope.interface/issues/190>`_.

- Add ``getDirectTaggedValue`` (and related methods) to interfaces to
  allow accessing tagged values irrespective of inheritance. See
  `issue 190
  <https://github.com/zopefoundation/zope.interface/issues/190>`_.

- Ensure that ``Interface`` is always the last item in the ``__iro__``
  and ``__sro__``. This is usually the case, but if classes that do
  not implement any interfaces are part of a class inheritance
  hierarchy, ``Interface`` could be assigned too high a priority.
  See `issue 8 <https://github.com/zopefoundation/zope.interface/issues/8>`_.

- Implement sorting, equality, and hashing in C for ``Interface``
  objects. In micro benchmarks, this makes those operations 40% to 80%
  faster. This translates to a 20% speed up in querying adapters.

  Note that this changes certain implementation details. In
  particular, ``InterfaceClass`` now has a non-default metaclass, and
  it is enforced that ``__module__`` in instances of
  ``InterfaceClass`` is read-only.

  See `PR 183 <https://github.com/zopefoundation/zope.interface/pull/183>`_.


4.7.2 (2020-03-10)
==================

- Remove deprecated use of setuptools features.  See `issue 30
  <https://github.com/zopefoundation/zope.interface/issues/30>`_.


4.7.1 (2019-11-11)
==================

- Use Python 3 syntax in the documentation.  See `issue 119
  <https://github.com/zopefoundation/zope.interface/issues/119>`_.


4.7.0 (2019-11-11)
==================

- Drop support for Python 3.4.

- Change ``queryTaggedValue``, ``getTaggedValue``,
  ``getTaggedValueTags`` in interfaces. They now include inherited
  values by following ``__bases__``. See `PR 144
  <https://github.com/zopefoundation/zope.interface/pull/144>`_.

  .. caution:: This may be a breaking change.

- Add support for Python 3.8.


4.6.0 (2018-10-23)
==================

- Add support for Python 3.7

- Fix ``verifyObject`` for class objects with staticmethods on
  Python 3. See `issue 126
  <https://github.com/zopefoundation/zope.interface/issues/126>`_.


4.5.0 (2018-04-19)
==================

- Drop support for 3.3, avoid accidental dependence breakage via setup.py.
  See `PR 110 <https://github.com/zopefoundation/zope.interface/pull/110>`_.
- Allow registering and unregistering instance methods as listeners.
  See `issue 12 <https://github.com/zopefoundation/zope.interface/issues/12>`_
  and `PR 102 <https://github.com/zopefoundation/zope.interface/pull/102>`_.
- Synchronize and simplify zope/__init__.py. See `issue 114
  <https://github.com/zopefoundation/zope.interface/issues/114>`_


4.4.3 (2017-09-22)
==================

- Avoid exceptions when the ``__annotations__`` attribute is added to
  interface definitions with Python 3.x type hints. See `issue 98
  <https://github.com/zopefoundation/zope.interface/issues/98>`_.
- Fix the possibility of a rare crash in the C extension when
  deallocating items. See `issue 100
  <https://github.com/zopefoundation/zope.interface/issues/100>`_.


4.4.2 (2017-06-14)
==================

- Fix a regression storing
  ``zope.component.persistentregistry.PersistentRegistry`` instances.
  See `issue 85 <https://github.com/zopefoundation/zope.interface/issues/85>`_.

- Fix a regression that could lead to the utility registration cache
  of ``Components`` getting out of sync. See `issue 93
  <https://github.com/zopefoundation/zope.interface/issues/93>`_.

4.4.1 (2017-05-13)
==================

- Simplify the caching of utility-registration data. In addition to
  simplification, avoids spurious test failures when checking for
  leaks in tests with persistent registries. See `pull 84
  <https://github.com/zopefoundation/zope.interface/pull/84>`_.

- Raise ``ValueError`` when non-text names are passed to adapter registry
  methods:  prevents corruption of lookup caches.

4.4.0 (2017-04-21)
==================

- Avoid a warning from the C compiler.
  (https://github.com/zopefoundation/zope.interface/issues/71)

- Add support for Python 3.6.

4.3.3 (2016-12-13)
==================

- Correct typos and ReST formatting errors in documentation.

- Add API documentation for the adapter registry.

- Ensure that the ``LICENSE.txt`` file is included in built wheels.

- Fix C optimizations broken on Py3k.  See the Python bug at:
  http://bugs.python.org/issue15657
  (https://github.com/zopefoundation/zope.interface/issues/60)


4.3.2 (2016-09-05)
==================

- Fix equality testing of ``implementedBy`` objects and proxies.
  (https://github.com/zopefoundation/zope.interface/issues/55)


4.3.1 (2016-08-31)
==================

- Support Components subclasses that are not hashable.
  (https://github.com/zopefoundation/zope.interface/issues/53)


4.3.0 (2016-08-31)
==================

- Add the ability to sort the objects returned by ``implementedBy``.
  This is compatible with the way interface classes sort so they can
  be used together in ordered containers like BTrees.
  (https://github.com/zopefoundation/zope.interface/issues/42)

- Make ``setuptools`` a hard dependency of ``setup.py``.
  (https://github.com/zopefoundation/zope.interface/issues/13)

- Change a linear algorithm (O(n)) in ``Components.registerUtility`` and
  ``Components.unregisterUtility`` into a dictionary lookup (O(1)) for
  hashable components. This substantially improves the time taken to
  manipulate utilities in large registries at the cost of some
  additional memory usage. (https://github.com/zopefoundation/zope.interface/issues/46)


4.2.0 (2016-06-10)
==================

- Add support for Python 3.5

- Drop support for Python 2.6 and 3.2.


4.1.3 (2015-10-05)
==================

- Fix installation without a C compiler on Python 3.5
  (https://github.com/zopefoundation/zope.interface/issues/24).


4.1.2 (2014-12-27)
==================

- Add support for PyPy3.

- Remove unittest assertions deprecated in Python3.x.

- Add ``zope.interface.document.asReStructuredText``, which formats the
  generated text for an interface using ReST double-backtick markers.


4.1.1 (2014-03-19)
==================

- Add support for Python 3.4.


4.1.0 (2014-02-05)
==================

- Update ``boostrap.py`` to version 2.2.

- Add ``@named(name)`` declaration, that specifies the component name, so it
  does not have to be passed in during registration.


4.0.5 (2013-02-28)
==================

- Fix a bug where a decorated method caused false positive failures on
  ``verifyClass()``.


4.0.4 (2013-02-21)
==================

- Fix a bug that was revealed by porting zope.traversing. During a loop, the
  loop body modified a weakref dict causing a ``RuntimeError`` error.

4.0.3 (2012-12-31)
==================

- Fleshed out PyPI Trove classifiers.

4.0.2 (2012-11-21)
==================

- Add support for Python 3.3.

- Restored ability to install the package in the absence of ``setuptools``.

- LP #1055223:  Fix test which depended on dictionary order and failed randomly
  in Python 3.3.

4.0.1 (2012-05-22)
==================

- Drop explicit ``DeprecationWarnings`` for "class advice" APIS (these
  APIs are still deprecated under Python 2.x, and still raise an exception
  under Python 3.x, but no longer cause a warning to be emitted under
  Python 2.x).

4.0.0 (2012-05-16)
==================

- Automated build of Sphinx HTML docs and running doctest snippets via tox.

- Deprecate the "class advice" APIs from ``zope.interface.declarations``:
  ``implements``, ``implementsOnly``, and ``classProvides``.  In their place,
  prefer the equivalent class decorators: ``@implementer``,
  ``@implementer_only``, and ``@provider``.  Code which uses the deprecated
  APIs will not work as expected under Py3k.

- Remove use of '2to3' and associated fixers when installing under Py3k.
  The code is now in a "compatible subset" which supports Python 2.6, 2.7,
  and 3.2, including PyPy 1.8 (the version compatible with the 2.7 language
  spec).

- Drop explicit support for Python 2.4 / 2.5 / 3.1.

- Add support for PyPy.

- Add support for continuous integration using ``tox`` and ``jenkins``.

- Add 'setup.py dev' alias (runs ``setup.py develop`` plus installs
  ``nose`` and ``coverage``).

- Add 'setup.py docs' alias (installs ``Sphinx`` and dependencies).

- Replace all unittest coverage previously accomplished via doctests with
  unittests.  The doctests have been moved into a ``docs`` section, managed
  as a Sphinx collection.

- LP #910987:  Ensure that the semantics of the ``lookup`` method of
  ``zope.interface.adapter.LookupBase`` are the same in both the C and
  Python implementations.

- LP #900906:  Avoid exceptions due to tne new ``__qualname__`` attribute
  added in Python 3.3 (see PEP 3155 for rationale).  Thanks to Antoine
  Pitrou for the patch.

3.8.0 (2011-09-22)
==================

- New module ``zope.interface.registry``.  This is code moved from
  ``zope.component.registry`` which implements a basic nonperistent component
  registry as ``zope.interface.registry.Components``.  This class was moved
  from ``zope.component`` to make porting systems (such as Pyramid) that rely
  only on a basic component registry to Python 3 possible without needing to
  port the entirety of the ``zope.component`` package.  Backwards
  compatibility import shims have been left behind in ``zope.component``, so
  this change will not break any existing code.

- New ``tests_require`` dependency: ``zope.event`` to test events sent by
  Components implementation.  The ``zope.interface`` package does not have a
  hard dependency on ``zope.event``, but if ``zope.event`` is importable, it
  will send component registration events when methods of an instance of
  ``zope.interface.registry.Components`` are called.

- New interfaces added to support ``zope.interface.registry.Components``
  addition: ``ComponentLookupError``, ``Invalid``, ``IObjectEvent``,
  ``ObjectEvent``, ``IComponentLookup``, ``IRegistration``,
  ``IUtilityRegistration``, ``IAdapterRegistration``,
  ``ISubscriptionAdapterRegistration``, ``IHandlerRegistration``,
  ``IRegistrationEvent``, ``RegistrationEvent``, ``IRegistered``,
  ``Registered``, ``IUnregistered``, ``Unregistered``,
  ``IComponentRegistry``, and ``IComponents``.

- No longer Python 2.4 compatible (tested under 2.5, 2.6, 2.7, and 3.2).

3.7.0 (2011-08-13)
==================

- Move changes from 3.6.2 - 3.6.5 to a new 3.7.x release line.

3.6.7 (2011-08-20)
==================

- Fix sporadic failures on x86-64 platforms in tests of rich comparisons
  of interfaces.

3.6.6 (2011-08-13)
==================

- LP #570942:  Now correctly compare interfaces  from different modules but
  with the same names.

  N.B.: This is a less intrusive / destabilizing fix than the one applied in
  3.6.3:  we only fix the underlying cmp-alike function, rather than adding
  the other "rich comparison" functions.

- Revert to software as released with 3.6.1 for "stable" 3.6 release branch.

3.6.5 (2011-08-11)
==================

- LP #811792:  work around buggy behavior in some subclasses of
  ``zope.interface.interface.InterfaceClass``, which invoke ``__hash__``
  before initializing ``__module__`` and ``__name__``.  The workaround
  returns a fixed constant hash in such cases, and issues a ``UserWarning``.

- LP #804832:  Under PyPy, ``zope.interface`` should not build its C
  extension.  Also, prevent attempting to build it under Jython.

- Add a tox.ini for easier xplatform testing.

- Fix testing deprecation warnings issued when tested under Py3K.

3.6.4 (2011-07-04)
==================

- LP 804951:  InterfaceClass instances were unhashable under Python 3.x.

3.6.3 (2011-05-26)
==================

- LP #570942:  Now correctly compare interfaces  from different modules but
  with the same names.

3.6.2 (2011-05-17)
==================

- Moved detailed documentation out-of-line from PyPI page, linking instead to
  http://docs.zope.org/zope.interface .

- Fixes for small issues when running tests under Python 3.2 using
  ``zope.testrunner``.

- LP # 675064:  Specify return value type for C optimizations module init
  under Python 3:  undeclared value caused warnings, and segfaults on some
  64 bit architectures.

- setup.py now raises RuntimeError if you don't have Distutils installed when
  running under Python 3.

3.6.1 (2010-05-03)
==================

- A non-ASCII character in the changelog made 3.6.0 uninstallable on
  Python 3 systems with another default encoding than UTF-8.

- Fix compiler warnings under GCC 4.3.3.

3.6.0 (2010-04-29)
==================

- LP #185974:  Clear the cache used by ``Specificaton.get`` inside
  ``Specification.changed``.  Thanks to Jacob Holm for the patch.

- Add support for Python 3.1. Contributors:

    Lennart Regebro
    Martin v Loewis
    Thomas Lotze
    Wolfgang Schnerring

  The 3.1 support is completely backwards compatible. However, the implements
  syntax used under Python 2.X does not work under 3.X, since it depends on
  how metaclasses are implemented and this has changed. Instead it now supports
  a decorator syntax (also under Python 2.X)::

    class Foo:
        implements(IFoo)
        ...

  can now also be written::

    @implementer(IFoo):
    class Foo:
        ...

  There are 2to3 fixers available to do this change automatically in the
  zope.fixers package.

- Python 2.3 is no longer supported.


3.5.4 (2009-12-23)
==================

- Use the standard Python doctest module instead of zope.testing.doctest, which
  has been deprecated.


3.5.3 (2009-12-08)
==================

- Fix an edge case: make providedBy() work when a class has '__provides__' in
  its __slots__ (see http://thread.gmane.org/gmane.comp.web.zope.devel/22490)


3.5.2 (2009-07-01)
==================

- BaseAdapterRegistry.unregister, unsubscribe: Remove empty portions of
  the data structures when something is removed.  This avoids leaving
  references to global objects (interfaces) that may be slated for
  removal from the calling application.


3.5.1 (2009-03-18)
==================

- verifyObject: use getattr instead of hasattr to test for object attributes
  in order to let exceptions other than AttributeError raised by properties
  propagate to the caller

- Add Sphinx-based documentation building to the package buildout
  configuration. Use the ``bin/docs`` command after buildout.

- Improve package description a bit. Unify changelog entries formatting.

- Change package's mailing list address to zope-dev at zope.org as
  zope3-dev at zope.org is now retired.


3.5.0 (2008-10-26)
==================

- Fix declaration of _zope_interface_coptimizations, it's not a top level
  package.

- Add a DocTestSuite for odd.py module, so their tests are run.

- Allow to bootstrap on Jython.

- Fix https://bugs.launchpad.net/zope3/3.3/+bug/98388: ISpecification
  was missing a declaration for __iro__.

- Add optional code optimizations support, which allows the building
  of C code optimizations to fail (Jython).

- Replace `_flatten` with a non-recursive implementation, effectively making
  it 3x faster.


3.4.1 (2007-10-02)
==================

- Fix a setup bug that prevented installation from source on systems
  without setuptools.


3.4.0 (2007-07-19)
==================

- Final release for 3.4.0.


3.4.0b3 (2007-05-22)
====================


- When checking whether an object is already registered, use identity
  comparison, to allow adding registering with picky custom comparison methods.


3.3.0.1 (2007-01-03)
====================

- Made a reference to OverflowWarning, which disappeared in Python
  2.5, conditional.


3.3.0 (2007/01/03)
==================

New Features
------------

- Refactor the adapter-lookup algorithim to make it much simpler and faster.

  Also, implement more of the adapter-lookup logic in C, making
  debugging of application code easier, since there is less
  infrastructre code to step through.

- Treat objects without interface declarations as if they
  declared that they provide ``zope.interface.Interface``.

- Add a number of richer new adapter-registration interfaces
  that provide greater control and introspection.

- Add a new interface decorator to zope.interface that allows the
  setting of tagged values on an interface at definition time (see
  zope.interface.taggedValue).

Bug Fixes
---------

- A bug in multi-adapter lookup sometimes caused incorrect adapters to
  be returned.


******* (2006-04-15)
====================

- Fix packaging bug:  'package_dir' must be a *relative* path.


******* (2006-04-14)
====================

- Packaging change:  suppress inclusion of 'setup.cfg' in 'sdist' builds.


3.2.0 (2006-01-05)
==================

- Corresponds to the version of the zope.interface package shipped as part of
  the Zope 3.2.0 release.


3.1.0 (2005-10-03)
==================

- Corresponds to the version of the zope.interface package shipped as part of
  the Zope 3.1.0 release.

- Made attribute resolution order consistent with component lookup order,
  i.e. new-style class MRO semantics.

- Deprecate 'isImplementedBy' and 'isImplementedByInstancesOf' APIs in
  favor of 'implementedBy' and 'providedBy'.


3.0.1 (2005-07-27)
==================

- Corresponds to the version of the zope.interface package shipped as part of
  the Zope X3.0.1 release.

- Fix a bug reported by James Knight, which caused adapter registries
  to fail occasionally to reflect declaration changes.


3.0.0 (2004-11-07)
==================

- Corresponds to the version of the zope.interface package shipped as part of
  the Zope X3.0.0 release.
