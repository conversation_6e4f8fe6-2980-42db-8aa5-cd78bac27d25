import json
import logging
import os
import re
from typing import Dict, Optional, Tuple

logger = logging.getLogger(__name__)


class ProductCategories:
    """Carrega e gerencia as categorias e subcategorias de produtos a partir de um arquivo JSON."""

    def __init__(self, categories_filename: str = "categories_promobell.json"):
        self.categories_filepath = self._find_categories_file(categories_filename)
        self.categories = self._load_categories_promobell()
        if not self.categories:
            logger.critical(
                f"Não foi possível carregar as categorias do arquivo: {self.categories_filepath}. A categorização pode falhar."
            )

    def _find_categories_file(self, filename: str) -> str:
        """Localiza o arquivo de categorias na estrutura esperada."""
        current_dir = os.path.dirname(os.path.abspath(__file__))
        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
        categories_path = os.path.join(base_dir, "src", "data", "categories", filename)

        # Debug adicional
        logger.info(f"Diretório atual: {current_dir}")
        logger.info(f"Diretório base: {base_dir}")
        logger.info(f"Caminho completo do arquivo: {categories_path}")
        logger.info(f"Arquivo existe? {os.path.exists(categories_path)}")

        return categories_path

    def _load_categories_promobell(self) -> Dict:
        """Carrega as categorias do arquivo JSON especificado."""
        if not os.path.exists(self.categories_filepath):
            logger.error(
                f"Arquivo de categorias não encontrado em: {self.categories_filepath}"
            )
            return {}
        try:
            with open(self.categories_filepath, "r", encoding="utf-8") as f:
                loaded_categories = json.load(f)
                logger.info(
                    f"Categorias carregadas com sucesso de {self.categories_filepath}. Total de categorias: {len(loaded_categories)}"
                )
                return loaded_categories
        except json.JSONDecodeError as e:
            logger.exception(
                f"Erro de decodificação JSON ao carregar categorias de {self.categories_filepath}: {e}"
            )
            return {}
        except IOError as e:
            logger.exception(
                f"Erro de I/O ao ler arquivo de categorias {self.categories_filepath}: {e}"
            )
            return {}
        except Exception as e:
            logger.exception(
                f"Erro inesperado ao carregar categorias de {self.categories_filepath}: {e}"
            )
            return {}

    def get_category_and_subcategory_indices(
        self, title: str
    ) -> Tuple[Optional[str], Optional[str]]:
        """
        Tenta encontrar a melhor correspondência de categoria e subcategoria para um título de produto.

        Args:
            title: O título do produto.

        Returns:
            Uma tupla contendo a chave da categoria (str) e o índice da subcategoria (str),
            ou (None, None) se nenhuma correspondência for encontrada.
        """
        if not title or not self.categories:
            return None, None

        title_lower = title.lower()
        best_match = {
            "cat_key": None,
            "sub_idx": None,
            "match_len": 0,
            "match_type": None,
        }

        # Prioriza correspondência de subcategoria
        for category_key, category_data in self.categories.items():
            subcategories = category_data.get("subcategories", [])
            for idx, subcategory in enumerate(subcategories):
                sub_lower = subcategory.lower()
                # Procura a subcategoria como uma palavra inteira ou frase no título
                if re.search(r"\b" + re.escape(sub_lower) + r"\b", title_lower):
                    if len(sub_lower) > best_match["match_len"]:
                        best_match = {
                            "cat_key": category_key,
                            "sub_idx": str(idx),
                            "match_len": len(sub_lower),
                            "match_type": "subcategory",
                        }

        # Se nenhuma subcategoria foi encontrada, tenta por nome da categoria
        if best_match["cat_key"] is None:
            for category_key, category_data in self.categories.items():
                category_name = category_data.get("name", "").lower()
                if category_name and re.search(
                    r"\b" + re.escape(category_name) + r"\b", title_lower
                ):
                    if len(category_name) > best_match["match_len"]:
                        best_match = {
                            "cat_key": category_key,
                            "sub_idx": None,  # Nenhuma subcategoria específica encontrada por nome de categoria
                            "match_len": len(category_name),
                            "match_type": "category",
                        }

        if best_match["cat_key"]:
            logger.debug(
                f"Título '{title}' mapeado para Categoria: {best_match['cat_key']}, Subcategoria Index: {best_match['sub_idx']} (Match: {best_match['match_type']} com tamanho {best_match['match_len']})"
            )
        else:
            logger.debug(
                f"Nenhuma categoria/subcategoria correspondente encontrada para o título: '{title}'"
            )

        return best_match["cat_key"], best_match["sub_idx"]

    def get_category_name(self, category_key: Optional[str]) -> Optional[str]:
        """Retorna o nome da categoria dado sua chave."""
        if category_key is None or category_key not in self.categories:
            return None
        return self.categories.get(category_key, {}).get("name")

    def get_subcategory_name(
        self, category_key: Optional[str], subcategory_index_str: Optional[str]
    ) -> Optional[str]:
        """Retorna o nome da subcategoria dado a chave da categoria e o índice da subcategoria."""
        if category_key is None or subcategory_index_str is None:
            return None

        try:
            subcategory_index = int(subcategory_index_str)
            category_data = self.categories.get(category_key, {})
            subcategories = category_data.get("subcategories", [])

            if 0 <= subcategory_index < len(subcategories):
                return subcategories[subcategory_index]
            else:
                logger.warning(
                    f"Índice de subcategoria '{subcategory_index}' inválido para categoria '{category_key}'."
                )
                return None
        except ValueError:
            logger.warning(
                f"Índice de subcategoria '{subcategory_index_str}' não é um número válido."
            )
            return None
        except Exception as e:
            logger.error(f"Erro ao obter nome da subcategoria: {e}")
            return None
