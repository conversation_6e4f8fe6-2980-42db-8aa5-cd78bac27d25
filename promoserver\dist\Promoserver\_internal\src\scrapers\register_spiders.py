"""
Script para registrar os spiders do Scrapy
"""

import os
import sys
import logging
import importlib
import inspect
import scrapy

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def register_spiders():
    """
    Registra os spiders do Scrapy
    """
    logger.info("Registrando spiders do Scrapy...")
    
    # Diretório dos spiders
    spiders_dir = os.path.join(os.path.dirname(__file__), "spiders")
    logger.info(f"Diretório dos spiders: {spiders_dir}")
    
    # Lista os arquivos no diretório
    spider_files = [f for f in os.listdir(spiders_dir) if f.endswith("_spider.py")]
    logger.info(f"Arquivos de spider encontrados: {spider_files}")
    
    # Registra os spiders
    spiders = []
    for spider_file in spider_files:
        # Remove a extensão .py
        module_name = spider_file[:-3]
        
        # Importa o módulo
        try:
            module_path = f"src.scrapers.spiders.{module_name}"
            logger.info(f"Importando módulo: {module_path}")
            module = importlib.import_module(module_path)
            
            # Encontra as classes de spider no módulo
            for name, obj in inspect.getmembers(module):
                if inspect.isclass(obj) and issubclass(obj, scrapy.Spider) and obj != scrapy.Spider:
                    logger.info(f"Spider encontrado: {name} ({obj.name})")
                    spiders.append(obj)
        except Exception as e:
            logger.error(f"Erro ao importar módulo {module_name}: {e}")
    
    logger.info(f"Total de spiders registrados: {len(spiders)}")
    return spiders

if __name__ == "__main__":
    # Exibe o diretório atual
    logger.info(f"Diretório atual: {os.getcwd()}")
    
    # Exibe o PYTHONPATH
    logger.info(f"PYTHONPATH: {sys.path}")
    
    # Registra os spiders
    spiders = register_spiders()
    
    if not spiders:
        logger.error("Nenhum spider registrado!")
        sys.exit(1)
    
    logger.info(f"Spiders registrados com sucesso: {[spider.name for spider in spiders]}")
