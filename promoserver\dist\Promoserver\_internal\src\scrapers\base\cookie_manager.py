import json
import logging
import os
import re
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)

class CookieManager:
    """Gerencia o armazenamento e carregamento de cookies para sessões de scraping."""

    def __init__(self, cookies_base_dir_name: str = "cookies"):
        # Garante que o caminho seja relativo ao diretório deste arquivo (base)
        script_dir = os.path.dirname(os.path.abspath(__file__))
        self.cookies_dir = os.path.join(script_dir, cookies_base_dir_name)
        try:
            os.makedirs(self.cookies_dir, exist_ok=True)
            logger.info(f"Diretório de cookies inicializado em: {self.cookies_dir}")
        except OSError as e:
            logger.error(f"Erro ao criar diretório de cookies {self.cookies_dir}: {e}")
            # Se não puder criar o diretório, as operações de salvar/carregar falharão graciosamente.

    def get_cookie_path(self, domain: str) -> str:
        """Retorna o caminho do arquivo de cookies para um domínio específico."""
        safe_domain = re.sub(r"[^\w\-\.]", "_", domain)
        return os.path.join(self.cookies_dir, f"{safe_domain}_cookies.json")

    def save_cookies(self, domain: str, cookies: List[Dict]) -> None:
        """Salva cookies para um domínio específico em formato JSON."""
        if not cookies:
            logger.debug(f"Nenhum cookie para salvar para {domain}.")
            return
        if not os.path.exists(self.cookies_dir):
            logger.warning(f"Diretório de cookies {self.cookies_dir} não existe. Não é possível salvar cookies.")
            return

        cookie_path = self.get_cookie_path(domain)
        try:
            with open(cookie_path, "w", encoding="utf-8") as f:
                json.dump(cookies, f, indent=4)
            logger.info(
                f"Cookies ({len(cookies)}) salvos para {domain} em {cookie_path}"
            )
        except IOError as e:
            logger.error(
                f"Erro de I/O ao salvar cookies para {domain} em {cookie_path}: {e}"
            )
        except TypeError as e:
            logger.error(f"Erro de tipo ao serializar cookies para {domain}: {e}")
        except Exception as e:
            logger.error(f"Erro inesperado ao salvar cookies para {domain}: {e}")

    def load_cookies(self, domain: str) -> List[Dict]:
        """Carrega cookies para um domínio específico a partir de um arquivo JSON."""
        if not os.path.exists(self.cookies_dir):
            logger.warning(f"Diretório de cookies {self.cookies_dir} não existe. Nenhum cookie carregado.")
            return []
            
        cookie_path = self.get_cookie_path(domain)
        if not os.path.exists(cookie_path):
            logger.info(
                f"Arquivo de cookies não encontrado para {domain} em {cookie_path}. Nenhum cookie carregado."
            )
            return []

        try:
            with open(cookie_path, "r", encoding="utf-8") as f:
                cookies = json.load(f)
                if isinstance(cookies, list) and all(
                    isinstance(c, dict) for c in cookies
                ):
                    logger.info(
                        f"Cookies ({len(cookies)}) carregados para {domain} de {cookie_path}"
                    )
                    return cookies
                else:
                    logger.warning(
                        f"Formato inválido encontrado no arquivo de cookies {cookie_path}. Ignorando."
                    )
                    return []
        except json.JSONDecodeError as e:
            logger.error(
                f"Erro ao decodificar JSON do arquivo de cookies {cookie_path}: {e}"
            )
            return []
        except IOError as e:
            logger.error(f"Erro de I/O ao carregar cookies de {cookie_path}: {e}")
            return []
        except Exception as e:
            logger.error(
                f"Erro inesperado ao carregar cookies de {cookie_path}: {e}"
            )
            return []

    def delete_cookies(self, domain: str) -> bool:
        """Exclui o arquivo de cookies para um domínio específico."""
        if not os.path.exists(self.cookies_dir):
            logger.warning(f"Diretório de cookies {self.cookies_dir} não existe. Não é possível excluir cookies.")
            return False

        cookie_path = self.get_cookie_path(domain)
        if os.path.exists(cookie_path):
            try:
                os.remove(cookie_path)
                logger.info(
                    f"Arquivo de cookies para {domain} excluído: {cookie_path}"
                )
                return True
            except OSError as e:
                logger.error(
                    f"Erro ao excluir arquivo de cookies {cookie_path}: {e}"
                )
                return False
        else:
            logger.info(
                f"Arquivo de cookies para {domain} não encontrado para exclusão."
            )
            return False
