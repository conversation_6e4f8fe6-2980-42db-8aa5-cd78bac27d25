"""
Módulo de modelos de resposta para a API do Promoserver.
Contém classes para padronizar as respostas da API.
"""

from typing import Any, Dict, Generic, List, Optional, TypeVar, Union
from pydantic import BaseModel, Field

T = TypeVar('T')

class ApiResponse(BaseModel, Generic[T]):
    """Modelo de resposta padrão da API."""
    
    status_code: int = Field(..., description="Código de status HTTP")
    success: bool = Field(..., description="Indica se a operação foi bem-sucedida")
    message: str = Field(..., description="Mensagem descritiva sobre o resultado da operação")
    data: Optional[T] = Field(None, description="Dados retornados pela operação")
    errors: Optional[List[str]] = Field(None, description="Lista de erros, se houver")
    
    @classmethod
    def success_response(cls, data: T, message: str = "Operação realizada com sucesso", status_code: int = 200) -> "ApiResponse[T]":
        """Cria uma resposta de sucesso."""
        return cls(
            status_code=status_code,
            success=True,
            message=message,
            data=data,
            errors=None
        )
    
    @classmethod
    def error_response(cls, message: str, errors: Optional[List[str]] = None, status_code: int = 400) -> "ApiResponse[None]":
        """Cria uma resposta de erro."""
        return cls(
            status_code=status_code,
            success=False,
            message=message,
            data=None,
            errors=errors or []
        )
