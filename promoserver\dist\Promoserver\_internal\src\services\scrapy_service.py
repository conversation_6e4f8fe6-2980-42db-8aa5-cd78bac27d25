"""
Serviço para integrar o Scrapy com o PromoHunter
"""

import logging
import asyncio
import os
import sys
import json
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from src.scrapers.base.ai_content_generator import AIContentGenerator
from src.scrapers.direct_scraper import DirectScraper

# Importa o gerenciador de logs
from src.utils.log_manager import get_log_manager

# Obtém o gerenciador de logs universal
log_manager = get_log_manager()

# Adiciona o diretório do Scrapy ao PYTHONPATH
scrapy_dir = os.path.join(
    os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
    "scrapers",
    "scrapy_implementation",
)
if scrapy_dir not in sys.path:
    sys.path.append(scrapy_dir)

# Adiciona também o diretório do projeto Scrapy
scrapy_project_dir = os.path.join(scrapy_dir, "promoscraper")
if scrapy_project_dir not in sys.path:
    sys.path.append(scrapy_project_dir)

# Importa o runner do Scrapy
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Variável para verificar se o Scrapy está disponível
scrape_mercadolivre = None

# Tenta importar o runner do Scrapy
try:
    from src.scrapers.scrapy_implementation.promoscraper.runner import (
        scrape_mercadolivre,
    )

    log_manager.info("Módulo runner do Scrapy importado com sucesso")
except ImportError as e:
    log_manager.error(f"Erro ao importar módulo runner do Scrapy: {e}")

logger = logging.getLogger(__name__)


class ScrapyService:
    """
    Serviço para integrar o Scrapy com o PromoHunter
    """

    def __init__(self):
        """Inicializa o serviço"""
        # Obtém o gerenciador de logs
        self.log_manager = log_manager

        # Variável para controlar processos em execução
        self.current_process = None
        self.is_running = False

        # Inicializa o scraper direto
        self.direct_scraper = DirectScraper(log_manager=log_manager)

        try:
            # Inicializa o ProductCategories
            from src.scrapers.base.product_categories import ProductCategories

            product_categories = ProductCategories()

            # Inicializa o AIContentGenerator com as categorias
            self.ai_generator = AIContentGenerator(product_categories)

            # Força a disponibilidade do modelo para testes
            self.ai_generator.available = True

            self.log_manager.info("AIContentGenerator inicializado com sucesso")
            logger.info("AIContentGenerator inicializado com sucesso")
        except Exception as e:
            self.log_manager.error(f"Erro ao inicializar AIContentGenerator: {e}")
            logger.error(f"Erro ao inicializar AIContentGenerator: {e}")
            self.ai_generator = None

        # Verifica se o Scrapy está disponível
        self.scrapy_available = scrape_mercadolivre is not None
        if not self.scrapy_available:
            self.log_manager.warning(
                "Scrapy não está disponível. O serviço usará o scraper direto."
            )
            logger.warning(
                "Scrapy não está disponível. O serviço usará o scraper direto."
            )
        else:
            self.log_manager.info("Scrapy está disponível e será usado para scraping")
            logger.info("Scrapy está disponível e será usado para scraping")

    async def scrape_url(self, url: str, max_pages: int = 2) -> List[Dict[str, Any]]:
        """
        Raspa produtos a partir de uma URL

        Args:
            url: URL para raspar
            max_pages: Número máximo de páginas a serem raspadas

        Returns:
            Lista de produtos
        """
        logger.info(f"Iniciando scraping da URL: {url}")
        self.log_manager.info(f"Iniciando scraping da URL: {url}")
        print(f"[INFO] promoserver: Iniciando scraping da URL: {url}")
        start_time = datetime.now()

        try:
            # Verifica se a URL é do Mercado Livre
            if "mercadolivre.com.br" not in url and "mercadolibre.com" not in url:
                logger.error(f"URL não suportada: {url}")
                return []

            # Se o Scrapy não estiver disponível, usa o scraper direto
            if not self.scrapy_available:
                logger.info("Usando o scraper direto para raspar a URL")
                self.log_manager.info("Usando o scraper direto para raspar a URL")
                products = self.direct_scraper.scrape_url(url, max_pages)
                # Enriquece os produtos com dados da IA
                enriched_products = await self._enrich_products(products)
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()
                logger.info(
                    f"Scraping direto concluído em {duration:.2f} segundos. Coletados {len(enriched_products)} produtos."
                )
                self.log_manager.info(
                    f"Scraping direto concluído em {duration:.2f} segundos. Coletados {len(enriched_products)} produtos."
                )
                print(
                    f"[INFO] promoserver: Scraping direto concluído em {duration:.2f} segundos. Coletados {len(enriched_products)} produtos."
                )
                return enriched_products

            # Usa o comando scrapy diretamente
            scrapy_dir = os.path.join(
                os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                "scrapers",
                "scrapy_implementation",
            )

            # Cria um nome de arquivo único baseado na URL
            import hashlib

            url_hash = hashlib.md5(url.encode()).hexdigest()[:8]
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"produtos_{url_hash}_{timestamp}.json"
            output_file = os.path.join(scrapy_dir, output_filename)

            # Executa o comando scrapy diretamente
            logger.info(f"Executando comando scrapy crawl mercadolivre para URL: {url}")
            self.log_manager.info(
                f"Executando comando scrapy crawl mercadolivre para URL: {url}"
            )
            print(
                f"[INFO] promoserver: Executando comando scrapy crawl mercadolivre para URL: {url}"
            )

            # Muda para o diretório do scrapy
            original_dir = os.getcwd()
            os.chdir(scrapy_dir)

            # Executa o comando diretamente usando o spider rápido
            cmd = f'scrapy crawl mercadolivre_fast -a url="{url}" -a max_pages={max_pages} -o {output_filename}'
            logger.info(f"Executando comando: {cmd}")
            self.log_manager.info(f"Executando comando: {cmd}")
            print(f"[INFO] promoserver: Executando comando: {cmd}")

            # Registra no log_manager para exibir na interface
            self.log_manager.info(f"Iniciando raspagem rápida da URL: {url}")

            # Executa o comando usando subprocess
            import subprocess
            import json

            self.current_process = subprocess.Popen(
                cmd,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=False,  # Alterado para False para evitar problemas de codificação
            )
            self.is_running = True

            # Monitora a saída em tempo real para mostrar progresso
            import threading

            def read_output():
                for line in iter(self.current_process.stdout.readline, b""):
                    try:
                        # Decodifica a linha com tratamento de erros
                        decoded_line = line.decode("utf-8", errors="replace").strip()
                        if decoded_line:
                            if (
                                "Encontrados" in decoded_line
                                and "produtos" in decoded_line
                            ):
                                self.log_manager.info(decoded_line)
                            elif "Processando produto" in decoded_line:
                                self.log_manager.debug(decoded_line)
                            elif (
                                "Página" in decoded_line and "concluída" in decoded_line
                            ):
                                self.log_manager.info(decoded_line)
                    except Exception as e:
                        self.log_manager.error(
                            f"Erro ao processar saída do processo: {e}"
                        )

            # Inicia thread para ler a saída em tempo real
            t = threading.Thread(target=read_output)
            t.daemon = True
            t.start()

            # Aguarda a conclusão do processo com timeout
            try:
                _, stderr = self.current_process.communicate(
                    timeout=300
                )  # Aumentado para 300 segundos (5 minutos)

                if self.current_process.returncode != 0:
                    try:
                        # Decodifica a saída de erro com tratamento de erros
                        stderr_text = (
                            stderr.decode("utf-8", errors="replace") if stderr else ""
                        )
                        logger.error(
                            f"Erro ao executar o comando scrapy: {stderr_text}"
                        )
                    except Exception as e:
                        logger.error(f"Erro ao decodificar saída de erro: {e}")

                    os.chdir(original_dir)  # Restaura o diretório original
                    self.is_running = False
                    self.current_process = None
                    return []
            except subprocess.TimeoutExpired:
                logger.warning(
                    "Timeout expirado ao executar o comando scrapy. Matando o processo..."
                )
                self.current_process.kill()
                _, stderr = self.current_process.communicate()
                logger.info(
                    "Processo terminado após timeout. Verificando se o arquivo de saída foi criado..."
                )
            finally:
                self.is_running = False
                self.current_process = None

            # Restaura o diretório original
            os.chdir(original_dir)

            # Verifica se o arquivo de saída existe
            import time

            # Aguarda um pouco para garantir que o arquivo seja salvo
            time.sleep(2)

            if not os.path.exists(output_file):
                logger.error(f"Arquivo de saída não encontrado: {output_file}")
                return []

            # Verifica se o arquivo está vazio
            if os.path.getsize(output_file) == 0:
                logger.error(f"Arquivo de saída está vazio: {output_file}")
                return []

            # Lê os resultados do arquivo
            try:
                with open(output_file, "r", encoding="utf-8") as f:
                    products = json.load(f)
            except Exception as e:
                logger.error(f"Erro ao ler o arquivo de resultados: {e}")
                products = []

            # Enriquece os produtos com dados da IA
            logger.info(
                f"Iniciando enriquecimento de {len(products)} produtos com categorias..."
            )
            enriched_products = await self._enrich_products(products)
            logger.info(
                f"Enriquecimento concluído. {len(enriched_products)} produtos processados."
            )

            # Verificar quantos produtos têm categorias e subcategorias
            with_category = sum(1 for p in enriched_products if p.get("category"))
            with_subcategory = sum(1 for p in enriched_products if p.get("subcategory"))
            logger.info(
                f"Produtos com categoria: {with_category}/{len(enriched_products)}"
            )
            logger.info(
                f"Produtos com subcategoria: {with_subcategory}/{len(enriched_products)}"
            )

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            logger.info(
                f"Scraping concluído em {duration:.2f} segundos. Coletados {len(enriched_products)} produtos."
            )

            return enriched_products
        except Exception as e:
            logger.error(f"Erro ao raspar URL {url}: {e}")
            return []

    async def scrape_category(
        self, store: str, category: str, max_pages: int = 2
    ) -> List[Dict[str, Any]]:
        """
        Raspa produtos de uma categoria

        Args:
            store: Loja (mercadolivre, magalu, amazon)
            category: ID da categoria
            max_pages: Número máximo de páginas a serem raspadas

        Returns:
            Lista de produtos
        """
        logger.info(f"Iniciando scraping da categoria {category} da loja {store}")
        start_time = datetime.now()

        try:
            # Verifica se a loja é suportada
            if store != "mercadolivre":
                logger.error(f"Loja não suportada: {store}")
                return []

            # Se o Scrapy não estiver disponível, usa o scraper direto
            if not self.scrapy_available:
                logger.info("Usando o scraper direto para raspar a categoria")
                self.log_manager.info("Usando o scraper direto para raspar a categoria")

                # Constrói a URL da categoria
                category_url = (
                    f"https://www.mercadolivre.com.br/ofertas?category={category}"
                )
                self.log_manager.info(f"URL da categoria: {category_url}")

                # Usa o scraper direto
                products = self.direct_scraper.scrape_url(category_url, max_pages)

                # Enriquece os produtos com dados da IA
                enriched_products = await self._enrich_products(products)

                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()
                logger.info(
                    f"Scraping direto concluído em {duration:.2f} segundos. Coletados {len(enriched_products)} produtos."
                )
                return enriched_products

            # Usa o comando scrapy diretamente
            scrapy_dir = os.path.join(
                os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                "scrapers",
                "scrapy_implementation",
            )

            # Cria um nome de arquivo único baseado na categoria
            import hashlib

            category_hash = hashlib.md5(f"{store}_{category}".encode()).hexdigest()[:8]
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"produtos_categoria_{category_hash}_{timestamp}.json"
            output_file = os.path.join(scrapy_dir, output_filename)

            # Executa o comando scrapy diretamente
            logger.info(
                f"Executando comando scrapy crawl mercadolivre para categoria: {category}"
            )

            # Muda para o diretório do scrapy
            original_dir = os.getcwd()
            os.chdir(scrapy_dir)

            # Executa o comando diretamente usando o spider rápido
            cmd = f'scrapy crawl mercadolivre_fast -a category="{category}" -a max_pages={max_pages} -o {output_filename}'
            logger.info(f"Executando comando: {cmd}")

            # Registra no log_manager para exibir na interface
            self.log_manager.info(f"Iniciando raspagem rápida da categoria: {category}")

            # Executa o comando usando subprocess
            import subprocess
            import json

            self.current_process = subprocess.Popen(
                cmd,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=False,  # Alterado para False para evitar problemas de codificação
            )
            self.is_running = True

            # Monitora a saída em tempo real para mostrar progresso
            import threading

            def read_output():
                for line in iter(self.current_process.stdout.readline, b""):
                    try:
                        # Decodifica a linha com tratamento de erros
                        decoded_line = line.decode("utf-8", errors="replace").strip()
                        if decoded_line:
                            if (
                                "Encontrados" in decoded_line
                                and "produtos" in decoded_line
                            ):
                                self.log_manager.info(decoded_line)
                            elif "Processando produto" in decoded_line:
                                self.log_manager.debug(decoded_line)
                            elif (
                                "Página" in decoded_line and "concluída" in decoded_line
                            ):
                                self.log_manager.info(decoded_line)
                    except Exception as e:
                        self.log_manager.error(
                            f"Erro ao processar saída do processo: {e}"
                        )

            # Inicia thread para ler a saída em tempo real
            t = threading.Thread(target=read_output)
            t.daemon = True
            t.start()

            # Aguarda a conclusão do processo com timeout
            try:
                _, stderr = self.current_process.communicate(
                    timeout=300
                )  # Aumentado para 300 segundos (5 minutos)

                if self.current_process.returncode != 0:
                    try:
                        # Decodifica a saída de erro com tratamento de erros
                        stderr_text = (
                            stderr.decode("utf-8", errors="replace") if stderr else ""
                        )
                        logger.error(
                            f"Erro ao executar o comando scrapy: {stderr_text}"
                        )
                    except Exception as e:
                        logger.error(f"Erro ao decodificar saída de erro: {e}")

                    os.chdir(original_dir)  # Restaura o diretório original
                    self.is_running = False
                    self.current_process = None
                    return []
            except subprocess.TimeoutExpired:
                logger.warning(
                    "Timeout expirado ao executar o comando scrapy. Matando o processo..."
                )
                self.current_process.kill()
                _, stderr = self.current_process.communicate()
                logger.info(
                    "Processo terminado após timeout. Verificando se o arquivo de saída foi criado..."
                )
            finally:
                self.is_running = False
                self.current_process = None

            # Restaura o diretório original
            os.chdir(original_dir)

            # Verifica se o arquivo de saída existe
            import time

            # Aguarda um pouco para garantir que o arquivo seja salvo
            time.sleep(2)

            if not os.path.exists(output_file):
                logger.error(f"Arquivo de saída não encontrado: {output_file}")
                return []

            # Verifica se o arquivo está vazio
            if os.path.getsize(output_file) == 0:
                logger.error(f"Arquivo de saída está vazio: {output_file}")
                return []

            # Lê os resultados do arquivo
            try:
                with open(output_file, "r", encoding="utf-8") as f:
                    products = json.load(f)
            except Exception as e:
                logger.error(f"Erro ao ler o arquivo de resultados: {e}")
                products = []

            # Enriquece os produtos com dados da IA
            enriched_products = await self._enrich_products(products)

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            logger.info(
                f"Scraping concluído em {duration:.2f} segundos. Coletados {len(enriched_products)} produtos."
            )

            return enriched_products
        except Exception as e:
            logger.error(f"Erro ao raspar categoria {category} da loja {store}: {e}")
            return []

    def stop_scraper(self) -> Dict[str, Any]:
        """
        Para a execução do scraper.

        Returns:
            Dict[str, Any]: Status após parar o scraper
        """
        if not self.is_running or self.current_process is None:
            logger.info("Não há processo de scraping em execução para parar")
            self.log_manager.info("Não há processo de scraping em execução para parar")
            return {
                "success": False,
                "message": "Não há processo de scraping em execução",
            }

        try:
            # Tenta matar o processo
            logger.warning("Parando processo de scraping...")
            self.log_manager.warning("Parando processo de scraping...")

            # Mata o processo
            self.current_process.kill()

            # Aguarda a finalização
            self.current_process.wait(timeout=5)

            # Limpa as variáveis
            self.is_running = False
            self.current_process = None

            logger.info("Processo de scraping parado com sucesso")
            self.log_manager.info("Processo de scraping parado com sucesso")

            return {
                "success": True,
                "message": "Processo de scraping parado com sucesso",
            }
        except Exception as e:
            logger.error(f"Erro ao parar o processo de scraping: {e}")
            self.log_manager.error(f"Erro ao parar o processo de scraping: {e}")

            # Força a limpeza das variáveis mesmo em caso de erro
            self.is_running = False
            self.current_process = None

            return {
                "success": False,
                "message": f"Erro ao parar o processo de scraping: {e}",
            }

    async def enrich_products(
        self, products: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Enriquece os produtos com dados da IA (wrapper público para _enrich_products)

        Args:
            products: Lista de produtos

        Returns:
            Lista de produtos enriquecidos
        """
        self.log_manager.info(
            f"Enriquecendo {len(products)} produtos com categorias e subcategorias usando IA"
        )
        return await self._enrich_products(products)

    async def _enrich_products(
        self, products: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Enriquece os produtos com dados da IA

        Args:
            products: Lista de produtos

        Returns:
            Lista de produtos enriquecidos
        """
        enriched_products = []
        total_products = len(products)
        logger.info(f"Enriquecendo {total_products} produtos com dados da IA")

        for idx, product in enumerate(products, 1):
            try:
                # Garantir que os campos necessários existam
                if "url" in product and not product.get("url_produto"):
                    product["url_produto"] = product["url"]

                # Garantir que url_afiliado esteja em branco conforme solicitado
                product["url_afiliado"] = ""

                # Garantir que a descrição esteja em branco conforme solicitado
                product["description"] = ""
                product["descricao"] = ""

                # Mapear campos para o formato esperado pelo PromoHunter
                if "title" in product and not product.get("titulo"):
                    product["titulo"] = product["title"]

                if "image_url" in product and not product.get("url_imagem"):
                    product["url_imagem"] = product["image_url"]

                if "price" in product and not product.get("preco_atual"):
                    product["preco_atual"] = product["price"]

                if "old_price" in product and not product.get("preco_antigo"):
                    product["preco_antigo"] = product["old_price"]

                # Adicionar campos padrão
                if "preco_alternativo" not in product:
                    product["preco_alternativo"] = 0.0

                if "ativo" not in product:
                    product["ativo"] = True

                if "cupom" not in product and "coupon_code" in product:
                    product["cupom"] = product["coupon_code"]
                elif "cupom" not in product:
                    product["cupom"] = ""

                if "menor_preco" not in product:
                    product["menor_preco"] = False

                if "indicamos" not in product:
                    product["indicamos"] = False

                if "disparar_whatsapp" not in product:
                    product["disparar_whatsapp"] = False

                if "grupo_whatsapp" not in product:
                    product["grupo_whatsapp"] = ""

                if "frete" not in product:
                    product["frete"] = False

                if "invalidProduct" not in product:
                    product["invalidProduct"] = False

                if "isStory" not in product:
                    product["isStory"] = False

                if "plataforma" not in product:
                    product["plataforma"] = "Mercadolivre"

                # Gera categoria e subcategoria usando IA (se disponível)
                # Verifica se o produto deve usar categorização por IA
                use_ai = product.get("_use_ai_categorization", True)
                if (
                    use_ai
                    and "title" in product
                    and not product.get("category_id")
                    and not product.get("categoria")
                ):
                    logger.info(
                        f"Gerando categoria para produto {idx}/{total_products}: {product.get('title', '')[:50]}..."
                    )

                    # Verifica se o AI Generator está disponível
                    # self.log_manager.info(f"Verificando disponibilidade do AI Generator para produto {idx}/{total_products}") # Removido log duplicado
                    logger.info(
                        f"Verificando disponibilidade do AI Generator para produto {idx}/{total_products}"
                    )

                    # Inicializa o AI Generator se não existir
                    if not hasattr(self, "ai_generator") or not self.ai_generator:
                        try:
                            from src.scrapers.base.ai_content_generator import (
                                AIContentGenerator,
                            )
                            from src.scrapers.base.product_categories import (
                                ProductCategories,
                            )

                            # Inicializa o ProductCategories
                            product_categories = ProductCategories()

                            # Inicializa o AIContentGenerator com as categorias
                            self.ai_generator = AIContentGenerator(product_categories)

                            # Força a disponibilidade do modelo para testes
                            self.ai_generator.available = True

                            self.log_manager.info(
                                "AI Generator inicializado com sucesso"
                            )  # Removido log duplicado
                            logger.info("AI Generator inicializado com sucesso")
                        except Exception as e:
                            # self.log_manager.error(f"Erro ao inicializar AI Generator: {e}") # Removido log duplicado
                            logger.error(f"Erro ao inicializar AI Generator: {e}")

                    # Tenta gerar categorias com o AI Generator
                    if hasattr(self, "ai_generator") and self.ai_generator:
                        logger.info(
                            f"AI Generator disponível. Gerando categoria para produto {idx}/{total_products}"
                        )
                        try:
                            # Força a disponibilidade do modelo para testes
                            self.ai_generator.available = True

                            # Verifica se o produto tem título
                            if not product.get("title") and product.get("titulo"):
                                product["title"] = product["titulo"]

                            # Garante que o título existe
                            if not product.get("title"):
                                logger.warning(
                                    f"Produto {idx} não tem título. Pulando categorização."
                                )
                                product["category"] = "Outros"
                                product["categoria"] = "Outros"
                                product["subcategory"] = "Geral"
                                product["subcategoria"] = "Geral"
                                continue

                            # Gera categoria
                            # self.log_manager.info(f"Gerando categoria para: {product['title'][:50]}...") # Removido log duplicado
                            logger.info(
                                f"Gerando categoria para: {product['title'][:50]}..."
                            )

                            # Chama o método generate_product_category com o título do produto
                            try:
                                category_id = (
                                    await self.ai_generator.generate_product_category(
                                        product["title"]
                                    )
                                )
                                # self.log_manager.info(f"Categoria gerada com sucesso: {category_id}") # Removido log duplicado
                                logger.info(
                                    f"Categoria gerada com sucesso: {category_id}"
                                )
                            except Exception as e:
                                # self.log_manager.error(f"Erro ao gerar categoria: {e}") # Removido log duplicado
                                logger.error(f"Erro ao gerar categoria: {e}")
                                category_id = None

                            if category_id:
                                product["category_id"] = category_id

                                # Obtém o nome da categoria
                                category_name = self.ai_generator.product_categories.get_category_name(
                                    category_id
                                )
                                if category_name:
                                    product["category"] = category_name
                                    product["categoria"] = category_name
                                    logger.info(f"Nome da categoria: {category_name}")

                                # Gera subcategoria
                                # self.log_manager.info(f"Gerando subcategoria para: {product['title'][:50]}...") # Removido log duplicado
                                logger.info(
                                    f"Gerando subcategoria para: {product['title'][:50]}..."
                                )

                                # Chama o método generate_product_subcategory com o título e categoria_id
                                try:
                                    subcategory_id = await self.ai_generator.generate_product_subcategory(
                                        product["title"], category_id
                                    )
                                    # self.log_manager.info(f"Subcategoria gerada com sucesso: {subcategory_id}") # Removido log duplicado
                                    logger.info(
                                        f"Subcategoria gerada com sucesso: {subcategory_id}"
                                    )
                                except Exception as e:
                                    # self.log_manager.error(f"Erro ao gerar subcategoria: {e}") # Removido log duplicado
                                    logger.error(f"Erro ao gerar subcategoria: {e}")
                                    subcategory_id = None

                                if subcategory_id:
                                    product["subcategory_id"] = subcategory_id

                                    # Obtém o nome da subcategoria
                                    subcategory_name = self.ai_generator.product_categories.get_subcategory_name(
                                        category_id, subcategory_id
                                    )
                                    if subcategory_name:
                                        product["subcategory"] = subcategory_name
                                        product["subcategoria"] = subcategory_name
                                        logger.info(
                                            f"Nome da subcategoria: {subcategory_name}"
                                        )
                            else:
                                # Se não conseguiu gerar categoria, tenta usar o método alternativo
                                logger.warning(
                                    "Tentando método alternativo para categorização..."
                                )
                                try:
                                    # Usar o método get_category_and_subcategory_indices
                                    cat_key, sub_idx = (
                                        self.ai_generator.product_categories.get_category_and_subcategory_indices(
                                            product["title"]
                                        )
                                    )
                                    logger.info(
                                        f"Método alternativo: cat_key={cat_key}, sub_idx={sub_idx}"
                                    )

                                    if cat_key:
                                        product["category_id"] = cat_key
                                        category_name = self.ai_generator.product_categories.get_category_name(
                                            cat_key
                                        )
                                        if category_name:
                                            product["category"] = category_name
                                            product["categoria"] = category_name
                                            logger.info(
                                                f"Nome da categoria (alternativo): {category_name}"
                                            )

                                        if sub_idx:
                                            product["subcategory_id"] = sub_idx
                                            subcategory_name = self.ai_generator.product_categories.get_subcategory_name(
                                                cat_key, sub_idx
                                            )
                                            if subcategory_name:
                                                product["subcategory"] = (
                                                    subcategory_name
                                                )
                                                product["subcategoria"] = (
                                                    subcategory_name
                                                )
                                                logger.info(
                                                    f"Nome da subcategoria (alternativo): {subcategory_name}"
                                                )
                                        else:
                                            # Se não encontrou subcategoria, usa valor padrão
                                            product["subcategory"] = "Geral"
                                            product["subcategoria"] = "Geral"
                                    else:
                                        # Se não conseguiu gerar categoria, usa valores padrão
                                        logger.warning(
                                            "Método alternativo falhou. Usando valores padrão."
                                        )
                                        product["category"] = "Outros"
                                        product["categoria"] = "Outros"
                                        product["subcategory"] = "Geral"
                                        product["subcategoria"] = "Geral"
                                except Exception as e:
                                    logger.error(
                                        f"Erro no método alternativo de categorização: {e}"
                                    )
                                    # Se ocorreu erro, usa valores padrão
                                    product["category"] = "Outros"
                                    product["categoria"] = "Outros"
                                    product["subcategory"] = "Geral"
                                    product["subcategoria"] = "Geral"
                        except Exception as e:
                            logger.warning(f"Erro ao gerar categoria com IA: {e}")
                            # Define valores padrão para categoria e subcategoria
                            product["category"] = "Outros"
                            product["categoria"] = "Outros"
                            product["subcategory"] = "Geral"
                            product["subcategoria"] = "Geral"
                    else:
                        logger.warning(
                            "AI Generator não disponível. Usando categorias padrão."
                        )
                        # Define valores padrão para categoria e subcategoria
                        product["category"] = "Outros"
                        product["categoria"] = "Outros"
                        product["subcategory"] = "Geral"
                        product["subcategoria"] = "Geral"

                enriched_products.append(product)
                # self.log_manager.info(f"Produto {idx}/{total_products} enriquecido com sucesso") # Removido log duplicado
                logger.info(f"Produto {idx}/{total_products} enriquecido com sucesso")
            except Exception as e:
                # self.log_manager.error(f"Erro ao enriquecer produto {idx}: {e}") # Removido log duplicado
                logger.error(f"Erro ao enriquecer produto {idx}: {e}")
                # Adiciona o produto mesmo com erro para não perder dados
                enriched_products.append(product)

        logger.info(
            f"Enriquecimento concluído. {len(enriched_products)} produtos processados."
        )
        return enriched_products
