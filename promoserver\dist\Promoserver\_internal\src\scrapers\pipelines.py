"""
Pipelines do Scrapy para o PromoScraper
"""

import logging
import re
from typing import Dict, Any, Optional
from urllib.parse import urlparse, parse_qs, urlencode, urlunparse

logger = logging.getLogger(__name__)


class ProductCleaningPipeline:
    """
    Pipeline para limpar e normalizar dados dos produtos
    """

    def process_item(self, item, spider):
        """Processa e limpa os dados do item"""
        # Limpar URL do produto
        if 'url_produto' in item:
            item['url_produto'] = self.clean_product_url(item['url_produto'])

        # Garantir que a URL afiliada fique em branco conforme solicitado
        if 'url_afiliado' in item:
            item['url_afiliado'] = ""

        # Limpar URL da imagem
        if 'image_url' in item:
            item['image_url'] = self.fix_image_url(item['image_url'])

        # Limpar e normalizar preços
        if 'price' in item:
            item['price'] = self.clean_price(item['price'])

        if 'old_price' in item:
            item['old_price'] = self.clean_price(item['old_price'])

        # Limpar título
        if 'title' in item:
            item['title'] = self.clean_title(item['title'])

        # Garantir que a descrição fique em branco conforme solicitado
        if 'description' in item:
            item['description'] = ""

        return item

    def clean_product_url(self, url: Optional[str]) -> str:
        """
        Limpa a URL do produto removendo parâmetros de rastreamento
        """
        if not url:
            return ""

        try:
            # Parse da URL
            parsed_url = urlparse(url)
            
            # Remove parâmetros de rastreamento comuns
            query_params = parse_qs(parsed_url.query)
            params_to_remove = [
                'utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content',
                'gclid', 'fbclid', 'msclkid', 'ref', 'source', 'campaign', 'tracking'
            ]
            
            # Remove os parâmetros indesejados
            for param in params_to_remove:
                if param in query_params:
                    del query_params[param]
            
            # Reconstrói a query string
            clean_query = urlencode(query_params, doseq=True) if query_params else ''
            
            # Reconstrói a URL limpa
            clean_url = urlunparse((
                parsed_url.scheme,
                parsed_url.netloc,
                parsed_url.path,
                parsed_url.params,
                clean_query,
                ''  # Remove o fragment
            ))
            
            return clean_url
        except Exception as e:
            logger.error(f"Erro ao limpar URL: {e}")
            return url

    def fix_image_url(self, url: Optional[str]) -> str:
        """
        Corrige a URL da imagem para obter a versão de maior qualidade
        """
        if not url:
            return ""

        # Verifica se é um placeholder ou data URI
        if url.startswith('data:'):
            logger.warning(f"Detectada imagem data URI: {url[:50]}...")
            return ""

        # Verifica se é uma URL do Mercado Livre
        if "mlstatic.com" in url:
            try:
                # Tenta melhorar a qualidade da imagem
                # Adiciona 2X_ para obter versão de maior qualidade
                if "2X_" not in url:
                    parts = url.split('D_NQ_NP_')
                    if len(parts) == 2:
                        high_quality_url = f"{parts[0]}D_NQ_NP_2X_{parts[1]}"
                        logger.info(f"URL de imagem melhorada para 2X: {high_quality_url}")
                        return high_quality_url
            except Exception as e:
                logger.error(f"Erro ao processar URL de imagem do Mercado Livre: {e}")
                return url

        # Verifica se é uma URL do Magalu
        if "mlcdn.com.br" in url:
            try:
                # Tenta obter a versão de maior qualidade
                if "1000x" not in url and "800x" not in url:
                    # Substitui qualquer dimensão por 1000x
                    high_quality_url = re.sub(r'/\d+x\d+/', '/1000x1000/', url)
                    if high_quality_url != url:
                        logger.info(f"URL de imagem do Magalu melhorada: {high_quality_url}")
                        return high_quality_url
            except Exception as e:
                logger.error(f"Erro ao processar URL de imagem do Magalu: {e}")
                return url

        # Verifica se é uma URL da Amazon
        if "amazon.com" in url or "amazon.com.br" in url:
            try:
                # Tenta obter a versão de maior qualidade
                if "_SL" in url:
                    # Substitui _SLxxx por _SL1000
                    high_quality_url = re.sub(r'_SL\d+_', '_SL1000_', url)
                    if high_quality_url != url:
                        logger.info(f"URL de imagem da Amazon melhorada: {high_quality_url}")
                        return high_quality_url
            except Exception as e:
                logger.error(f"Erro ao processar URL de imagem da Amazon: {e}")
                return url

        return url

    def clean_price(self, price: Optional[str]) -> str:
        """
        Limpa e normaliza o preço
        """
        if not price:
            return ""

        try:
            # Remove caracteres não numéricos, exceto vírgula e ponto
            price = re.sub(r'[^\d,.]', '', price)
            
            # Garante que o formato seja consistente
            if price:
                # Se tiver vírgula, mantém como está (formato brasileiro)
                if ',' in price:
                    return price
                # Se tiver apenas ponto, converte para formato brasileiro
                elif '.' in price:
                    parts = price.split('.')
                    if len(parts) == 2:
                        # Se a parte decimal tiver 2 dígitos, assume que é centavos
                        if len(parts[1]) == 2:
                            return price.replace('.', ',')
            
            return price
        except Exception as e:
            logger.error(f"Erro ao limpar preço: {e}")
            return price

    def clean_title(self, title: Optional[str]) -> str:
        """
        Limpa e normaliza o título
        """
        if not title:
            return ""

        try:
            # Remove espaços extras
            title = re.sub(r'\s+', ' ', title).strip()
            
            # Remove caracteres especiais indesejados
            title = re.sub(r'[^\w\s\-.,;:()[\]{}!?@#$%&*+=]', '', title)
            
            return title
        except Exception as e:
            logger.error(f"Erro ao limpar título: {e}")
            return title


class ImageProcessingPipeline:
    """
    Pipeline para processar imagens de produtos
    """

    def process_item(self, item, spider):
        """Processa a imagem do item"""
        if 'image_url' in item and item['image_url']:
            # Verifica se a imagem é um placeholder ou ícone
            if self._is_placeholder_image(item['image_url']):
                logger.warning(f"Imagem detectada como placeholder: {item['image_url']}")
                item['image_url'] = ""

        return item

    def _is_placeholder_image(self, url: str) -> bool:
        """
        Verifica se a URL da imagem é um placeholder
        """
        placeholder_indicators = [
            'placeholder', 'no-image', 'noimage', 'default', 'missing',
            'sem-foto', 'semfoto', 'no-photo', 'nophoto'
        ]
        
        url_lower = url.lower()
        for indicator in placeholder_indicators:
            if indicator in url_lower:
                return True
                
        # Verifica se é uma imagem muito pequena (ícone)
        small_image_patterns = [
            r'_\d+x\d+\.', r'/\d+x\d+/', r'_icon\.', r'_thumb\.'
        ]
        
        for pattern in small_image_patterns:
            if re.search(pattern, url_lower):
                size_match = re.search(r'(\d+)x(\d+)', url_lower)
                if size_match:
                    width, height = int(size_match.group(1)), int(size_match.group(2))
                    if width < 100 or height < 100:
                        return True
                        
        return False
