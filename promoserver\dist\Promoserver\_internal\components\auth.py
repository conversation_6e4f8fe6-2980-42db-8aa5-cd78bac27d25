"""
Módulo de autenticação para a API do Promoserver.
Contém funções, dependências e endpoints relacionados à autenticação.
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

# Assuming SupabaseService is correctly importable or handled by DI
# If SupabaseService needs specific initialization, adjust as needed.
from src.services.supabase_service import SupabaseService
from components.exceptions import handle_exception
from components.models import LoginInput  # Import the LoginInput model

log = logging.getLogger("api.auth")

# Roteador para endpoints de autenticação
router = APIRouter(prefix="/auth", tags=["authentication"])

# Instância do HTTPBearer para autenticação
bearer_security = HTTPBearer()

# Global variable to hold the Supabase service instance
# This will be set by the initialize function
supabase_service_instance: SupabaseService = None

def initialize(s_service: SupabaseService):
    """Inicializa o módulo com a instância do SupabaseService."""
    global supabase_service_instance
    supabase_service_instance = s_service
    log.info("Serviço Supabase inicializado para o componente de autenticação.")

# Dependency to get the initialized SupabaseService instance
# This avoids the problematic 'from api_main import supabase_service'
def get_supabase_service() -> SupabaseService:
    """Dependency function to get the initialized SupabaseService."""
    if supabase_service_instance is None:
        # This should ideally not happen if initialize() is called correctly at startup
        log.error("FATAL: SupabaseService não foi inicializado no componente auth.")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Serviço de autenticação não está disponível.",
        )
    return supabase_service_instance

async def verify_supabase_session(
    credentials: HTTPAuthorizationCredentials = Depends(bearer_security),
    db_service: SupabaseService = Depends(get_supabase_service), # Use the dependency
) -> dict:
    """
    Dependência FastAPI que valida o token Bearer chamando o SupabaseService,
    que por sua vez contata o servidor Supabase.

    Args:
        credentials: Credenciais de autenticação (obtidas automaticamente pelo FastAPI).
        db_service: Instância do SupabaseService (injetada pela dependência get_supabase_service).

    Returns:
        dict: Dados do usuário autenticado.

    Raises:
        HTTPException: Se a autenticação falhar ou ocorrer erro interno.
    """
    token = credentials.credentials
    log.debug("Tentando validação do token via serviço Supabase")
    try:
        user_data = await db_service.validate_token_with_supabase(token)
        # Reduzido para log.debug para evitar excesso de logs
        log.debug(
            f"Token validado com sucesso via Supabase para usuário: {user_data.get('email')}"
        )
        return user_data
    except HTTPException as e:
        # Re-raise known HTTPExceptions (like 401 Unauthorized from SupabaseService)
        log.warning(
            f"Falha na validação do token: {e.detail} (Status: {e.status_code})"
        )
        raise e
    except Exception as e:
        # Handle unexpected errors during validation
        log.exception(f"Erro inesperado durante verificação da sessão Supabase: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Ocorreu um erro interno durante a verificação da autenticação.",
        )

# --- Endpoints ---

@router.post("/login")
async def login_endpoint(
    login_data: LoginInput,
    db_service: SupabaseService = Depends(get_supabase_service) # Inject service
):
    """
    Autentica o usuário via Supabase e retorna um JWT.

    Args:
        login_data: Dados de login (email e senha) do corpo da requisição.
        db_service: Instância injetada do SupabaseService.

    Returns:
        dict: Resultado da autenticação (e.g., contendo o token JWT).

    Raises:
        HTTPException: Se a autenticação falhar ou ocorrer erro interno.
    """
    log.info(f"Tentativa de login para email: {login_data.email}")
    try:
        result = await db_service.login_user(
            email=login_data.email, password=login_data.password
        )
        log.info(f"Login realizado com sucesso para email: {login_data.email}")
        return result
    except HTTPException as e:
        log.error(f"Falha ao fazer login para {login_data.email}: Status {e.status_code}, Detalhes: {e.detail}")
        log.exception(e)
        raise e
    except Exception as e:
        log.error(f"Erro inesperado durante login para {login_data.email}: {e}")
        log.exception(e)
        raise handle_exception(e, "Erro interno do servidor durante o login")


@router.get("/me", dependencies=[Depends(verify_supabase_session)])
async def read_users_me(user_info: dict = Depends(verify_supabase_session)):
    """
    Retorna informações do usuário autenticado.
    A autenticação é garantida pela dependência verify_supabase_session.

    Args:
        user_info: Dados do usuário validados, injetados pela dependência.

    Returns:
        dict: Informações selecionadas do usuário (email, ID, role).
    """
    log.info(f"Acessando /auth/me para usuário: {user_info.get('email')}")
    # user_info contains what was returned by verify_supabase_session
    return {
        "email": user_info.get("email"),
        "user_id": user_info.get("user_id"),
        "role": user_info.get("role"),
        # Add any other relevant user info you want to expose
    }

# Note: Removed the old standalone login_user function as its logic is now in login_endpoint
