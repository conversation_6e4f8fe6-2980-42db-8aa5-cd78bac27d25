"""
Módulo de endpoints do PromoHunter para a API do Promoserver.
Contém funções relacionadas aos endpoints de scraping de ofertas.
"""

import logging
from typing import Dict, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query

from components.auth import verify_supabase_session
from components.models import OfferCategoryResponse, OfferScraperResponse, SelectorData, SelectorOutput
from components.exceptions import handle_exception

log = logging.getLogger("api.promohunter")

# Roteador para endpoints do PromoHunter
router = APIRouter(prefix="/promohunter", tags=["promohunter"])

# Referência ao PromoHunterService (será definida na inicialização)
promohunter_service = None


def initialize(ph_service):
    """Inicializa o módulo com a instância do PromoHunterService."""
    global promohunter_service
    promohunter_service = ph_service

    # Registrar o logger no gerenciador de logs
    from src.utils.log_manager import get_log_manager
    log_manager = get_log_manager()
    log_manager.register_logger("api.promohunter")


@router.get(
    "/categories",
    response_model=List[OfferCategoryResponse],
    dependencies=[Depends(verify_supabase_session)],
)
async def get_offer_categories(
    store: Optional[str] = Query(None, description="Filtrar por loja (mercadolivre, magalu, amazon)"),
    user_info: dict = Depends(verify_supabase_session)
):
    """
    Obtém as categorias de ofertas disponíveis.

    Args:
        store: Filtro opcional por loja
        user_info: Informações do usuário autenticado

    Returns:
        List[OfferCategoryResponse]: Lista de categorias de ofertas
    """
    log.info(f"Usuário {user_info.get('email')} requisitou categorias de ofertas")
    if not promohunter_service:
        raise handle_exception(Exception("Serviço de PromoHunter não disponível"))
    try:
        # Verificar se o método get_categories é assíncrono ou não
        # Se store for None, precisamos obter categorias para cada loja e combiná-las
        if store is None:
            # Obter categorias para cada loja suportada
            all_categories = []
            for current_store in ['mercadolivre', 'magalu', 'amazon']:
                try:
                    # Sempre passar o parâmetro store para o método get_categories
                    store_categories = await promohunter_service.get_categories(store=current_store)
                    # Garantir que cada categoria tenha o campo 'store'
                    for category in store_categories:
                        if 'store' not in category:
                            category['store'] = current_store
                    all_categories.extend(store_categories)
                except Exception as e:
                    log.warning(f"Erro ao obter categorias para {current_store}: {e}")
            return all_categories
        else:
            # Obter categorias para a loja específica
            try:
                # Sempre passar o parâmetro store para o método get_categories
                categories = await promohunter_service.get_categories(store=store)
                # Garantir que cada categoria tenha o campo 'store'
                for category in categories:
                    if 'store' not in category:
                        category['store'] = store
                return categories
            except Exception as e:
                log.warning(f"Erro ao obter categorias para {store}: {e}")
                return []
    except Exception as e:
        log.error(f"Erro ao obter categorias de ofertas: {e}")
        raise handle_exception(e)


@router.post(
    "/categories",
    response_model=OfferCategoryResponse,
    dependencies=[Depends(verify_supabase_session)],
)
async def create_offer_category(
    category: OfferCategoryResponse,
    user_info: dict = Depends(verify_supabase_session)
):
    """
    Cria uma nova categoria de ofertas.

    Args:
        category: Dados da categoria
        user_info: Informações do usuário autenticado

    Returns:
        OfferCategoryResponse: Categoria criada
    """
    log.info(f"Usuário {user_info.get('email')} está criando uma nova categoria de ofertas")
    if not promohunter_service:
        raise handle_exception(Exception("Serviço de PromoHunter não disponível"))
    try:
        # Converter o modelo Pydantic para um dicionário
        try:
            # Pydantic v2
            category_dict = category.model_dump()
        except AttributeError:
            # Pydantic v1
            category_dict = category.dict()

        # Garantir que o campo store esteja presente
        if 'store' not in category_dict or not category_dict['store']:
            category_dict['store'] = 'mercadolivre'  # Valor padrão
            log.info(f"Campo 'store' não fornecido, usando valor padrão: {category_dict['store']}")

        created_category = await promohunter_service.create_category(category_dict)

        # Garantir que o campo store esteja presente no resultado
        if 'store' not in created_category:
            created_category['store'] = category_dict['store']

        # Converter o dicionário de volta para o modelo Pydantic
        return OfferCategoryResponse(**created_category)
    except Exception as e:
        log.error(f"Erro ao criar categoria de ofertas: {e}")
        raise handle_exception(e)


@router.put(
    "/categories/{category_id}",
    response_model=OfferCategoryResponse,
    dependencies=[Depends(verify_supabase_session)],
)
async def update_offer_category(
    category_id: int,
    category: OfferCategoryResponse,
    user_info: dict = Depends(verify_supabase_session)
):
    """
    Atualiza uma categoria de ofertas existente.

    Args:
        category_id: ID da categoria
        category: Novos dados da categoria
        user_info: Informações do usuário autenticado

    Returns:
        OfferCategoryResponse: Categoria atualizada
    """
    log.info(f"Usuário {user_info.get('email')} está atualizando a categoria de ofertas {category_id}")
    if not promohunter_service:
        raise handle_exception(Exception("Serviço de PromoHunter não disponível"))
    try:
        # Converter o modelo Pydantic para um dicionário
        try:
            # Pydantic v2
            category_dict = category.model_dump()
        except AttributeError:
            # Pydantic v1
            category_dict = category.dict()

        # Garantir que o campo store esteja presente
        if 'store' not in category_dict or not category_dict['store']:
            category_dict['store'] = 'mercadolivre'  # Valor padrão
            log.info(f"Campo 'store' não fornecido na atualização, usando valor padrão: {category_dict['store']}")

        # Garantir que o ID da categoria esteja presente
        if 'id' not in category_dict or not category_dict['id']:
            category_dict['id'] = category_id
            log.info(f"Adicionando ID da categoria ao dicionário: {category_id}")

        updated_category = await promohunter_service.update_category(category_dict)

        # Garantir que o campo store esteja presente no resultado
        if 'store' not in updated_category:
            updated_category['store'] = category_dict['store']

        # Converter o dicionário de volta para o modelo Pydantic
        return OfferCategoryResponse(**updated_category)
    except Exception as e:
        log.error(f"Erro ao atualizar categoria de ofertas: {e}")
        raise handle_exception(e)


@router.delete(
    "/categories/{category_id}",
    dependencies=[Depends(verify_supabase_session)],
)
async def delete_offer_category(
    category_id: int,
    user_info: dict = Depends(verify_supabase_session)
):
    """
    Remove uma categoria de ofertas.

    Args:
        category_id: ID da categoria
        user_info: Informações do usuário autenticado

    Returns:
        dict: Mensagem de sucesso
    """
    log.info(f"Usuário {user_info.get('email')} está removendo a categoria de ofertas {category_id}")
    if not promohunter_service:
        raise handle_exception(Exception("Serviço de PromoHunter não disponível"))
    try:
        # Obter a loja da categoria - passando 'mercadolivre' como padrão para obter categorias
        # e depois verificar todas as lojas
        stores = ['mercadolivre', 'magalu', 'amazon']
        store = None

        # Procurar a categoria em todas as lojas
        for current_store in stores:
            try:
                # Sempre passar o parâmetro store para o método get_categories
                categories = await promohunter_service.get_categories(store=current_store)
                for category in categories:
                    if category.get('id') == category_id:
                        store = current_store
                        break
                if store:
                    break
            except Exception as e:
                log.warning(f"Erro ao obter categorias para {current_store}: {e}")

        if not store:
            store = 'mercadolivre'  # Valor padrão se não encontrar
            log.warning(f"Não foi possível determinar a loja para a categoria {category_id}, usando padrão: {store}")

        # Chamar o método com os dois parâmetros necessários
        if store:
            await promohunter_service.delete_category(store, category_id)
            return {"message": "Categoria removida com sucesso"}
        else:
            raise ValueError(f"Não foi possível determinar a loja para a categoria {category_id}")
    except Exception as e:
        log.error(f"Erro ao remover categoria de ofertas: {e}")
        raise handle_exception(e)


@router.get(
    "/scrape",
    response_model=OfferScraperResponse,
    dependencies=[Depends(verify_supabase_session)],
)
async def run_offer_scraper(
    store: str = Query(..., description="Loja a ser raspada (mercadolivre, magalu, amazon)"),
    category_id: Optional[int] = Query(None, description="ID da categoria específica para raspar"),
    max_pages: int = Query(2, description="Número máximo de páginas por categoria"),
    user_info: dict = Depends(verify_supabase_session)
):
    """
    Executa o scraper de ofertas.

    Args:
        store: Loja a ser raspada
        category_id: ID da categoria específica (opcional)
        max_pages: Número máximo de páginas por categoria
        user_info: Informações do usuário autenticado

    Returns:
        OfferScraperResponse: Resultado da execução do scraper
    """
    log.info(f"Usuário {user_info.get('email')} iniciou scraping de ofertas para {store}")
    if not promohunter_service:
        raise handle_exception(Exception("Serviço de PromoHunter não disponível"))
    try:
        result = await promohunter_service.run_scraper(store, category_id, max_pages)
        return result
    except Exception as e:
        log.error(f"Erro ao executar scraper de ofertas: {e}")
        raise handle_exception(e)


@router.get(
    "/scrape/status",
    dependencies=[Depends(verify_supabase_session)],
)
async def get_scraper_status(
    user_info: dict = Depends(verify_supabase_session)
):
    """
    Obtém o status atual do scraper de ofertas.

    Args:
        user_info: Informações do usuário autenticado

    Returns:
        dict: Status do scraper
    """
    log.info(f"Usuário {user_info.get('email')} requisitou status do scraper de ofertas")
    if not promohunter_service:
        raise handle_exception(Exception("Serviço de PromoHunter não disponível"))
    try:
        status = await promohunter_service.get_scraper_status()
        return status
    except Exception as e:
        log.error(f"Erro ao obter status do scraper de ofertas: {e}")
        raise handle_exception(e)


@router.post(
    "/scrape/stop",
    dependencies=[Depends(verify_supabase_session)],
)
async def stop_offer_scraper(
    user_info: dict = Depends(verify_supabase_session)
):
    """
    Para a execução do scraper de ofertas.

    Args:
        user_info: Informações do usuário autenticado

    Returns:
        dict: Mensagem de sucesso
    """
    log.info(f"Usuário {user_info.get('email')} solicitou parar o scraper de ofertas")
    if not promohunter_service:
        raise handle_exception(Exception("Serviço de PromoHunter não disponível"))
    try:
        result = await promohunter_service.stop_scraper()
        return {"message": "Scraper parado com sucesso", "status": result}
    except Exception as e:
        log.error(f"Erro ao parar scraper de ofertas: {e}")
        raise handle_exception(e)


@router.get(
    "/selectors",
    response_model=List[SelectorOutput],
    dependencies=[Depends(verify_supabase_session)],
)
async def get_promohunter_selectors(
    store: Optional[str] = Query(None, description="Filtrar por loja (mercadolivre, magalu, amazon)"),
    selector_type: Optional[str] = Query(None, description="Filtrar por tipo de seletor"),
    status: Optional[str] = Query(None, description="Filtrar por status (ativos, inativos, todos)"),
    user_info: dict = Depends(verify_supabase_session)
):
    """
    Obtém os seletores do PromoHunter.

    Args:
        store: Filtro opcional por loja
        selector_type: Filtro opcional por tipo de seletor
        status: Filtro opcional por status (ativos, inativos, todos)
        user_info: Informações do usuário autenticado

    Returns:
        List[SelectorOutput]: Lista de seletores
    """
    log.info(f"Usuário {user_info.get('email')} requisitou seletores do PromoHunter")
    if not promohunter_service:
        raise handle_exception(Exception("Serviço de PromoHunter não disponível"))
    try:
        selectors = await promohunter_service.get_selectors(store, selector_type, status)
        return selectors
    except Exception as e:
        log.error(f"Erro ao obter seletores do PromoHunter: {e}")
        raise handle_exception(e)


@router.post(
    "/selectors",
    response_model=SelectorOutput,
    dependencies=[Depends(verify_supabase_session)],
)
async def create_promohunter_selector(
    selector: SelectorData,
    user_info: dict = Depends(verify_supabase_session)
):
    """
    Cria um novo seletor para o PromoHunter.

    Args:
        selector: Dados do seletor
        user_info: Informações do usuário autenticado

    Returns:
        SelectorOutput: Seletor criado
    """
    log.info(f"Usuário {user_info.get('email')} está criando um novo seletor para o PromoHunter")
    if not promohunter_service:
        raise handle_exception(Exception("Serviço de PromoHunter não disponível"))
    try:
        created_selector = await promohunter_service.create_selector(selector)
        return created_selector
    except Exception as e:
        log.error(f"Erro ao criar seletor para o PromoHunter: {e}")
        raise handle_exception(e)


@router.put(
    "/selectors/{selector_id}",
    response_model=SelectorOutput,
    dependencies=[Depends(verify_supabase_session)],
)
async def update_promohunter_selector(
    selector_id: int,
    selector: SelectorData,
    user_info: dict = Depends(verify_supabase_session)
):
    """
    Atualiza um seletor existente do PromoHunter.

    Args:
        selector_id: ID do seletor
        selector: Novos dados do seletor
        user_info: Informações do usuário autenticado

    Returns:
        SelectorOutput: Seletor atualizado
    """
    log.info(f"Usuário {user_info.get('email')} está atualizando o seletor {selector_id} do PromoHunter")
    if not promohunter_service:
        raise handle_exception(Exception("Serviço de PromoHunter não disponível"))
    try:
        updated_selector = await promohunter_service.update_selector(selector_id, selector)
        return updated_selector
    except Exception as e:
        log.error(f"Erro ao atualizar seletor do PromoHunter: {e}")
        raise handle_exception(e)


@router.delete(
    "/selectors/{selector_id}",
    dependencies=[Depends(verify_supabase_session)],
)
async def delete_promohunter_selector(
    selector_id: int,
    user_info: dict = Depends(verify_supabase_session)
):
    """
    Remove um seletor do PromoHunter.

    Args:
        selector_id: ID do seletor
        user_info: Informações do usuário autenticado

    Returns:
        dict: Mensagem de sucesso
    """
    log.info(f"Usuário {user_info.get('email')} está removendo o seletor {selector_id} do PromoHunter")
    if not promohunter_service:
        raise handle_exception(Exception("Serviço de PromoHunter não disponível"))
    try:
        await promohunter_service.delete_selector(selector_id)
        return {"message": "Seletor removido com sucesso"}
    except Exception as e:
        log.error(f"Erro ao remover seletor do PromoHunter: {e}")
        raise handle_exception(e)


@router.post(
    "/selectors/validate",
    dependencies=[Depends(verify_supabase_session)],
)
async def validate_promohunter_selectors(
    store: str = Query(..., description="Loja para validar os seletores (mercadolivre, magalu, amazon)"),
    url: Optional[str] = Query(None, description="URL opcional para testar os seletores"),
    user_info: dict = Depends(verify_supabase_session)
):
    """
    Valida os seletores do PromoHunter para uma loja específica.

    Args:
        store: Loja para validar os seletores
        url: URL opcional para testar os seletores
        user_info: Informações do usuário autenticado

    Returns:
        dict: Resultado da validação
    """
    log.info(f"Usuário {user_info.get('email')} está validando os seletores do PromoHunter para {store}")
    if not promohunter_service:
        raise handle_exception(Exception("Serviço de PromoHunter não disponível"))
    try:
        result = await promohunter_service.validate_selectors(store, url)
        return result
    except Exception as e:
        log.error(f"Erro ao validar seletores do PromoHunter: {e}")
        raise handle_exception(e)


@router.post(
    "/selectors/test",
    dependencies=[Depends(verify_supabase_session)],
)
async def test_promohunter_selector(
    store: str = Query(..., description="Loja para testar o seletor (mercadolivre, magalu, amazon)"),
    selector: str = Query(..., description="Seletor CSS a ser testado"),
    selector_type: str = Query(..., description="Tipo do seletor (product, title, price, etc.)"),
    url: str = Query(..., description="URL para testar o seletor"),
    user_info: dict = Depends(verify_supabase_session)
):
    """
    Testa um seletor específico do PromoHunter em uma URL.

    Args:
        store: Loja para testar o seletor
        selector: Seletor CSS a ser testado
        selector_type: Tipo do seletor
        url: URL para testar o seletor
        user_info: Informações do usuário autenticado

    Returns:
        dict: Resultado do teste do seletor
    """
    log.info(f"Usuário {user_info.get('email')} está testando o seletor '{selector}' do tipo '{selector_type}' para {store}")
    if not promohunter_service:
        raise handle_exception(Exception("Serviço de PromoHunter não disponível"))
    try:
        result = await promohunter_service.test_selector(store, selector, selector_type, url)
        return result
    except Exception as e:
        log.error(f"Erro ao testar seletor do PromoHunter: {e}")
        raise handle_exception(e)
