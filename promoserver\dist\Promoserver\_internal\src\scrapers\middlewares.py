"""
Middlewares do Scrapy para o PromoScraper
"""

import logging
import random
import time
from typing import List, Dict, Optional
from scrapy import signals
from scrapy.http import Response, Request
from scrapy.downloadermiddlewares.retry import RetryMiddleware
from scrapy.utils.response import response_status_message

logger = logging.getLogger(__name__)


class RandomUserAgentMiddleware:
    """
    Middleware para rotação de User Agents
    """

    user_agents = [
        # Chrome
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
        # Firefox
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:124.0) Gecko/20100101 Firefox/124.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:124.0) Gecko/20100101 Firefox/124.0',
        # Edge
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0',
        # Safari
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15',
    ]

    def __init__(self):
        self.user_agent = random.choice(self.user_agents)
        logger.info(f"Usando User-Agent: {self.user_agent}")

    def process_request(self, request, spider):
        """Aplica um User-Agent aleatório ao request"""
        # Alterna o User-Agent a cada 10 requisições
        if random.random() < 0.1:
            self.user_agent = random.choice(self.user_agents)
            logger.debug(f"Alterando User-Agent para: {self.user_agent}")

        request.headers['User-Agent'] = self.user_agent
        return None


class AdaptiveDelayMiddleware:
    """
    Middleware para aplicar delays adaptativos entre requisições
    """

    def __init__(self, delay=1.0, randomize=True):
        self.delay = delay
        self.randomize = randomize
        self.min_delay = 0.5
        self.max_delay = 2.5
        self.last_requests = {}

    @classmethod
    def from_crawler(cls, crawler):
        settings = crawler.settings
        delay = settings.getfloat('DOWNLOAD_DELAY', 1.0)
        randomize = settings.getbool('RANDOMIZE_DOWNLOAD_DELAY', True)
        return cls(delay, randomize)

    def process_request(self, request, spider):
        """Aplica delay adaptativo"""
        domain = request.url.split('/')[2]

        # Verificar tempo desde a última requisição para este domínio
        now = time.time()
        last_request = self.last_requests.get(domain, 0)
        time_passed = now - last_request

        # Calcular delay necessário
        if time_passed < self.min_delay:
            delay = self.delay
            if self.randomize:
                delay = random.uniform(self.min_delay, self.max_delay)

            time_to_wait = delay - time_passed
            if time_to_wait > 0:
                logger.debug(f"Aguardando {time_to_wait:.2f}s para {domain}")
                time.sleep(time_to_wait)

        # Atualizar timestamp da última requisição
        self.last_requests[domain] = time.time()

        return None


class CaptchaDetectionMiddleware:
    """
    Middleware para detectar CAPTCHAs e outras proteções anti-bot
    """

    def process_response(self, request, response, spider):
        """Verifica se a resposta contém CAPTCHA ou outras proteções"""
        # Verificar status code
        if response.status in [403, 429]:
            logger.warning(f"Possível bloqueio detectado: Status {response.status} para {request.url}")
            return self._retry_or_pass(request, response, spider)

        # Verificar conteúdo da página
        captcha_indicators = [
            'captcha', 'robot', 'automated', 'blocked', 'suspicious',
            'unusual activity', 'verify you are a human'
        ]

        # Verifica se a resposta é texto antes de tentar acessar response.text
        try:
            if hasattr(response, 'text') and callable(getattr(response, 'text', None)):
                page_text = response.text.lower()
                for indicator in captcha_indicators:
                    if indicator in page_text:
                        logger.warning(f"Possível CAPTCHA detectado: '{indicator}' encontrado em {request.url}")
                        return self._retry_or_pass(request, response, spider)
        except AttributeError:
            logger.warning(f"Resposta não é texto para {request.url}")
        except Exception as e:
            logger.warning(f"Erro ao verificar texto da resposta para {request.url}: {e}")

        return response

    def _retry_or_pass(self, request, response, spider):
        """Decide se deve tentar novamente ou passar a resposta"""
        # Verificar se já tentou muitas vezes
        retries = request.meta.get('retry_times', 0)
        max_retries = spider.settings.getint('RETRY_TIMES', 3)

        if retries < max_retries:
            logger.info(f"Tentando novamente ({retries+1}/{max_retries}): {request.url}")
            retryreq = request.copy()
            retryreq.meta['retry_times'] = retries + 1
            retryreq.dont_filter = True
            return retryreq
        else:
            logger.warning(f"Máximo de tentativas atingido para {request.url}")
            return response
