"""
Módulo de endpoints de categorias para a API do Promoserver.
Contém funções relacionadas aos endpoints de categorias.
"""

import logging
from typing import List
from fastapi import APIRouter, Depends

from components.auth import verify_supabase_session
from components.models import CategoryResponse
from components.exceptions import ServiceUnavailableException, handle_exception

log = logging.getLogger("api.categories")

# Roteador para endpoints de categorias
router = APIRouter(prefix="/categories", tags=["categories"])

# Referência ao ProductCategories (será definida na inicialização)
category_loader = None

def initialize(c_loader):
    """Inicializa o módulo com a instância do ProductCategories."""
    global category_loader
    category_loader = c_loader

@router.get(
    "",
    response_model=List[CategoryResponse],
    dependencies=[Depends(verify_supabase_session)],
)
async def get_categories(user_info: dict = Depends(verify_supabase_session)):
    """
    Retorna todas as categorias.
    
    Args:
        user_info: Informações do usuário autenticado
    
    Returns:
        List[CategoryResponse]: Lista de categorias
    """
    log.info(f"Usuário {user_info.get('email')} requisitando GET /categories")
    try:
        if not category_loader or not category_loader.categories:
            log.warning(
                f"Serviço de Categorias não disponível para o usuário {user_info.get('email')}"
            )
            raise ServiceUnavailableException("Serviço de Categorias")
        categories_list = []
        for category_key, data in category_loader.categories.items():
            categories_list.append(
                {
                    "key": str(category_key),
                    "name": data.get("name", "N/A"),
                    "subcategories": data.get("subcategories", []),
                }
            )
        log.info(
            f"Retornando {len(categories_list)} categorias para usuário {user_info.get('email')}"
        )
        return sorted(categories_list, key=lambda x: x["name"])
    except Exception as e:
        log.error(f"Erro ao buscar categorias para usuário {user_info.get('email')}: {e}")
        log.exception(e)
        raise handle_exception(e, "Erro ao buscar categorias")
