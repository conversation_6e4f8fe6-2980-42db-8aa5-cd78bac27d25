class ProdutoSupabase:
    def __init__(
        self,
        plataforma: str = "",
        url_afiliado: str = "",
        url_produto: str = "",  # Adicionado campo url_produto
        url_imagem: str = "",
        image_id: str = "",  # ID da imagem no Digital Ocean
        is_local_image: bool = False,  # Indica se a imagem é local (adicionado para compatibilidade com o frontend)
        titulo: str = "",
        categoria: str = "",
        subcategoria: str = "",
        descricao: str = "",
        preco_atual: float = 0.0,
        preco_antigo: float = 0.0,
        preco_alternativo: float = 0.0,
        ativo: bool = True,
        cupom: str = "",
        menor_preco: bool = False,
        indicamos: bool = False,
        disparar_whatsapp: bool = False,
        grupo_whatsapp: str = "",
        frete: bool = False,
        isStory: bool = False,
        invalidProduct: bool = False,
    ):
        self.plataforma = plataforma
        self.url_afiliado = url_afiliado
        self.url_produto = url_produto  # Adicionado campo url_produto
        self.url_imagem = url_imagem
        self.image_id = image_id  # ID da imagem no Digital Ocean
        self.is_local_image = is_local_image  # Indica se a imagem é local
        self.titulo = titulo
        self.categoria = categoria
        self.subcategoria = subcategoria
        self.descricao = descricao
        self.preco_atual = preco_atual
        self.preco_antigo = preco_antigo
        self.preco_alternativo = preco_alternativo
        self.ativo = ativo
        self.cupom = cupom
        self.menor_preco = menor_preco
        self.indicamos = indicamos
        self.disparar_whatsapp = disparar_whatsapp
        self.grupo_whatsapp = grupo_whatsapp
        self.frete = frete  # Corrigido: removido a tupla
        self.isStory = isStory
        self.invalidProduct = invalidProduct

    def to_dict(self):
        """Converte o objeto para um dicionário compatível com a estrutura da tabela no Supabase."""
        # Criar um dicionário com os campos que existem na tabela produtos_cadastro
        data = {
            "plataforma": self.plataforma,
            "url_afiliado": self.url_afiliado,
            "url_produto": self.url_produto,  # Campo adicionado na tabela
            "url_imagem": self.url_imagem,
            "image_id": self.image_id,
            # Não incluir is_local_image pois não existe na tabela do banco de dados
            # "is_local_image": self.is_local_image,
            "titulo": self.titulo,
            "categoria": self.categoria,
            "subcategoria": self.subcategoria,
            "descricao": self.descricao,
            "preco_atual": self.preco_atual,
            "preco_antigo": self.preco_antigo,
            "preco_alternativo": self.preco_alternativo,
            "ativo": self.ativo,
            "cupom": self.cupom,
            "menor_preco": self.menor_preco,
            "indicamos": self.indicamos,
            "disparar_whatsapp": self.disparar_whatsapp,
            "grupo_whatsapp": self.grupo_whatsapp,
            "frete": bool(self.frete),
            "isStory": self.isStory,
            "invalidProduct": self.invalidProduct,
        }
        return data

    @staticmethod
    def from_dict(data):
        return ProdutoSupabase(**data)
