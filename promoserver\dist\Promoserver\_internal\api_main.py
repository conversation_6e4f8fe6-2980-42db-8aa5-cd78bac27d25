import sys
import os
from pathlib import Path

import uvicorn
from fastapi import FastAPI

# Configurar sys.path para ambiente empacotado
if getattr(sys, 'frozen', False):
    # Executável empacotado
    bundle_dir = Path(sys.executable).parent
    internal_dir = bundle_dir / '_internal'

    # Adicionar diretórios ao sys.path
    if internal_dir.exists():
        sys.path.insert(0, str(internal_dir))
    sys.path.insert(0, str(bundle_dir))

    print(f"[DEBUG] Ambiente empacotado detectado")
    print(f"[DEBUG] Bundle dir: {bundle_dir}")
    print(f"[DEBUG] Internal dir: {internal_dir}")
    print(f"[DEBUG] sys.path atualizado: {sys.path[:3]}")

# Import components
# Assuming exceptions are handled within components or re-raised
from components import (
    auth,
    categories,
    config,
    products,
    promohunter,
    scraping,
    selectors,
    selectors_validation,
)

# --- Setup ---
log = config.setup_logging()
project_root, supabase_url, supabase_key = config.setup_environment()

# --- Service Initialization ---
try:
    (
        selector_manager_instance,
        product_service_instance,
        supabase_service_instance,
        category_loader_instance,
        product_db_service_instance,
        promohunter_service_instance,
    ) = config.initialize_services()
except Exception as e:
    log.critical(f"FATAL: Failed to initialize services: {e}", exc_info=True)
    sys.exit(f"Failed to initialize services: {e}")


# --- FastAPI Application ---
app = FastAPI(
    title="Promoserver",
    description="Backend API for Promotor application",
    version="1.0.0",
)

# --- Middleware ---
config.configure_cors(app)

# --- Component Initialization ---
# Pass necessary service instances to each component module
# Note: The verify_supabase_session dependency in auth.py needs access to supabase_service_instance
# We pass it during initialization or rely on the dependency injection within verify_supabase_session itself.
# The current implementation in auth.py seems to handle finding the service if not directly passed.
auth.initialize(supabase_service_instance)
categories.initialize(category_loader_instance)
products.initialize(
    product_service_instance, supabase_service_instance, product_db_service_instance
)
promohunter.initialize(promohunter_service_instance)
scraping.initialize(product_service_instance)
selectors.initialize(selector_manager_instance)
selectors_validation.initialize()

# --- Routers ---
# Include routers from component modules
app.include_router(auth.router)
app.include_router(categories.router)
app.include_router(products.router)
app.include_router(promohunter.router)
app.include_router(scraping.router)
app.include_router(selectors.router)
app.include_router(selectors_validation.router)


# --- Root Endpoint ---
@app.get("/")
async def read_root():
    """Root endpoint indicating the API is running."""
    return {"message": "API Backend do Promotor está em execução!"}


# --- Run Server ---
def run_server(host="127.0.0.1", port=8000, reload=False):
    """Starts the Uvicorn server."""
    log.info(f"Iniciando servidor API em http://{host}:{port} (Reload: {reload})")
    uvicorn.run("api_main:app", host=host, port=port, reload=reload, log_level="info")


if __name__ == "__main__":
    # Example: Start the server listening on all interfaces
    run_server()
