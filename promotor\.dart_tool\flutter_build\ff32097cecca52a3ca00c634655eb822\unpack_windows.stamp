{"inputs": ["C:\\src\\flutter\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\windows.dart", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll.exp", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll.lib", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.dll.pdb", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_export.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_messenger.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_plugin_registrar.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_texture_registrar.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\flutter_windows.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\icudtl.dat", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\binary_messenger_impl.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\byte_buffer_streams.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\core_implementations.cc", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\engine_method_result.cc", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\flutter_engine.cc", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\flutter_view_controller.cc", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\basic_message_channel.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\binary_messenger.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\byte_streams.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\dart_project.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\encodable_value.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\engine_method_result.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_channel.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_sink.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_stream_handler.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\event_stream_handler_functions.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\flutter_engine.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\flutter_view.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\flutter_view_controller.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\message_codec.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_call.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_channel.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_codec.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_result.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\method_result_functions.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\plugin_registrar.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\plugin_registrar_windows.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\plugin_registry.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\standard_codec_serializer.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\standard_message_codec.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\standard_method_codec.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\include\\flutter\\texture_registrar.h", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\plugin_registrar.cc", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\readme", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\standard_codec.cc", "C:\\src\\flutter\\bin\\cache\\artifacts\\engine\\windows-x64\\cpp_client_wrapper\\texture_registrar_impl.h"], "outputs": ["C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\flutter_windows.dll", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\flutter_windows.dll.exp", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\flutter_windows.dll.lib", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\flutter_windows.dll.pdb", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\flutter_export.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\flutter_messenger.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\flutter_plugin_registrar.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\flutter_texture_registrar.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\flutter_windows.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\icudtl.dat", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\binary_messenger_impl.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\byte_buffer_streams.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\core_implementations.cc", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\engine_method_result.cc", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\flutter_engine.cc", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\flutter_view_controller.cc", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\basic_message_channel.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\binary_messenger.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\byte_streams.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\dart_project.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\encodable_value.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\engine_method_result.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_channel.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_sink.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_stream_handler.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_stream_handler_functions.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_engine.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_view.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_view_controller.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\message_codec.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_call.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_channel.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_codec.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_result.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_result_functions.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registrar.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registrar_windows.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registry.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_codec_serializer.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_message_codec.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_method_codec.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\texture_registrar.h", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\plugin_registrar.cc", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\readme", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\standard_codec.cc", "C:\\TRABALHO\\PROMOBELL\\promotor\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\texture_registrar_impl.h"]}