"""
Serviço para operações relacionadas ao PromoHunter.
"""

import json
import logging
import os
import threading
import time
import requests
from datetime import datetime
from typing import Dict, List, Optional, Any
from bs4 import BeautifulSoup

from src.utils.selector_manager import SelectorManager
from src.scrapers.base.header_manager import HeaderManager

log = logging.getLogger(__name__)


class PromoHunterService:
    """Serviço para operações relacionadas ao PromoHunter."""

    def __init__(self, selector_manager: Optional[SelectorManager] = None):
        """
        Inicializa o serviço do PromoHunter.

        Args:
            selector_manager: Gerenciador de seletores (opcional)
        """
        self.selector_manager = selector_manager or SelectorManager()
        self.header_manager = HeaderManager()
        self.base_dir = os.path.dirname(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        )
        self.categories_dir = os.path.join(
            self.base_dir, "src", "data", "offer_categories"
        )
        self.output_dir = os.path.join(self.base_dir, "output")

        # Criar diretórios se não existirem
        os.makedirs(self.categories_dir, exist_ok=True)
        os.makedirs(self.output_dir, exist_ok=True)

        # Status do scraper
        self._is_running = False
        self.current_status = "Não iniciado"
        self.scraper_stats = {
            "total_products": 0,
            "processed_categories": 0,
            "processed_pages": 0,
            "errors": [],
        }

        # Lock para acesso concorrente
        self._lock = threading.Lock()

        # Carregar categorias
        self._load_categories()

    def _load_categories(self):
        """Carrega as categorias de ofertas dos arquivos JSON."""
        self.categories = {"mercadolivre": [], "magalu": [], "amazon": []}

        # Verificar e criar arquivos de categorias se não existirem
        for store in self.categories.keys():
            file_path = os.path.join(self.categories_dir, f"{store}_categories.json")
            if not os.path.exists(file_path):
                with open(file_path, "w", encoding="utf-8") as f:
                    json.dump([], f, ensure_ascii=False, indent=4)

            # Carregar categorias
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    self.categories[store] = json.load(f)
            except Exception as e:
                log.error(f"Erro ao carregar categorias de {store}: {e}")
                self.categories[store] = []

    def _save_categories(self, store: str):
        """
        Salva as categorias de ofertas em um arquivo JSON.

        Args:
            store: Loja para a qual salvar as categorias
        """
        file_path = os.path.join(self.categories_dir, f"{store}_categories.json")
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(self.categories[store], f, ensure_ascii=False, indent=4)
            log.info(f"Categorias de {store} salvas com sucesso")
        except Exception as e:
            log.error(f"Erro ao salvar categorias de {store}: {e}")
            raise e

    async def get_categories(self, store: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Obtém as categorias de ofertas.

        Args:
            store: Filtro opcional por loja

        Returns:
            List[Dict[str, Any]]: Lista de categorias de ofertas
        """
        # Recarregar categorias para garantir dados atualizados
        self._load_categories()

        if store and store in self.categories:
            # Adicionar o campo 'store' a cada categoria
            result = []
            for category in self.categories[store]:
                category_copy = category.copy()
                category_copy["store"] = store
                result.append(category_copy)
            return result
        elif not store:
            # Retornar todas as categorias de todas as lojas
            all_categories = []
            for store_name, categories in self.categories.items():
                for category in categories:
                    category_copy = category.copy()
                    if "store" not in category_copy:
                        category_copy["store"] = store_name
                    all_categories.append(category_copy)
            return all_categories
        else:
            log.warning(f"Loja {store} não encontrada")
            return []

    async def create_category(self, category: Dict[str, Any]) -> Dict[str, Any]:
        """
        Cria uma nova categoria de ofertas.

        Args:
            category: Dados da categoria

        Returns:
            Dict[str, Any]: Categoria criada
        """
        store = category.get("store")
        if not store or store not in self.categories:
            raise ValueError(f"Loja inválida: {store}")

        # Gerar ID para a nova categoria
        max_id = 0
        for cat in self.categories[store]:
            if cat.get("id", 0) > max_id:
                max_id = cat.get("id", 0)

        new_category = {
            "id": max_id + 1,
            "name": category.get("name"),
            "url_template": category.get("url_template"),
            "max_page": category.get("max_page", 2),
            "active": category.get("active", True),
        }

        self.categories[store].append(new_category)
        self._save_categories(store)

        # Adicionar a loja ao resultado
        result = new_category.copy()
        result["store"] = store
        return result

    async def update_category(
        self, category_id: int, category: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Atualiza uma categoria de ofertas existente.

        Args:
            category_id: ID da categoria
            category: Novos dados da categoria

        Returns:
            Dict[str, Any]: Categoria atualizada
        """
        store = category.get("store")
        if not store or store not in self.categories:
            raise ValueError(f"Loja inválida: {store}")

        # Encontrar a categoria pelo ID
        for i, cat in enumerate(self.categories[store]):
            if cat.get("id") == category_id:
                # Atualizar a categoria
                self.categories[store][i] = {
                    "id": category_id,
                    "name": category.get("name"),
                    "url_template": category.get("url_template"),
                    "max_page": category.get("max_page", 2),
                    "active": category.get("active", True),
                }
                self._save_categories(store)

                # Adicionar a loja ao resultado
                result = self.categories[store][i].copy()
                result["store"] = store
                return result

        raise ValueError(
            f"Categoria com ID {category_id} não encontrada para a loja {store}"
        )

    async def delete_category(self, category_id: int) -> None:
        """
        Remove uma categoria de ofertas.

        Args:
            category_id: ID da categoria
        """
        # Procurar a categoria em todas as lojas
        for store in self.categories:
            for i, cat in enumerate(self.categories[store]):
                if cat.get("id") == category_id:
                    # Remover a categoria
                    self.categories[store].pop(i)
                    self._save_categories(store)
                    return

        raise ValueError(f"Categoria com ID {category_id} não encontrada")

    async def run_scraper(
        self, store: str, category_id: Optional[int] = None, max_pages: int = 2
    ) -> Dict[str, Any]:
        """
        Executa o scraper de ofertas.

        Args:
            store: Loja a ser raspada
            category_id: ID da categoria específica (opcional)
            max_pages: Número máximo de páginas por categoria

        Returns:
            Dict[str, Any]: Resultado da execução do scraper
        """
        with self._lock:
            if self._is_running:
                return {
                    "status": "running",
                    "message": "O scraper já está em execução",
                    "total_products": self.scraper_stats["total_products"],
                    "processed_categories": self.scraper_stats["processed_categories"],
                    "processed_pages": self.scraper_stats["processed_pages"],
                    "errors": self.scraper_stats["errors"],
                    "is_running": True,
                    "is_completed": False,
                    "has_error": False,
                }

            # Resetar estatísticas
            self.scraper_stats = {
                "total_products": 0,
                "processed_categories": 0,
                "processed_pages": 0,
                "errors": [],
                "start_time": datetime.now().isoformat(),
            }

            # Iniciar scraper em uma thread separada
            self._is_running = True
            self.current_status = f"Iniciando scraper para {store}"

            # Criar thread
            thread = threading.Thread(
                target=self._run_scraper_task, args=(store, category_id, max_pages)
            )
            thread.daemon = True
            thread.start()

            return {
                "status": "started",
                "message": f"Scraper iniciado para {store}",
                "total_products": 0,
                "processed_categories": 0,
                "processed_pages": 0,
                "errors": [],
                "is_running": True,
                "is_completed": False,
                "has_error": False,
            }

    def _run_scraper_task(
        self, store: str, category_id: Optional[int] = None, max_pages: int = 2
    ) -> None:
        """
        Tarefa para executar o scraper de ofertas.

        Args:
            store: Loja a ser raspada
            category_id: ID da categoria específica (opcional)
            max_pages: Número máximo de páginas por categoria
        """
        try:
            # Filtrar categorias
            categories = []
            if category_id:
                # Buscar apenas a categoria específica
                for cat in self.categories[store]:
                    if cat.get("id") == category_id and cat.get("active", True):
                        categories.append(cat)
                        break
            else:
                # Buscar todas as categorias ativas
                categories = [
                    cat for cat in self.categories[store] if cat.get("active", True)
                ]

            if not categories:
                self.scraper_stats["errors"].append(
                    f"Nenhuma categoria ativa encontrada para {store}"
                )
                self._is_running = False
                self.current_status = "Finalizado com erro"
                return

            # Processar cada categoria
            for category in categories:
                if not self._is_running:
                    break

                self.current_status = f"Processando categoria: {category['name']}"
                log.info(f"Processando categoria: {category['name']}")

                # Limitar o número máximo de páginas
                max_page = min(category.get("max_page", 2), max_pages)

                # Criar arquivo CSV para a categoria
                output_file = os.path.join(
                    self.output_dir, f"{store}_{category['name'].replace(' ', '_')}.csv"
                )

                # Cabeçalho do CSV
                headers = [
                    "plataforma",
                    "url",
                    "url_afiliado",
                    "url_imagem",
                    "titulo",
                    "categoria",
                    "subcategoria",
                    "descricao",
                    "preco_atual",
                    "preco_antigo",
                    "preco_alternativo",
                    "ativo",
                    "cupom",
                    "menor_preco",
                    "indicamos",
                    "disparar_whatsapp",
                    "grupo_whatsapp",
                    "frete",
                    "invalidProduct",
                    "isStory",
                ]

                # Criar ou sobrescrever arquivo CSV com cabeçalho
                with open(output_file, "w", encoding="utf-8-sig", newline="") as f:
                    f.write(",".join(headers) + "\n")

                # Processar cada página
                for page in range(1, max_page + 1):
                    if not self._is_running:
                        break

                    self.current_status = f"Processando categoria: {category['name']} - Página {page}/{max_page}"
                    log.info(f"Processando página {page} de {max_page}")

                    # Substituir {i} na URL pelo número da página
                    url = category["url_template"].format(i=page)

                    try:
                        # Obter produtos da página
                        products = self._scrape_page(store, url)

                        if products:
                            # Salvar produtos no CSV
                            with open(
                                output_file, "a", encoding="utf-8-sig", newline=""
                            ) as f:
                                for product in products:
                                    # Converter produto para linha CSV
                                    csv_line = (
                                        ",".join(
                                            [
                                                f'"{str(product.get(field, "")).replace("\"", "\"\"")}"'
                                                for field in headers
                                            ]
                                        )
                                        + "\n"
                                    )
                                    f.write(csv_line)

                            self.scraper_stats["total_products"] += len(products)

                        self.scraper_stats["processed_pages"] += 1

                    except Exception as e:
                        error_msg = f"Erro ao processar página {page} da categoria {category['name']}: {str(e)}"
                        log.error(error_msg)
                        self.scraper_stats["errors"].append(error_msg)

                self.scraper_stats["processed_categories"] += 1

            self.current_status = "Finalizado com sucesso"

        except Exception as e:
            error_msg = f"Erro ao executar scraper: {str(e)}"
            log.error(error_msg)
            self.scraper_stats["errors"].append(error_msg)
            self.current_status = "Finalizado com erro"

        finally:
            self._is_running = False

    def _scrape_page(self, store: str, url: str) -> List[Dict[str, Any]]:
        """
        Raspa os produtos de uma página.

        Args:
            store: Loja a ser raspada
            url: URL da página

        Returns:
            List[Dict[str, Any]]: Lista de produtos encontrados
        """
        products = []

        # Obter headers aleatórios
        headers = self.header_manager.get_random_header()

        try:
            # Fazer requisição HTTP
            response = requests.get(url, headers=headers, timeout=30)
            if response.status_code != 200:
                log.error(f"Erro ao acessar {url}: {response.status_code}")
                return []

            soup = BeautifulSoup(response.text, "html.parser")

            # Obter seletores ativos para produtos
            product_selectors = self.selector_manager.get_selectors_by_type(
                store, "product"
            )
            product_selectors = [
                s["selector"] for s in product_selectors if s.get("active", True)
            ]

            # Se não houver seletores específicos, usar seletores padrão
            if not product_selectors:
                if store == "mercadolivre":
                    product_selectors = [
                        "li.ui-search-layout__item",
                        "div.ui-search-result",
                        "div.promotion-item",
                        "div.andes-card.poly-card--grid-card",
                    ]
                elif store == "magalu":
                    product_selectors = [
                        "li.productShowCase",
                        "li.product-li",
                        "div.product-card",
                    ]
                elif store == "amazon":
                    product_selectors = [
                        "div.s-result-item",
                        "div.sg-col-4-of-12",
                        "div.a-section.a-spacing-base",
                    ]

            # Tenta cada seletor até encontrar produtos
            product_elements = []
            for selector in product_selectors:
                product_elements = soup.select(selector)
                if product_elements:
                    log.info(f"Produtos encontrados usando seletor: {selector}")
                    break

            if not product_elements:
                log.warning(f"Nenhum produto encontrado na página {url}")
                return []

            # Processar cada produto
            for element in product_elements:
                try:
                    product = self._extract_product_data(store, element, url)
                    if product:
                        products.append(product)
                except Exception as e:
                    log.error(f"Erro ao extrair dados do produto: {e}")

        except Exception as e:
            log.error(f"Erro ao raspar página {url}: {e}")

        return products

    def _extract_product_data(
        self, store: str, element, page_url: str
    ) -> Optional[Dict[str, Any]]:
        """
        Extrai os dados de um produto a partir do elemento HTML.

        Args:
            store: Loja do produto
            element: Elemento HTML do produto
            page_url: URL da página

        Returns:
            Optional[Dict[str, Any]]: Dados do produto ou None se não for possível extrair
        """
        try:
            # Obter seletores para cada campo
            title_selectors = self.selector_manager.get_selectors_by_type(
                store, "title"
            )
            title_selectors = [
                s["selector"] for s in title_selectors if s.get("active", True)
            ]

            price_selectors = self.selector_manager.get_selectors_by_type(
                store, "price"
            )
            price_selectors = [
                s["selector"] for s in price_selectors if s.get("active", True)
            ]

            old_price_selectors = self.selector_manager.get_selectors_by_type(
                store, "old_price"
            )
            old_price_selectors = [
                s["selector"] for s in old_price_selectors if s.get("active", True)
            ]

            link_selectors = self.selector_manager.get_selectors_by_type(store, "link")
            link_selectors = [
                s["selector"] for s in link_selectors if s.get("active", True)
            ]

            image_selectors = self.selector_manager.get_selectors_by_type(
                store, "image"
            )
            image_selectors = [
                s["selector"] for s in image_selectors if s.get("active", True)
            ]

            # Se não houver seletores específicos, usar seletores padrão
            if not title_selectors:
                if store == "mercadolivre":
                    title_selectors = [
                        "h2.ui-search-item__title",
                        "h2.promotion-item__title",
                        "span.ui-search-item__title",
                        "a.poly-component__title",
                    ]
                elif store == "magalu":
                    title_selectors = [
                        "h3.productTitle",
                        "h2.product-title",
                        "h3.nm-product-name",
                    ]
                elif store == "amazon":
                    title_selectors = [
                        "h2.a-size-mini",
                        "span.a-size-base-plus",
                        "span.a-size-medium",
                    ]

            # Extrair título
            title = None
            for selector in title_selectors:
                title_element = element.select_one(selector)
                if title_element:
                    title = title_element.get_text().strip()
                    break

            if not title:
                return None

            # Extrair link
            product_url = None
            for selector in link_selectors:
                link_element = element.select_one(selector)
                if link_element and link_element.has_attr("href"):
                    product_url = link_element["href"]
                    # Garantir URL completa
                    if product_url.startswith("/"):
                        if store == "mercadolivre":
                            product_url = (
                                f"https://www.mercadolivre.com.br{product_url}"
                            )
                        elif store == "magalu":
                            product_url = (
                                f"https://www.magazineluiza.com.br{product_url}"
                            )
                        elif store == "amazon":
                            product_url = f"https://www.amazon.com.br{product_url}"
                    break

            # Se não encontrou link específico, procurar qualquer link
            if not product_url:
                link_element = element.select_one("a")
                if link_element and link_element.has_attr("href"):
                    product_url = link_element["href"]
                    # Garantir URL completa
                    if product_url.startswith("/"):
                        if store == "mercadolivre":
                            product_url = (
                                f"https://www.mercadolivre.com.br{product_url}"
                            )
                        elif store == "magalu":
                            product_url = (
                                f"https://www.magazineluiza.com.br{product_url}"
                            )
                        elif store == "amazon":
                            product_url = f"https://www.amazon.com.br{product_url}"

            # Extrair preço atual
            price = None
            for selector in price_selectors:
                price_element = element.select_one(selector)
                if price_element:
                    price_text = price_element.get_text().strip()
                    # Limpar e converter para float
                    price_text = (
                        price_text.replace("R$", "")
                        .replace(".", "")
                        .replace(",", ".")
                        .strip()
                    )
                    try:
                        price = float(price_text)
                        break
                    except ValueError:
                        continue

            # Extrair preço antigo
            old_price = None
            for selector in old_price_selectors:
                old_price_element = element.select_one(selector)
                if old_price_element:
                    old_price_text = old_price_element.get_text().strip()
                    # Limpar e converter para float
                    old_price_text = (
                        old_price_text.replace("R$", "")
                        .replace(".", "")
                        .replace(",", ".")
                        .strip()
                    )
                    try:
                        old_price = float(old_price_text)
                        break
                    except ValueError:
                        continue

            # Extrair URL da imagem
            image_url = None

            # Primeiro, tenta encontrar imagens dentro do elemento .poly-card__portada
            portada_elements = element.select(".poly-card__portada")
            if portada_elements:
                for portada in portada_elements:
                    img = portada.find("img")
                    if img and img.has_attr("src"):
                        src = img["src"]
                        if (
                            src
                            and not src.startswith("data:")
                            and ("http2.mlstatic.com" in src or "D_NQ_NP" in src)
                        ):
                            image_url = src
                            log.info(
                                f"Imagem encontrada em .poly-card__portada: {image_url[:100]}"
                            )
                            break

            # Se não encontrou na portada, tenta com os seletores padrão
            if not image_url:
                for selector in image_selectors:
                    image_element = element.select_one(selector)
                    if image_element and image_element.has_attr("src"):
                        image_url = image_element["src"]
                        # Verificar se é uma URL válida e não um data:image
                        if image_url and not image_url.startswith("data:"):
                            # Verificar se é uma URL do Mercado Livre
                            if (
                                "http2.mlstatic.com" in image_url
                                or "D_NQ_NP" in image_url
                            ):
                                log.info(
                                    f"Imagem encontrada com seletor {selector}: {image_url[:100]}"
                                )
                                break
                    elif image_element and image_element.has_attr("data-src"):
                        image_url = image_element["data-src"]
                        # Verificar se é uma URL válida e não um data:image
                        if image_url and not image_url.startswith("data:"):
                            # Verificar se é uma URL do Mercado Livre
                            if (
                                "http2.mlstatic.com" in image_url
                                or "D_NQ_NP" in image_url
                            ):
                                log.info(
                                    f"Imagem encontrada com seletor {selector} (data-src): {image_url[:100]}"
                                )
                                break

            # Se não encontrou imagem específica, procurar qualquer imagem
            if not image_url or image_url.startswith("data:"):
                # Tentar encontrar qualquer imagem que seja do Mercado Livre
                all_images = element.select("img")
                for img in all_images:
                    src = img.get("src", "")
                    if (
                        src
                        and not src.startswith("data:")
                        and ("http2.mlstatic.com" in src or "D_NQ_NP" in src)
                    ):
                        image_url = src
                        log.info(
                            f"Imagem encontrada em busca geral (src): {image_url[:100]}"
                        )
                        break

                    # Tentar data-src se src não for válido
                    data_src = img.get("data-src", "")
                    if (
                        not image_url
                        and data_src
                        and not data_src.startswith("data:")
                        and ("http2.mlstatic.com" in data_src or "D_NQ_NP" in data_src)
                    ):
                        image_url = data_src
                        log.info(
                            f"Imagem encontrada em busca geral (data-src): {image_url[:100]}"
                        )
                        break

            # Melhorar a qualidade da imagem se possível
            if image_url:
                # Tenta melhorar a qualidade da imagem
                if "D_NQ_NP_" in image_url and not "2X_" in image_url:
                    parts = image_url.split("D_NQ_NP_")
                    if len(parts) == 2:
                        high_quality_url = f"{parts[0]}D_NQ_NP_2X_{parts[1]}"
                        log.info(
                            f"URL de imagem melhorada para 2X: {high_quality_url[:100]}"
                        )
                        image_url = high_quality_url

            # Extrair cupom/desconto
            coupon = None
            coupon_selectors = self.selector_manager.get_selectors_by_type(
                store, "coupon"
            )
            coupon_selectors = [
                s["selector"] for s in coupon_selectors if s.get("active", True)
            ]

            # Se não houver seletores específicos, usar seletores padrão
            if not coupon_selectors:
                if store == "mercadolivre":
                    coupon_selectors = [
                        ".poly-coupons__pill",
                        ".poly-coupons__icon",
                        "#poly_coupon",
                        ".andes-money-amount__discount",
                        ".poly-price__disc_label",
                        ".ui-pdp-price__discount-label",
                    ]

            # Tentar cada seletor até encontrar um cupom
            for selector in coupon_selectors:
                coupon_element = element.select_one(selector)
                if coupon_element:
                    coupon = coupon_element.get_text().strip()
                    if coupon:
                        break

            # Se não encontrou cupom, tentar calcular o desconto a partir dos preços
            if not coupon and price and old_price and old_price > price:
                discount_percent = int(((old_price - price) / old_price) * 100)
                if discount_percent > 0:
                    coupon = f"{discount_percent}% OFF"

            # Criar objeto do produto
            product = {
                "plataforma": store.capitalize(),
                "url": product_url or "",
                "url_afiliado": "",  # Deixar em branco conforme solicitado
                "url_imagem": image_url or "",
                "titulo": title,
                "categoria": "",  # Será preenchido posteriormente
                "subcategoria": "",  # Será preenchido posteriormente
                "descricao": "",  # Será preenchido posteriormente
                "preco_atual": price or 0.0,
                "preco_antigo": old_price or 0.0,
                "preco_alternativo": 0.0,
                "ativo": "True",
                "cupom": coupon or "",
                "menor_preco": "False",
                "indicamos": "False",
                "disparar_whatsapp": "False",
                "grupo_whatsapp": "",
                "frete": "False",
                "invalidProduct": "False",
                "isStory": "False",
                "_use_ai_categorization": True,  # Flag para indicar que deve usar IA para categorização
            }

            return product

        except Exception as e:
            log.error(f"Erro ao extrair dados do produto: {e}")
            return None

    # Método removido para evitar duplicação

    async def get_selectors(
        self,
        store: Optional[str] = None,
        selector_type: Optional[str] = None,
        status: Optional[str] = None,
    ) -> List[Dict[str, Any]]:
        """
        Obtém os seletores do PromoHunter.

        Args:
            store: Filtro opcional por loja
            selector_type: Filtro opcional por tipo de seletor
            status: Filtro opcional por status (ativos, inativos, todos)

        Returns:
            List[Dict[str, Any]]: Lista de seletores
        """
        selectors = []

        # Obter todos os seletores
        all_selectors = self.selector_manager.all_selectors_flat

        # Aplicar filtros
        for selector in all_selectors:
            # Filtrar por loja
            if store and selector.get("store") != store:
                continue

            # Filtrar por tipo
            if selector_type and selector.get("type") != selector_type:
                continue

            # Filtrar por status
            if status:
                is_active = selector.get("active", True)
                if status.lower() == "ativos" and not is_active:
                    continue
                elif status.lower() == "inativos" and is_active:
                    continue

            # Converter para o formato de saída
            store_id = selector.get("store")
            selectors.append(
                {
                    "id": selector.get("id"),
                    "store_id": store_id,
                    "store": self.selector_manager.get_store_name(store_id),
                    "type": selector.get("type"),
                    "selector": selector.get("selector"),
                    "description": selector.get("description", ""),
                    "active": selector.get("active", True),
                }
            )

        return selectors

    async def create_selector(self, selector_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Cria um novo seletor para o PromoHunter.

        Args:
            selector_data: Dados do seletor

        Returns:
            Dict[str, Any]: Seletor criado
        """
        store_id = selector_data.get("store_id")
        selector_type = selector_data.get("type")
        selector_text = selector_data.get("selector")
        description = selector_data.get("description", "")
        active = selector_data.get("active", True)

        # Adicionar o seletor
        new_selector = self.selector_manager.add_selector(
            store_id=store_id,
            selector_type=selector_type,
            selector_text=selector_text,
            description=description,
            active=active,
        )

        if not new_selector:
            raise ValueError(f"Falha ao adicionar seletor: {selector_text}")

        # Converter para o formato de saída
        return {
            "id": new_selector.get("id"),
            "store_id": new_selector.get("store_id"),
            "store": self.selector_manager.get_store_name(new_selector.get("store_id")),
            "type": new_selector.get("type"),
            "selector": new_selector.get("selector"),
            "description": new_selector.get("description", ""),
            "active": new_selector.get("active", True),
        }

    async def update_selector(
        self, selector_id: int, selector_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Atualiza um seletor existente do PromoHunter.

        Args:
            selector_id: ID do seletor
            selector_data: Novos dados do seletor

        Returns:
            Dict[str, Any]: Seletor atualizado
        """
        store_id = selector_data.get("store_id")
        selector_type = selector_data.get("type")
        selector_text = selector_data.get("selector")
        description = selector_data.get("description", "")
        active = selector_data.get("active", True)

        # Atualizar o seletor
        updated = self.selector_manager.update_selector(
            selector_id=selector_id,
            store_id=store_id,
            selector_type=selector_type,
            selector_text=selector_text,
            description=description,
            active=active,
        )

        if not updated:
            raise ValueError(
                f"Seletor com ID {selector_id} não encontrado ou falha ao atualizar"
            )

        # Buscar o seletor atualizado
        for selector in self.selector_manager.all_selectors_flat:
            if selector.get("id") == selector_id:
                # Converter para o formato de saída
                return {
                    "id": selector.get("id"),
                    "store_id": selector.get("store_id"),
                    "store": self.selector_manager.get_store_name(
                        selector.get("store_id")
                    ),
                    "type": selector.get("type"),
                    "selector": selector.get("selector"),
                    "description": selector.get("description", ""),
                    "active": selector.get("active", True),
                }

        raise ValueError(
            f"Seletor com ID {selector_id} não encontrado após atualização"
        )

    async def delete_selector(self, selector_id: int) -> None:
        """
        Remove um seletor do PromoHunter.

        Args:
            selector_id: ID do seletor
        """
        # Remover o seletor
        deleted = self.selector_manager.delete_selector(selector_id)

        if not deleted:
            raise ValueError(
                f"Seletor com ID {selector_id} não encontrado ou falha ao remover"
            )

    async def get_scraper_status(self) -> Dict[str, Any]:
        """
        Retorna o status atual do scraper.

        Returns:
            Dict[str, Any]: Status do scraper
        """
        with self._lock:
            # Calcular tempo decorrido se estiver em execução
            elapsed_time = ""
            if self._is_running and "start_time" in self.scraper_stats:
                start_time = datetime.fromisoformat(
                    self.scraper_stats.get("start_time", datetime.now().isoformat())
                )
                elapsed_seconds = (datetime.now() - start_time).total_seconds()
                elapsed_time = f" (em execução há {elapsed_seconds:.1f} segundos)"

            # Adicionar informação de tempo ao status
            status = (
                self.current_status + elapsed_time
                if self._is_running
                else self.current_status
            )

            return {
                "is_running": self._is_running,
                "is_completed": not self._is_running
                and self.current_status == "Finalizado com sucesso",
                "has_error": not self._is_running
                and self.current_status == "Finalizado com erro",
                "status": status,
                "message": self.current_status,
                "total_products": self.scraper_stats.get("total_products", 0),
                "processed_categories": self.scraper_stats.get(
                    "processed_categories", 0
                ),
                "processed_pages": self.scraper_stats.get("processed_pages", 0),
                "errors": self.scraper_stats.get("errors", []),
            }

    async def validate_selectors(
        self, store: str, url: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Valida os seletores do PromoHunter para uma loja específica.

        Args:
            store: Loja para validar os seletores
            url: URL opcional para testar os seletores

        Returns:
            Dict[str, Any]: Resultado da validação
        """
        # Verificar se a loja é válida
        if store not in ["mercadolivre", "magalu", "amazon"]:
            raise ValueError(f"Loja inválida: {store}")

        # Se não houver URL, usar uma URL padrão para a loja
        if not url:
            if store == "mercadolivre":
                url = "https://www.mercadolivre.com.br/ofertas"
            elif store == "magalu":
                url = "https://www.magazineluiza.com.br/ofertas/l/of/"
            elif store == "amazon":
                url = "https://www.amazon.com.br/deals"

        # Obter headers aleatórios
        headers = self.header_manager.get_random_header()

        try:
            # Fazer requisição HTTP
            response = requests.get(url, headers=headers, timeout=30)
            if response.status_code != 200:
                return {
                    "success": False,
                    "message": f"Erro ao acessar {url}: {response.status_code}",
                    "results": {},
                }

            soup = BeautifulSoup(response.text, "html.parser")

            # Validar seletores por tipo
            results = {}
            selector_types = ["product", "title", "price", "old_price", "link", "image"]

            for selector_type in selector_types:
                # Obter todos os seletores para este tipo
                all_selectors = self.selector_manager.get_selectors_by_type(
                    store, selector_type
                )
                # Obter apenas os seletores ativos para este tipo
                active_selectors = self.selector_manager.get_active_selectors(
                    store, selector_type
                )

                type_results = []

                # Primeiro, adicionar os seletores ativos
                for selector in active_selectors:
                    selector_text = selector.get("selector")
                    elements = soup.select(selector_text)

                    type_results.append(
                        {
                            "id": selector.get("id"),
                            "selector": selector_text,
                            "found": len(elements) > 0,
                            "count": len(elements),
                            "active": True,
                        }
                    )

                # Depois, adicionar os seletores inativos
                for selector in all_selectors:
                    # Pular os seletores ativos que já foram adicionados
                    if selector.get("active", False):
                        continue

                    selector_text = selector.get("selector")
                    elements = soup.select(selector_text)

                    type_results.append(
                        {
                            "id": selector.get("id"),
                            "selector": selector_text,
                            "found": len(elements) > 0,
                            "count": len(elements),
                            "active": False,
                        }
                    )

                results[selector_type] = type_results

            return {
                "success": True,
                "message": f"Validação concluída para {store}",
                "url": url,
                "results": results,
            }

        except Exception as e:
            log.error(f"Erro ao validar seletores para {store}: {e}")
            return {
                "success": False,
                "message": f"Erro ao validar seletores: {str(e)}",
                "results": {},
            }
