"""
Configurações do Scrapy para o PromoScraper
"""

BOT_NAME = "promoscraper"

SPIDER_MODULES = ["spiders"]
NEWSPIDER_MODULE = "spiders"

# Configurações de comportamento do crawler
USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"

# Respeitar robots.txt
ROBOTSTXT_OBEY = False

# Configurar número máximo de requisições simultâneas
CONCURRENT_REQUESTS = 8

# Configurar delay entre requisições para o mesmo domínio
DOWNLOAD_DELAY = 1.5
RANDOMIZE_DOWNLOAD_DELAY = True

# Desabilitar cookies (habilitado por padrão)
COOKIES_ENABLED = True

# Desabilitar Telnet Console (habilitado por padrão)
TELNETCONSOLE_ENABLED = False

# Configurações de cache HTTP
HTTPCACHE_ENABLED = True
HTTPCACHE_EXPIRATION_SECS = 86400  # 24 horas
HTTPCACHE_DIR = "httpcache"
HTTPCACHE_IGNORE_HTTP_CODES = [503, 504, 505, 500, 400, 401, 402, 403, 404]
HTTPCACHE_STORAGE = "scrapy.extensions.httpcache.FilesystemCacheStorage"

# Configurações de retry
RETRY_ENABLED = True
RETRY_TIMES = 3
RETRY_HTTP_CODES = [500, 502, 503, 504, 408, 429]

# Configurações de timeout
DOWNLOAD_TIMEOUT = 30

# Configurações para logging
LOG_LEVEL = 'INFO'
LOG_FORMAT = '%(asctime)s [%(name)s] [%(levelname)s] %(message)s'
LOG_DATEFORMAT = '%Y-%m-%d %H:%M:%S'

# Desabilitar log em arquivo
LOG_FILE = None
LOG_ENABLED = True
LOG_STDOUT = True

# Configuração dos spiders
SPIDER_MODULES = ['src.scrapers.spiders']
NEWSPIDER_MODULE = 'src.scrapers.spiders'

# Configuração do Scrapy
BOT_NAME = 'promoscraper'

# Middlewares
DOWNLOADER_MIDDLEWARES = {
    # Rotação de User Agents
    'src.scrapers.middlewares.RandomUserAgentMiddleware': 400,
    # Middleware para lidar com delays adaptativos
    'src.scrapers.middlewares.AdaptiveDelayMiddleware': 550,
    # Middleware para detecção de captcha
    'src.scrapers.middlewares.CaptchaDetectionMiddleware': 600,
    # Desabilitar o middleware padrão de User-Agent do Scrapy
    'scrapy.downloadermiddlewares.useragent.UserAgentMiddleware': None,
    # Middleware de retry
    'scrapy.downloadermiddlewares.retry.RetryMiddleware': 500,
}

# Item Pipelines
ITEM_PIPELINES = {
    'src.scrapers.pipelines.ProductCleaningPipeline': 300,
    'src.scrapers.pipelines.ImageProcessingPipeline': 400,
}

# Lista de User Agents modernos
USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:124.0) Gecko/20100101 Firefox/124.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
]

# Configurações específicas para o Mercado Livre
MERCADOLIVRE_SETTINGS = {
    'product_selectors': [
        '.andes-card.poly-card--grid-card',
        '.ui-search-layout__item',
        '.ui-search-result',
    ],
    'title_selectors': [
        'a.poly-component__title::text',
        'h2.ui-search-item__title::text',
        'h1.ui-pdp-title::text',
    ],
    'price_selectors': [
        '.poly-price__current .andes-money-amount__fraction::text',
        '.ui-search-price__part .andes-money-amount__fraction::text',
        '.ui-pdp-price__second-line .andes-money-amount__fraction::text',
    ],
    'old_price_selectors': [
        '.andes-money-amount--previous .andes-money-amount__fraction::text',
        '.ui-search-price__second-line .andes-money-amount__fraction::text',
        '.ui-pdp-price__original-value .andes-money-amount__fraction::text',
    ],
    'image_selectors': [
        '.poly-card__portada img::attr(src)',
        'img.poly-component__picture::attr(src)',
        '.ui-pdp-gallery__figure img::attr(src)',
        'img[data-src]::attr(data-src)',
    ],
    'link_selectors': [
        'a.poly-component__title::attr(href)',
        '.ui-search-result__image a::attr(href)',
        'link[rel="canonical"]::attr(href)',
    ],
    'coupon_selectors': [
        '.poly-coupons__pill::text',
        '.poly-coupons__icon::text',
        '#poly_coupon::text',
    ],
}

# Configurações específicas para o Magalu
MAGALU_SETTINGS = {
    'title_selectors': [
        'h1[data-testid="heading-product-title"]::text',
        'h1.sc-kpDqfm::text',
    ],
    'price_selectors': [
        '[data-testid="price-value"]::text',
        '.price-template__text::text',
        'span.sc-kpDqfm.price-info__Price-sc-__sc-33au7i-0::text',
    ],
    'old_price_selectors': [
        '.price-template__old-value::text',
        'span.sc-kpDqfm.price-info__PriceInfo-sc-__sc-33au7i-1::text',
    ],
    'image_selectors': [
        '[data-testid="image-selected-thumbnail"]::attr(src)',
        '.showcase-product__big-img::attr(src)',
        'img.sc-dBmzty::attr(src)',
        'img[src*="i.mlcdn.com.br"]::attr(src)',
    ],
}

# Configurações específicas para a Amazon
AMAZON_SETTINGS = {
    'title_selectors': [
        '#productTitle::text',
        '#title::text',
        '.product-title-word-break::text',
    ],
    'price_selectors': [
        '.a-price .a-offscreen::text',
        '#priceblock_ourprice::text',
        '#priceblock_dealprice::text',
        '.a-price-whole::text',
    ],
    'old_price_selectors': [
        '.a-text-price .a-offscreen::text',
        '#priceblock_listprice::text',
        '.a-text-price::text',
    ],
    'image_selectors': [
        '#landingImage::attr(src)',
        '#imgBlkFront::attr(src)',
        '#main-image::attr(src)',
        '.a-dynamic-image::attr(src)',
    ],
}
