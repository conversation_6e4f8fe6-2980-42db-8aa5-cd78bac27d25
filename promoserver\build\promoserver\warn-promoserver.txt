
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named pyimod02_importers - imported by C:\TRABALHO\PROMOBELL\promoserver\.venv\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed), C:\TRABALHO\PROMOBELL\promoserver\.venv\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed)
missing module named urllib.urlopen - imported by url<PERSON>b (delayed, optional), lxml.html (delayed, optional)
missing module named urllib.urlencode - imported by urllib (delayed, optional), lxml.html (delayed, optional)
missing module named posix - imported by os (conditional, optional), shutil (conditional), importlib._bootstrap_external (conditional), posixpath (optional)
missing module named resource - imported by posix (top-level), twisted.internet.process (delayed, optional)
missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), psutil (optional), netrc (delayed, conditional), getpass (delayed), http.server (delayed, optional), setuptools._distutils.util (delayed, conditional, optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.archive_util (optional), _pytest._py.path (delayed), twisted.python.util (optional), twisted.protocols.ftp (optional)
missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.archive_util (optional), _pytest._py.path (delayed), twisted.python.util (optional), twisted.protocols.ftp (optional)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named fcntl - imported by subprocess (optional), xmlrpc.server (optional), twisted.python.compat (delayed, optional), twisted.internet.fdesc (optional), pty (delayed, optional), twisted.internet.process (optional), filelock._unix (conditional, optional)
missing module named termios - imported by getpass (optional), tty (top-level), click._termui_impl (conditional), twisted.internet.process (optional)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), setuptools._vendor.wheel.vendored.packaging._manylinux (delayed, optional)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named '_typeshed.importlib' - imported by pkg_resources (conditional)
missing module named _typeshed - imported by pydantic_core._pydantic_core (top-level), pydantic._internal._dataclasses (conditional), anyio.abc._eventloop (conditional), anyio._core._sockets (conditional), anyio._core._fileio (conditional), anyio._core._tempfile (conditional), setuptools._distutils.dist (conditional), pkg_resources (conditional), setuptools.glob (conditional), setuptools.compat.py311 (conditional), trio._file_io (conditional), trio._path (conditional), httpx._transports.wsgi (conditional), scrapy.settings (conditional), anyio._backends._asyncio (conditional), anyio._core._asyncio_selector_thread (conditional), anyio._backends._trio (conditional), scrapy.extensions.feedexport (conditional)
missing module named jnius - imported by setuptools._vendor.platformdirs.android (delayed, conditional, optional)
missing module named android - imported by setuptools._vendor.platformdirs.android (delayed, conditional, optional)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named readline - imported by site (delayed, optional), rlcompleter (optional), cmd (delayed, conditional, optional), code (delayed, conditional, optional), pdb (delayed, optional), _pytest.capture (delayed, optional), pstats (conditional, optional), websockets.__main__ (optional), scrapy.utils.console (delayed, optional)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named importlib_resources - imported by setuptools._vendor.jaraco.text (optional)
missing module named htmlentitydefs - imported by lxml.html.soupparser (optional)
missing module named BeautifulSoup - imported by lxml.html.soupparser (optional)
missing module named urlparse - imported by lxml.ElementInclude (optional), lxml.html.html5parser (optional)
missing module named urllib2 - imported by lxml.ElementInclude (optional), lxml.html.html5parser (optional)
missing module named 'html5lib.treebuilders' - imported by bs4.builder._html5lib (top-level), lxml.html._html5builder (top-level), lxml.html.html5parser (top-level)
missing module named html5lib - imported by bs4.builder._html5lib (top-level), lxml.html.html5parser (top-level)
missing module named lxml_html_clean - imported by lxml.html.clean (optional)
missing module named annotationlib - imported by attr._compat (conditional)
missing module named collections.Mapping - imported by collections (optional), sortedcontainers.sorteddict (optional), hyperlink._url (optional), google.auth.jwt (optional), google.auth.pluggable (optional), google.auth.identity_pool (optional)
missing module named cryptography.x509.UnsupportedExtension - imported by cryptography.x509 (optional), urllib3.contrib.pyopenssl (optional)
missing module named priority - imported by twisted.web._http2 (top-level)
missing module named 'google.cloud' - imported by scrapy.utils.test (delayed), scrapy.extensions.feedexport (delayed), scrapy.pipelines.files (delayed)
missing module named _curses - imported by curses (top-level), curses.has_key (top-level)
missing module named subunit - imported by twisted.trial.reporter (optional)
missing module named cython - imported by pydantic.v1.version (optional)
missing module named email_validator - imported by pydantic.networks (delayed, conditional, optional), pydantic.v1.networks (delayed, conditional, optional), fastapi.openapi.models (optional), pydantic.v1._hypothesis_plugin (optional)
missing module named 'IPython.core' - imported by dotenv.ipython (top-level), google.genai.tunings (delayed)
missing module named IPython - imported by dotenv.ipython (top-level), google.genai.types (delayed, optional), google.genai.tunings (delayed, conditional)
missing module named toml - imported by pydantic.v1.mypy (delayed, conditional, optional)
missing module named 'mypy.version' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.util' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.typevars' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.types' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.server' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.semanal' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.plugins' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.plugin' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.options' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.nodes' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.errorcodes' - imported by pydantic.v1.mypy (top-level)
missing module named 'hypothesis.strategies' - imported by pydantic.v1._hypothesis_plugin (top-level)
missing module named 'mypy.typeops' - imported by pydantic.mypy (top-level)
missing module named 'mypy.type_visitor' - imported by pydantic.mypy (top-level)
missing module named 'mypy.state' - imported by pydantic.mypy (top-level)
missing module named 'mypy.expandtype' - imported by pydantic.mypy (top-level)
missing module named mypy - imported by pydantic.mypy (top-level)
missing module named eval_type_backport - imported by pydantic._internal._typing_extra (delayed, optional)
missing module named 'rich.pretty' - imported by pydantic._internal._core_utils (delayed)
missing module named rich - imported by pydantic._internal._core_utils (conditional)
missing module named pydantic.PydanticUserError - imported by pydantic (top-level), pydantic.root_model (top-level)
missing module named pydantic.PydanticSchemaGenerationError - imported by pydantic (delayed), pydantic.functional_validators (delayed, conditional), fastapi._compat (conditional)
missing module named pydantic.BaseModel - imported by pydantic (conditional), pydantic._internal._typing_extra (conditional), pydantic._internal._import_utils (delayed, conditional), pydantic._internal._core_utils (delayed), pydantic.deprecated.copy_internals (delayed, conditional), fastapi.exceptions (top-level), fastapi.types (top-level), fastapi._compat (top-level), fastapi.openapi.models (top-level), fastapi.security.http (top-level), fastapi.utils (top-level), fastapi.dependencies.utils (top-level), fastapi.encoders (top-level), fastapi.routing (top-level), fastapi.openapi.utils (top-level), gotrue.helpers (top-level), gotrue.types (top-level), gotrue._async.gotrue_base_api (top-level), gotrue._sync.gotrue_base_api (top-level), postgrest.base_request_builder (top-level), components.response_models (top-level), components.models (top-level), google.genai._api_client (top-level), src.services.product_db_service (delayed, conditional, optional)
missing module named 'pygments.lexers' - imported by _pytest._io.terminalwriter (delayed, conditional, optional), httpx._main (top-level), scrapy.utils.display (delayed)
missing module named 'pygments.formatters' - imported by _pytest._io.terminalwriter (delayed, optional), scrapy.utils.display (delayed)
missing module named pygments - imported by _pytest._io.terminalwriter (delayed, conditional), scrapy.utils.display (delayed, optional)
missing module named ptpython - imported by scrapy.utils.console (delayed)
missing module named bpython - imported by scrapy.utils.console (delayed)
missing module named 'IPython.frontend' - imported by scrapy.utils.console (delayed, optional)
missing module named 'IPython.terminal' - imported by scrapy.utils.console (delayed, optional)
missing module named botocore - imported by scrapy.utils.boto (delayed, optional)
missing module named zstandard.backend_rust - imported by zstandard (conditional)
missing module named brotlicffi - imported by httpx._decoders (optional), scrapy.utils._compression (optional), urllib3.util.request (optional), urllib3.response (optional), aiohttp.compression_utils (optional), scrapy.downloadermiddlewares.httpcompression (optional)
missing module named brotli - imported by httpx._decoders (optional), scrapy.utils._compression (optional), urllib3.util.request (optional), urllib3.response (optional), aiohttp.compression_utils (optional), scrapy.downloadermiddlewares.httpcompression (optional)
missing module named robotexclusionrulesparser - imported by scrapy.robotstxt (delayed)
missing module named 'botocore.session' - imported by scrapy.pipelines.files (delayed)
missing module named xmlrpclib - imported by defusedxml.xmlrpc (conditional)
missing module named boto3 - imported by scrapy.extensions.feedexport (delayed, optional)
missing module named StringIO - imported by six (conditional), requests_file (optional)
missing module named simplejson - imported by requests.compat (conditional, optional)
missing module named 'pyodide.ffi' - imported by urllib3.contrib.emscripten.fetch (delayed, optional)
missing module named pyodide - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named js - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named win_inet_pton - imported by socks (conditional, optional)
missing module named collections.Callable - imported by collections (optional), cffi.api (optional), socks (optional)
missing module named dummy_threading - imported by requests.cookies (optional)
missing module named chardet - imported by requests (optional), bs4.dammit (optional)
missing module named 'botocore.awsrequest' - imported by scrapy.core.downloader.handlers.s3 (delayed, conditional)
missing module named 'botocore.credentials' - imported by scrapy.core.downloader.handlers.s3 (delayed)
missing module named 'botocore.auth' - imported by scrapy.core.downloader.handlers.s3 (delayed)
missing module named google.genai.replay_api_client - imported by google.genai.errors (conditional)
missing module named numpy - imported by PIL._typing (conditional, optional), _pytest.python_api (delayed, conditional)
missing module named 'setuptools._distutils.msvc9compiler' - imported by cffi._shimmed_dist_utils (conditional, optional)
missing module named imp - imported by cffi.verifier (conditional), cffi._imp_emulation (optional)
missing module named _dummy_thread - imported by sortedcontainers.sortedlist (conditional, optional), cffi.lock (conditional, optional)
missing module named dummy_thread - imported by sortedcontainers.sortedlist (conditional, optional), cffi.lock (conditional, optional)
missing module named thread - imported by sortedcontainers.sortedlist (conditional, optional), cffi.lock (conditional, optional), cffi.cparser (conditional, optional)
missing module named cStringIO - imported by cffi.ffiplatform (optional)
missing module named cPickle - imported by pycparser.ply.yacc (delayed, optional)
missing module named cffi._pycparser - imported by cffi (optional), cffi.cparser (optional)
missing module named 'pyu2f.model' - imported by google.oauth2.challenges (delayed, optional)
missing module named 'pyu2f.errors' - imported by google.oauth2.challenges (delayed, optional)
missing module named pyu2f - imported by google.oauth2.challenges (delayed, optional)
missing module named 'requests.packages.urllib3' - imported by google.auth.transport.requests (top-level)
missing module named 'google.appengine' - imported by google.auth.app_engine (optional)
missing module named 'hypothesis.internal' - imported by trio._core._run (delayed, optional)
missing module named hypothesis - imported by trio._core._run (delayed)
missing module named collections.Sequence - imported by collections (optional), sortedcontainers.sortedlist (optional), sortedcontainers.sortedset (optional), sortedcontainers.sorteddict (optional)
missing module named collections.ValuesView - imported by collections (optional), sortedcontainers.sorteddict (optional)
missing module named collections.KeysView - imported by collections (optional), sortedcontainers.sorteddict (optional)
missing module named collections.ItemsView - imported by collections (optional), sortedcontainers.sorteddict (optional)
missing module named collections.Set - imported by collections (optional), sortedcontainers.sortedset (optional)
missing module named collections.MutableSet - imported by collections (optional), sortedcontainers.sortedset (optional)
missing module named collections.MutableSequence - imported by collections (optional), sortedcontainers.sortedlist (optional)
missing module named curio - imported by sniffio._impl (delayed, conditional)
missing module named 'pygments.util' - imported by _pytest._io.terminalwriter (delayed, optional), httpx._main (top-level)
missing module named 'pygments.lexer' - imported by _pytest._io.terminalwriter (conditional)
missing module named argcomplete - imported by _pytest._argcomplete (conditional, optional)
missing module named pexpect - imported by _pytest.pytester (conditional), _pytest.legacypath (conditional)
missing module named exceptiongroup - imported by anyio._core._exceptions (conditional), anyio._core._sockets (conditional), starlette._utils (conditional, optional), trio._util (conditional), trio._core._run (conditional), trio.testing._check_streams (conditional), _pytest.runner (conditional), _pytest.fixtures (conditional), _pytest._code.code (conditional), trio.testing._raises_group (conditional), trio._channel (conditional), trio._highlevel_open_tcp_listeners (conditional), trio._highlevel_open_tcp_stream (conditional), anyio._backends._asyncio (conditional), anyio._backends._trio (conditional), _pytest.unittest (conditional)
missing module named uvloop - imported by aiohttp.worker (delayed), anyio._backends._asyncio (delayed, conditional)
missing module named 'IPython.display' - imported by google.genai.tunings (delayed)
missing module named socksio - imported by httpcore._sync.socks_proxy (top-level), httpcore._async.socks_proxy (top-level), httpx._transports.default (delayed, conditional, optional)
missing module named 'rich.table' - imported by httpx._main (top-level)
missing module named 'rich.syntax' - imported by httpx._main (top-level)
missing module named 'rich.progress' - imported by httpx._main (top-level)
missing module named 'rich.markup' - imported by httpx._main (top-level)
missing module named 'rich.console' - imported by httpx._main (top-level)
missing module named '_typeshed.wsgi' - imported by httpx._transports.wsgi (conditional)
missing module named cchardet - imported by bs4.dammit (optional)
missing module named 'html5lib.constants' - imported by bs4.builder._html5lib (top-level)
missing module named orjson - imported by fastapi.responses (optional)
missing module named ujson - imported by fastapi.responses (optional)
missing module named wsaccel - imported by websocket._utils (optional)
missing module named 'python_socks.sync' - imported by websocket._http (optional)
missing module named 'python_socks._types' - imported by websocket._http (optional)
missing module named python_socks - imported by websocket._http (optional)
missing module named 'wsaccel.xormask' - imported by websocket._abnf (optional)
missing module named _winreg - imported by platform (delayed, optional), selenium.webdriver.firefox.firefox_binary (delayed, optional)
missing module named 'src.scrapers.scrapy_implementation.scrapy_service' - imported by src.services.promohunter_service_scrapy (delayed, optional)
missing module named async_timeout - imported by aiohttp.helpers (conditional), aiohttp.web_ws (conditional), aiohttp.client_ws (conditional)
missing module named 'gunicorn.workers' - imported by aiohttp.worker (top-level)
missing module named gunicorn - imported by aiohttp.worker (top-level)
missing module named aiodns - imported by aiohttp.resolver (optional)
missing module named six.moves.range - imported by six.moves (top-level), dateutil.rrule (top-level)
runtime module named six.moves - imported by pystray._base (top-level), pystray._win32 (top-level), pystray._xorg (top-level), dateutil.tz.tz (top-level), dateutil.tz._factories (top-level), dateutil.tz.win (top-level), dateutil.rrule (top-level)
missing module named dateutil.tz.tzfile - imported by dateutil.tz (top-level), dateutil.zoneinfo (top-level)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
missing module named PIL._avif - imported by PIL (optional), PIL.AvifImagePlugin (optional)
missing module named 'gi.repository' - imported by pystray._appindicator (top-level), pystray._util.gtk (top-level), pystray._util.notify_dbus (top-level), pystray._gtk (top-level)
missing module named gi - imported by pystray._appindicator (top-level), pystray._util.gtk (top-level), pystray._util.notify_dbus (top-level), pystray._gtk (top-level)
missing module named 'Xlib.XK' - imported by pystray._xorg (top-level)
missing module named 'Xlib.threaded' - imported by pystray._xorg (top-level)
missing module named Xlib - imported by pystray._xorg (top-level)
missing module named PyObjCTools - imported by pystray._darwin (top-level)
missing module named objc - imported by pystray._darwin (top-level)
missing module named Foundation - imported by pystray._darwin (top-level)
missing module named AppKit - imported by pystray._darwin (top-level)
