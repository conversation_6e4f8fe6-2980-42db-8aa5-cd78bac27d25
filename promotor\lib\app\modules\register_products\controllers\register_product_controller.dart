import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:intl/intl.dart';

import '../../../components/log_console.dart';
import '../../../models/category_model.dart';
import '../../../models/scraped_model.dart';
import '../../../services/category_service.dart';
import '../../../services/product_service.dart';
import '../../../services/scraping_service.dart';

class RegisterProductController extends ChangeNotifier {
  final ScrapingService _scrapingService;
  final ProductService _productService;
  final CategoryService _categoryService;

  final TextEditingController _urlController =
      TextEditingController();
  final TextEditingController _titleController =
      TextEditingController();
  final TextEditingController _descriptionController =
      TextEditingController();
  final TextEditingController _priceController =
      TextEditingController();
  final TextEditingController _oldPriceController =
      TextEditingController();
  final TextEditingController _couponController =
      TextEditingController();

  TextEditingController get urlController => _urlController;
  TextEditingController get titleController => _titleController;
  TextEditingController get descriptionController =>
      _descriptionController;
  TextEditingController get priceController => _priceController;
  TextEditingController get oldPriceController => _oldPriceController;
  TextEditingController get couponController => _couponController;

  final NumberFormat _currencyFormatter = NumberFormat.currency(
    locale: 'pt_BR',
    symbol: 'R\$',
  );
  NumberFormat get currencyFormatter => _currencyFormatter;

  List<CategoryModel> categories = [];
  String? error;
  bool isLoading = false;

  String _url = '';
  String get url => _url;

  // void setUrl(String value) {
  //   _url = value.trim();
  //   _urlController.text = _url;
  //   notifyListeners();
  // }

  bool _isFetching = false;
  bool get isFetching => _isFetching;

  String? _fetchError;
  String? get fetchError => _fetchError;

  ScrapedProduct? _scrapedProduct;
  ScrapedProduct? get scrapedProduct => _scrapedProduct;

  CategoryModel? _selectedCategory;
  CategoryModel? get selectedCategory => _selectedCategory;

  List<String> _currentSubcategories = [];
  List<String> get currentSubcategories => _currentSubcategories;

  String? _selectedSubcategory;
  String? get selectedSubcategory => _selectedSubcategory;

  bool _bestPrice = false;
  bool get bestPrice => _bestPrice;
  bool _recommended = false;
  bool get recommended => _recommended;
  bool _hasShipping = false;
  bool get hasShipping => _hasShipping;
  bool _dispatchWhatsapp = false;
  bool get dispatchWhatsapp => _dispatchWhatsapp;
  bool _isStory = false;
  bool get isStory => _isStory;
  String? _selectedWhatsappGroup;
  String? get selectedWhatsappGroup => _selectedWhatsappGroup;
  List<String> get availableWhatsappGroups => ['Enviar mensagem 01'];

  // Propriedades para gerenciar imagem local
  String? _localImageUrl;
  bool _isUsingLocalImage = false;
  String? get localImageUrl => _localImageUrl;
  bool get isUsingLocalImage => _isUsingLocalImage;

  // Getter para obter a URL da imagem atual (local ou do scraper)
  String? get currentImageUrl =>
      _isUsingLocalImage ? _localImageUrl : _scrapedProduct?.imageUrl;

  bool _isPublishing = false;
  bool get isPublishing => _isPublishing;
  String? _publishError;
  String? get publishError => _publishError;

  RegisterProductController(
    this._scrapingService,
    this._productService,
    this._categoryService,
  ) {
    globalLogger.i(
      "Controlador de Registro de Produtos Inicializado",
    );

    Future.delayed(Duration(milliseconds: 100), () {
      _loadCategories();
    });
    _urlController.addListener(() {
      _url = _urlController.text.trim();
      globalLogger.d(
        "URL Controller Listener: _urlController.text='${_urlController.text}', _url='$_url'",
      );
      notifyListeners();
    });
  }

  Future<void> _loadCategories() async {
    try {
      isLoading = true;
      notifyListeners();

      categories = await _categoryService.fetchCategories();
      error = null;
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        globalLogger.w(
          'Tentando novamente após erro de autenticação...',
        );

        await Future.delayed(Duration(milliseconds: 500));
        try {
          categories = await _categoryService.fetchCategories();
          error = null;
          return;
        } catch (retryError) {
          error =
              'Autenticação necessária: Por favor, faça login para continuar';
          Modular.to.navigate('/login/');
        }
      } else {
        error =
            'Falha ao carregar categorias. Por favor, tente novamente.';
        globalLogger.e('Erro ao carregar categorias: $e');
      }
    } catch (e) {
      error = 'Ocorreu um erro inesperado';
      globalLogger.e('Erro ao carregar categorias: $e');
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }

  Future<void> refreshCategories() async {
    await _loadCategories();
  }

  void setUrl(String value) {
    String processedUrl = value.trim();

    if (processedUrl.isNotEmpty &&
        !processedUrl.startsWith('http://') &&
        !processedUrl.startsWith('https://')) {
      processedUrl = 'https://$processedUrl';
    }

    // Limpa o formulário se a URL efetivamente mudou (após processamento)
    // E havia um produto raspado anteriormente com uma URL base diferente.
    if (_scrapedProduct != null &&
        processedUrl != _scrapedProduct!.affiliateUrl &&
        processedUrl != _url) {
      clearForm(
        notify: false,
      ); // notify: false porque notifyListeners() será chamado no final.
    }

    _url = processedUrl; // Define a _url interna do controller

    // Atualiza o TextEditingController somente se o texto processado for diferente do que já está lá.
    if (_urlController.text != _url) {
      // Mantém a posição do cursor ou move para o final.
      final currentSelection = _urlController.selection;
      _urlController.text = _url;
      if (currentSelection.start > _url.length ||
          currentSelection.end > _url.length) {
        _urlController.selection = TextSelection.collapsed(
          offset: _url.length,
        );
      } else {
        // Tenta manter a seleção se ainda for válida, senão move para o fim.
        _urlController.selection = currentSelection;
      }
    }
    notifyListeners(); // Notifica para atualizar a UI com base na nova _url e outros estados.
  }

  Future<void> fetchProductDetails() async {
    // setUrl() é chamado antes desta função pelos manipuladores de evento em UrlInputSection.
    // Portanto, _url já está processada.
    globalLogger.d("fetchProductDetails: URL recebida: '$_url'");
    if (_url.isEmpty) {
      _fetchError = "Por favor, insira uma URL válida";
      _isFetching = false; // Não está buscando se a URL é inválida.
      notifyListeners();
      return;
    }

    // Se a URL não é vazia, prossiga com a busca.
    _isFetching = true;
    _fetchError = null; // Limpa qualquer erro de fetch anterior.
    _scrapedProduct =
        null; // Limpa dados do produto raspado anterior antes de nova busca.
    notifyListeners(); // Atualiza a UI para mostrar o loading e limpar o erro.

    try {
      globalLogger.i("Buscando detalhes do produto para URL: $_url");
      final result = await _scrapingService.scrapeProduct(_url);

      if (result.error == true) {
        _fetchError =
            result.description ?? 'Falha ao buscar dados do produto.';
        // _scrapedProduct já foi definido como null acima.
        globalLogger.w("Falha na busca para $_url: $_fetchError");
      } else {
        _scrapedProduct =
            result; // Define o produto raspado em caso de sucesso.
        _updateStateFromScrapedData(result);
        _fetchError =
            null; // Garante que não há erro de fetch em caso de sucesso.
        globalLogger.i("Busca realizada com sucesso para $_url");
      }
    } catch (e) {
      globalLogger.e("Erro ao buscar detalhes do produto: $e");
      _fetchError = "Erro ao buscar detalhes: $e";
      // _scrapedProduct já foi definido como null acima.
    } finally {
      _isFetching = false;
      notifyListeners(); // Remove o loading e mostra qualquer _fetchError resultante.
    }
  }

  void _updateStateFromScrapedData(ScrapedProduct data) {
    // Log detalhado dos dados recebidos para debug

    _hasShipping =
        data.shipping?.toLowerCase().contains('com frete') ?? false;

    // Atualizar os controllers com os dados do produto
    _titleController.text = data.title;
    _descriptionController.text = data.description ?? '';

    // Preencher os campos de preço com os valores formatados
    if (data.price != null) {
      _priceController.text = _currencyFormatter.format(data.price);
      globalLogger.d("Preço formatado: ${_priceController.text}");
    }

    if (data.oldPrice != null) {
      _oldPriceController.text = _currencyFormatter.format(
        data.oldPrice,
      );
      globalLogger.d(
        "Preço antigo formatado: ${_oldPriceController.text}",
      );
    }

    // Atualizar o campo de cupom
    _couponController.text = data.couponInfo ?? '';

    // Atualizar categoria e subcategoria
    if (data.category != null) {
      // Encontra a categoria pelo nome
      _selectedCategory = categories.firstWhere(
        (cat) =>
            cat.name.toLowerCase() == data.category!.toLowerCase(),
        orElse: () => CategoryModel.empty(),
      );

      if (_selectedCategory != null &&
          _selectedCategory!.key.isNotEmpty) {
        _currentSubcategories = _selectedCategory!.subcategories;

        // Se tiver subcategoria, seleciona ela
        if (data.subcategory != null) {
          _selectedSubcategory = _currentSubcategories.firstWhere(
            (sub) =>
                sub.toLowerCase() == data.subcategory!.toLowerCase(),
            orElse: () => '',
          );
        }
      }
    }

    notifyListeners();
  }

  void selectCategory(CategoryModel? category) {
    _selectedCategory = category;
    _currentSubcategories = category?.subcategories ?? [];
    if (_currentSubcategories.isEmpty ||
        (_selectedSubcategory != null &&
            !_currentSubcategories.contains(_selectedSubcategory))) {
      _selectedSubcategory = null;
    }
    notifyListeners();
  }

  void selectSubcategory(String? subcategory) {
    _selectedSubcategory = subcategory;
    notifyListeners();
  }

  void setBestPrice(bool value) {
    _bestPrice = value;
    notifyListeners();
  }

  void setRecommended(bool value) {
    _recommended = value;
    notifyListeners();
  }

  void setIsStory(bool value) {
    _isStory = value;
    notifyListeners();
  }

  void setHasShipping(bool value) {
    _hasShipping = value;
    notifyListeners();
  }

  void setDispatchWhatsapp(bool value) {
    _dispatchWhatsapp = value;
    if (!value) {
      _selectedWhatsappGroup = null;
    }
    notifyListeners();
  }

  void setSelectedWhatsappGroup(String? value) {
    _selectedWhatsappGroup = value;
    notifyListeners();
  }

  // Métodos para gerenciar a imagem local
  void setLocalImage(String imageUrl) {
    _localImageUrl = imageUrl;
    _isUsingLocalImage = true;
    notifyListeners();
    globalLogger.i("Imagem local definida: $imageUrl");
  }

  void clearLocalImage() {
    _localImageUrl = null;
    _isUsingLocalImage = false;
    notifyListeners();
    globalLogger.i("Imagem local removida");
  }

  void clearForm({bool notify = true}) {
    _url = '';
    _scrapedProduct = null;
    _fetchError = null;
    _selectedCategory = null;
    _currentSubcategories = [];
    _selectedSubcategory = null;
    _bestPrice = false;
    _recommended = false;
    _hasShipping = false;
    _isStory = false;
    _dispatchWhatsapp = false;
    _selectedWhatsappGroup = null;
    _publishError = null;
    _localImageUrl = null;
    _isUsingLocalImage = false;

    // Limpar todos os controllers
    _urlController.clear();
    _titleController.clear();
    _descriptionController.clear();
    _priceController.clear();
    _oldPriceController.clear();
    _couponController.clear();

    if (notify) {
      notifyListeners();
    }
  }

  Future<bool> publishProduct({
    required String currentTitle,
    required String currentDescription,
    required String currentPriceStr,
    required String oldPriceStr,
    required String coupon,
  }) async {
    if (_scrapedProduct == null || _isPublishing) return false;
    if (_dispatchWhatsapp && _selectedWhatsappGroup == null) {
      _publishError = "Selecione um grupo do WhatsApp para disparo.";
      notifyListeners();
      return false;
    }

    _isPublishing = true;
    _publishError = null;
    notifyListeners();
    globalLogger.i("Publicando produto: $currentTitle");

    try {
      final format = NumberFormat.currency(
        locale: 'pt_BR',
        symbol: 'R\$',
      );
      double currentPrice = 0.0;
      double oldPrice = 0.0;
      try {
        currentPrice = format.parse(currentPriceStr).toDouble();
        globalLogger.i(
          "Preço atual convertido: $currentPriceStr -> $currentPrice",
        );
      } catch (e) {
        globalLogger.w(
          "Falha ao converter preço atual '$currentPriceStr': $e",
        );
        try {
          final cleanStr = currentPriceStr
              .replaceAll('R\$', '')
              .trim()
              .replaceAll('.', '')
              .replaceAll(',', '.');
          currentPrice = double.parse(cleanStr);
          globalLogger.i(
            "Preço atual convertido (método alternativo): $currentPriceStr -> $currentPrice",
          );
        } catch (e2) {
          globalLogger.e("Falha total ao converter preço atual: $e2");
        }
      }

      try {
        oldPrice = format.parse(oldPriceStr).toDouble();
        globalLogger.i(
          "Preço antigo convertido: $oldPriceStr -> $oldPrice",
        );
      } catch (e) {
        globalLogger.w(
          "Falha ao converter preço antigo '$oldPriceStr': $e",
        );
        try {
          final cleanStr = oldPriceStr
              .replaceAll('R\$', '')
              .trim()
              .replaceAll('.', '')
              .replaceAll(',', '.');
          oldPrice = double.parse(cleanStr);
          globalLogger.i(
            "Preço antigo convertido (método alternativo): $oldPriceStr -> $oldPrice",
          );
        } catch (e2) {
          globalLogger.e(
            "Falha total ao converter preço antigo: $e2",
          );
        }
      }

      final productData = {
        "plataforma": _scrapedProduct!.platform,
        "url_afiliado": _url,
        "url_produto": _scrapedProduct!.productUrl,
        "url_imagem":
            _isUsingLocalImage
                ? _localImageUrl
                : _scrapedProduct!.imageUrl ?? '',
        "titulo": currentTitle,
        "categoria": _selectedCategory?.name,
        "subcategoria": _selectedSubcategory,
        "descricao": currentDescription,
        "preco_atual": currentPrice,
        "preco_antigo": oldPrice,
        "cupom": coupon.isNotEmpty ? coupon : null,
        "menor_preco": _bestPrice,
        "indicamos": _recommended,
        "frete": _hasShipping,
        "grupo_whatsapp": _selectedWhatsappGroup,
        "disparar_whatsapp": _dispatchWhatsapp,
        "ativo": true,
        "preco_alternativo": 0.0,
        "isStory": _isStory,
        "invalidProduct": false,
        "is_local_image": _isUsingLocalImage,
      };

      globalLogger.d("Dados para salvar: $productData");

      final savedProduct = await _productService.saveProduct(
        productData,
      );

      if (savedProduct != null) {
        globalLogger.i(
          "Produto salvo com sucesso (ID: ${savedProduct.id}).",
        );

        if (_dispatchWhatsapp) {
          globalLogger.i(
            "Disparo do WhatsApp seria acionado aqui para o grupo: $_selectedWhatsappGroup",
          );
        }
        clearForm();
        return true;
      } else {
        _publishError = "Falha ao salvar o produto no servidor.";
        return false;
      }
    } catch (e) {
      globalLogger.e("Erro ao publicar produto: $e");
      _publishError = "Erro ao publicar: $e";
      return false;
    } finally {
      _isPublishing = false;
      notifyListeners();
    }
  }

  void clearControllers() {
    _urlController.clear();
    _titleController.clear();
    _descriptionController.clear();
    _priceController.clear();
    _oldPriceController.clear();
    _couponController.clear();
  }

  @override
  void dispose() {
    // Liberar os recursos dos controllers
    _urlController.dispose();
    _titleController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _oldPriceController.dispose();
    _couponController.dispose();
    super.dispose();
  }
}
