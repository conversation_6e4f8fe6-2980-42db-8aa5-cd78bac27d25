import 'package:flutter/material.dart';

import '../../controllers/products_controller.dart';

class ProductInfoSection extends StatelessWidget {
  final ProductsController controller;
  final GlobalKey<FormState> formKey;

  const ProductInfoSection({super.key, required this.controller, required this.formKey});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Informações do Produto', style: theme.textTheme.titleLarge),
        const SizedBox(height: 16),

        // Título
        ProductFormField(controller: controller.titleController, label: 'Títu<PERSON>', readOnly: !controller.isEditingProduct, validator: (value) => value?.isEmpty ?? true ? 'Campo obrigatório' : null),
        const SizedBox(height: 16),

        // Descrição
        ProductFormField(controller: controller.descriptionController, label: 'Descri<PERSON>', readOnly: !controller.isEditingProduct, maxLines: 3),
        const SizedBox(height: 16),

        // Preço atual
        ProductFormField(
          controller: controller.priceController,
          label: 'Preço Atual',
          readOnly: !controller.isEditingProduct,
          keyboardType: TextInputType.number,
          prefixText: 'R\$ ',
          validator: (value) => value?.isEmpty ?? true ? 'Campo obrigatório' : null,
        ),
        const SizedBox(height: 16),

        // Preço antigo
        ProductFormField(controller: controller.oldPriceController, label: 'Preço Antigo', readOnly: !controller.isEditingProduct, keyboardType: TextInputType.number, prefixText: 'R\$ '),
        const SizedBox(height: 16),

        // Cupom
        ProductFormField(controller: controller.couponController, label: 'Cupom', readOnly: !controller.isEditingProduct),
        const SizedBox(height: 16),

        // Categoria
        ProductFormField(controller: controller.categoryController, label: 'Categoria', readOnly: !controller.isEditingProduct),
        const SizedBox(height: 16),

        // Subcategoria
        ProductFormField(controller: controller.subcategoryController, label: 'Subcategoria', readOnly: !controller.isEditingProduct),
        const SizedBox(height: 16),

        // URL do Produto
        ProductFormField(controller: controller.productUrlController, label: 'URL do Produto', readOnly: !controller.isEditingProduct, hintText: 'URL original do produto na loja'),
      ],
    );
  }
}

class ProductFormField extends StatelessWidget {
  final TextEditingController controller;
  final String label;
  final bool readOnly;
  final int? maxLines;
  final TextInputType? keyboardType;
  final String? prefixText;
  final String? hintText;
  final String? Function(String?)? validator;

  const ProductFormField({super.key, required this.controller, required this.label, this.readOnly = false, this.maxLines = 1, this.keyboardType, this.prefixText, this.hintText, this.validator});

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(labelText: label, border: const OutlineInputBorder(), prefixText: prefixText, hintText: hintText),
      readOnly: readOnly,
      maxLines: maxLines,
      keyboardType: keyboardType,
      validator: validator,
    );
  }
}
