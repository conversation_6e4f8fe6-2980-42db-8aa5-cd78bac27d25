"""
Runner para o Scrapy
"""

import logging
from typing import Dict, List, Any, Optional

# Configuração de logging
logger = logging.getLogger(__name__)

def scrape_mercadolivre(url: str, max_pages: int = 2, output_file: str = None) -> List[Dict[str, Any]]:
    """
    Função para raspar produtos do Mercado Livre usando Scrapy

    Args:
        url: URL para raspar
        max_pages: Número máximo de páginas a serem raspadas
        output_file: Arquivo de saída para salvar os resultados

    Returns:
        Lista de produtos
    """
    logger.warning("Esta é uma implementação de stub do scrape_mercadolivre. O sistema usará o scraper direto.")

    # Verificar se é uma URL de perfil social ou link de compartilhamento
    if "/social/" in url:
        logger.warning(f"URL de perfil social detectada: {url}")
        return [{
            "platform": "Mercado Livre",
            "url_produto": url,
            "url_afiliado": "",
            "title": "URL de perfil social não suportada",
            "description": "Esta URL é um link de perfil social ou compartilhamento, não um produto específico. Por favor, use uma URL direta de produto do Mercado Livre.",
            "error": True
        }]

    # Esta é apenas uma implementação de stub para evitar erros de importação
    # O sistema usará o scraper direto em vez desta implementação

    return []
