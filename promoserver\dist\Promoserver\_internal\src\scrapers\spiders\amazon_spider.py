import scrapy
import logging
import re
from datetime import datetime
from urllib.parse import urlparse, parse_qs, urle<PERSON><PERSON>, urlunparse

logger = logging.getLogger(__name__)

class AmazonSpider(scrapy.Spider):
    name = "amazon"
    allowed_domains = ["amazon.com.br", "amazon.com"]

    def __init__(self, url=None, *args, **kwargs):
        super(AmazonSpider, self).__init__(*args, **kwargs)
        self.start_urls = [url] if url else []
        self.logger.info(f"Iniciando spider para URL: {url}")

    def parse(self, response):
        self.logger.info(f"Processando página: {response.url}")

        # Extrai o título do produto
        title = response.css('#productTitle::text').get()
        if not title:
            title = response.css('#title::text').get()
        if not title:
            title = response.css('.product-title-word-break::text').get()
        if not title:
            title = response.css('title::text').get()

        if title:
            title = title.strip()

        if not title:
            self.logger.warning(f"Não foi possível extrair o título do produto: {response.url}")
            title = "Título não encontrado"

        price = None
        old_price = None

        # --- Extração de Preço Atual (com base no HTML fornecido e fallbacks) ---
        # Tentativa 1: Estrutura com priceToPay (HTML fornecido)
        price_symbol_new = response.css('span.priceToPay span.a-price-symbol::text').get()
        price_whole_new = response.css('span.priceToPay span.a-price-whole::text').get()
        price_fraction_new = response.css('span.priceToPay span.a-price-fraction::text').get()

        if price_symbol_new and price_whole_new:
            price = f"{price_symbol_new.strip()}{price_whole_new.strip()}"
            if price_fraction_new:
                price += f",{price_fraction_new.strip()}"
            self.logger.info(f"Preço atual (priceToPay) encontrado: {price}")

        # Tentativa 2: Usar o .aok-offscreen que é irmão do savingsPercentage (HTML fornecido)
        if not price:
            offscreen_price_text = response.css('div.aok-relative > span.aok-offscreen::text').get()
            if offscreen_price_text:
                # Extrair "R$ XXX,XX" da string "R$ XXX,XX com YY por cento de desconto"
                match = re.search(r"(R\$\s*[\d.,]+)", offscreen_price_text)
                if match:
                    price = match.group(1).replace(" ", "").replace(".", "").replace(",", ",") # Normaliza para R$XXX,XX
                    # Re-adiciona o R$ e formata corretamente
                    price_parts = price.replace("R$", "").split(',')
                    if len(price_parts) == 2:
                        price = f"R$ {price_parts[0]},{price_parts[1]}"
                    elif len(price_parts) == 1:
                         price = f"R$ {price_parts[0]},00" # Adiciona ,00 se não tiver centavos
                    else:
                        price = None # Formato inesperado
                    if price:
                        self.logger.info(f"Preço atual (aok-offscreen irmão de savings) encontrado: {price}")
        
        # Fallbacks (lógica anterior, ajustada)
        if not price:
            price_selectors_current_fallback = [
                '#corePrice_feature_div .a-price:not(.a-text-price) .a-offscreen::text',
                '#corePrice_feature_div span[data-a-color="price"] .a-offscreen::text',
                '#priceblock_ourprice::text',
                '#priceblock_dealprice::text',
                '.apexPriceToPay .a-offscreen::text',
            ]
            for selector in price_selectors_current_fallback:
                price_text = response.css(selector).get()
                if price_text:
                    price = price_text.strip()
                    if price:
                        self.logger.info(f"Preço atual (fallback: {selector}) encontrado: {price}")
                        break
        if not price:
            price_whole_element = response.xpath('//span[contains(@class, "a-price-whole") and not(ancestor::span[contains(@class, "a-text-price")])]/text()').get()
            price_fraction_element = response.xpath('//span[contains(@class, "a-price-fraction") and not(ancestor::span[contains(@class, "a-text-price")])]/text()').get()
            if price_whole_element:
                current_price_str = price_whole_element.strip().replace('.', '')
                if price_fraction_element:
                    current_price_str += f",{price_fraction_element.strip()}"
                else:
                    current_price_str += ",00"
                price = f"R$ {current_price_str}"
                self.logger.info(f"Preço atual (fallback whole/fraction) encontrado: {price}")

        # --- Extração de Preço Antigo (com base no HTML fornecido e fallbacks) ---
        # Tentativa 1: Usar o span.aok-offscreen que contém "De: R$..." (HTML fornecido)
        # O HTML é: <span class="a-size-small aok-offscreen"> De: R$&nbsp;209,90 </span>
        # Este span é irmão de <span class="basisPrice">...</span>
        old_price_offscreen_de = response.xpath('//div[contains(@id, "corePriceDisplay_desktop_feature_div")]//span[contains(@class, "aok-offscreen") and contains(text(), "De:")]/text()').get()
        if old_price_offscreen_de:
            match = re.search(r"R\$\s*([\d.,]+)", old_price_offscreen_de)
            if match:
                old_price = f"R${match.group(1).replace('.', ',')}" # Garante formato R$XXX,XX
                self.logger.info(f"Preço antigo (aok-offscreen 'De:') encontrado: {old_price}")
        
        # Tentativa 2: Estrutura com basisPrice e a-text-price[data-a-strike="true"] (HTML fornecido)
        if not old_price:
            old_price_text_basis = response.css('span.basisPrice span.a-text-price[data-a-strike="true"] span.a-offscreen::text').get()
            if old_price_text_basis:
                old_price = old_price_text_basis.strip()
                self.logger.info(f"Preço antigo (basisPrice a-text-price aok-offscreen) encontrado: {old_price}")

        # Fallbacks (lógica anterior, ajustada para ser mais específica)
        if not old_price:
            old_price_selectors_fallback = [
                # Procura por um a-text-price que esteja explicitamente riscado ou seja um preço de lista
                'span.a-price.a-text-price[data-a-strike="true"] span.a-offscreen::text',
                '#priceblock_listprice::text', # Layouts mais antigos
                # Seletor mais genérico, mas tenta evitar o preço por unidade
                '.a-text-price:not(ancestor::span[contains(@class, "pricePerUnit")]) .a-offscreen::text',
            ]
            for selector in old_price_selectors_fallback:
                old_price_text = response.css(selector).get()
                if old_price_text:
                    # Adicionalmente, verificar se o texto não é o preço por unidade
                    if "/ unidade" not in old_price_text and "/ unit" not in old_price_text:
                         old_price = old_price_text.strip()
                         if old_price:
                            self.logger.info(f"Preço antigo (fallback: {selector}) encontrado: {old_price}")
                            break
        
        # Verificação final se o preço atual pegou o antigo por engano
        if price and old_price and price == old_price:
            self.logger.warning(f"Preço atual ({price}) igual ao preço antigo ({old_price}). Tentando encontrar um preço diferente que não seja o antigo.")
            # Tenta pegar o primeiro .a-offscreen que não seja o old_price e não esteja dentro de um a-text-price
            all_potential_current_prices = response.xpath('//span[contains(@class, "a-price") and not(contains(@class, "a-text-price"))]//span[@class="a-offscreen"]/text()').getall()
            if not all_potential_current_prices: # Tenta um seletor mais amplo se o anterior falhar
                all_potential_current_prices = response.css('.a-price .a-offscreen::text').getall()

            for p_text_candidate in all_potential_current_prices:
                p_text_candidate_stripped = p_text_candidate.strip()
                if p_text_candidate_stripped and p_text_candidate_stripped != old_price:
                    price = p_text_candidate_stripped
                    self.logger.info(f"Preço atual corrigido para: {price} (encontrado um diferente do antigo e não dentro de a-text-price)")
                    break
            if price == old_price:
                 self.logger.warning(f"Não foi possível encontrar um preço atual diferente do preço antigo. Preço atual mantido: {price}")

        # Extrai a imagem
        image_url = response.css('#landingImage::attr(src)').get()
        if not image_url:
            image_url = response.css('#imgBlkFront::attr(src)').get()
        if not image_url:
            image_url = response.css('#main-image::attr(src)').get()
        if not image_url:
            image_url = response.css('.a-dynamic-image::attr(src)').get()
        if not image_url:
            # Tenta extrair a imagem do data-old-hires
            image_url = response.css('#landingImage::attr(data-old-hires)').get()
        if not image_url:
            # Tenta extrair a imagem do data-a-dynamic-image (JSON)
            data_dynamic = response.css('#landingImage::attr(data-a-dynamic-image)').get()
            if data_dynamic:
                try:
                    import json
                    image_dict = json.loads(data_dynamic)
                    if image_dict and isinstance(image_dict, dict) and len(image_dict) > 0:
                        # Pega a URL da primeira imagem (geralmente a maior)
                        image_url = list(image_dict.keys())[0]
                except Exception as e:
                    self.logger.error(f"Erro ao extrair imagem do data-a-dynamic-image: {e}")
        if not image_url:
            # Tenta qualquer imagem da Amazon
            image_url = response.css('img[src*="images-amazon"]::attr(src)').get()

        # Extrai informações de parcelamento
        installments = response.css('#installmentCalculator::text').get()
        if not installments:
            installments = "Consulte parcelas no site"

        # Extrai informações de cupom - Deixar em branco conforme solicitado
        coupon_info = ""

        # Extrai informações de frete
        shipping = "Verificar frete"
        shipping_text = response.css('#deliveryBlockMessage::text').get()
        if shipping_text:
            shipping_text = shipping_text.lower()
            if "frete grátis" in shipping_text or "entrega grátis" in shipping_text:
                shipping = "Com frete"

        prime_eligible = response.css('.prime-eligible')
        if prime_eligible:
            shipping = "Com frete (Prime)"

        # Extrai o ID do produto da URL (ASIN)
        product_id = None
        patterns = [
            r"/dp/([A-Z0-9]{10})",
            r"/gp/product/([A-Z0-9]{10})",
            r"/dp%2F([A-Z0-9]{10})",
            r"&ASIN=([A-Z0-9]{10})",
            r"/ASIN/([A-Z0-9]{10})",
        ]
        for pattern in patterns:
            match = re.search(pattern, response.url, re.IGNORECASE)
            if match:
                product_id = match.group(1).upper()
                break

        # Limpa a URL do produto
        product_url = self.clean_product_url(response.url)

        # Retorna os dados do produto
        return {
            "platform": "Amazon",
            "product_id": product_id,
            "url_produto": product_url,
            "url_afiliado": "",  # Deixa vazio conforme solicitado
            "title": title, # Já stripado
            "description": "",  # Deixa vazio conforme solicitado
            "price": price if price else "Preço não disponível", # Já stripado e formatado
            "old_price": old_price if old_price else None, # Já stripado, retorna None se não encontrado
            "image_url": image_url,
            "installments": installments.strip() if installments else "Consulte parcelas no site",
            "coupon_info": coupon_info, # Já está ""
            "shipping": shipping.strip() if shipping else "Verificar frete",
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            # Campos adicionais para compatibilidade com PromoHunter
            "ativo": True,
            "menor_preco": False,
            "indicamos": False,
            "disparar_whatsapp": False,
            "frete": False, # Redundante com shipping, mas mantendo
            "invalidProduct": False,
            "isStory": False,
            "cupom": coupon_info # Já está ""
        }

    def clean_product_url(self, url):
        """
        Limpa a URL do produto, removendo parâmetros de tracking
        """
        if not url:
            return ""

        try:
            parsed = urlparse(url)

            # Remove fragmentos (tudo após #)
            clean_url = url.split('#')[0]

            # Se for uma URL de produto da Amazon, simplifica para a forma canônica
            if "/dp/" in parsed.path:
                asin_match = re.search(r"/dp/([A-Z0-9]{10})", parsed.path, re.IGNORECASE)
                if asin_match:
                    asin = asin_match.group(1)
                    return f"https://{parsed.netloc}/dp/{asin}"

            # Remove parâmetros de tracking
            params_to_remove = [
                "ref", "ref_", "tag", "_encoding", "psc", "pd_rd_w", "pd_rd_r",
                "pd_rd_wg", "sprefix", "keywords", "crid", "dchild", "qid", "sr",
                "th", "spm", "trk", "smid", "asc_source", "asc_campaign", "asc_refurl",
                "searchVariation", "position", "tracking_id", "source_id", "component_id",
                "item_id", "category_id", "official_store_id", "pdp_filters", "dealer_id",
                "force_landing_page"
            ]

            query_params = parse_qs(parsed.query, keep_blank_values=True)
            filtered_params = {
                k: v for k, v in query_params.items()
                if k.lower() not in params_to_remove
                and not k.lower().startswith("asc_")
                and not k.lower().startswith("pd_rd_")
                and not k.lower().startswith("pf_rd_")
                and not k.lower().startswith("ref_")
                and not k.lower().startswith("sp_")
            }

            new_query = urlencode(filtered_params, doseq=True)

            clean_url = urlunparse((
                parsed.scheme,
                parsed.netloc,
                parsed.path.rstrip("/"),
                parsed.params,
                new_query,
                ""
            ))

            if clean_url.endswith("?"):
                clean_url = clean_url[:-1]

            return clean_url
        except Exception as e:
            self.logger.error(f"Erro ao limpar URL: {e}")

        return url
