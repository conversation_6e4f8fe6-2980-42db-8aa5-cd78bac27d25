"""
Módulo de endpoints de seletores para a API do Promoserver.
Contém funções relacionadas aos endpoints de seletores.
"""

import logging
from typing import List, Tuple
from fastapi import APIRouter, Depends, status

from components.auth import verify_supabase_session
from components.models import SelectorData, SelectorOutput
from components.exceptions import ServiceUnavailableException, handle_exception

log = logging.getLogger("api.selectors")

# Roteador para endpoints de seletores
router = APIRouter(prefix="/selectors", tags=["selectors"])

# Referência ao SelectorManager (será definida na inicialização)
selector_manager = None

def initialize(manager):
    """Inicializa o módulo com a instância do SelectorManager."""
    global selector_manager
    selector_manager = manager

@router.get(
    "",
    response_model=List[Tuple],
    dependencies=[Depends(verify_supabase_session)],
)
async def get_all_selectors_api(user_info: dict = Depends(verify_supabase_session)):
    """
    Retorna todos os seletores.
    
    Args:
        user_info: Informações do usuário autenticado
    
    Returns:
        List[Tuple]: Lista de seletores
    """
    try:
        log.info(f"Usuário {user_info.get('email')} acessando GET /selectors")
        if not selector_manager:
            raise ServiceUnavailableException("Selector Manager")
        selectors_data = selector_manager.get_all_selectors_for_ui()
        return selectors_data
    except Exception as e:
        log.error(f"Erro ao buscar seletores para o usuário {user_info.get('email')}: {e}")
        log.exception(e)
        return handle_exception(e, "Erro interno ao buscar seletores")

@router.post(
    "",
    response_model=SelectorOutput,
    status_code=status.HTTP_201_CREATED,
    dependencies=[Depends(verify_supabase_session)],
)
async def add_selector_api(
    selector_data: SelectorData, user_info: dict = Depends(verify_supabase_session)
):
    """
    Adiciona um novo seletor.
    
    Args:
        selector_data: Dados do seletor
        user_info: Informações do usuário autenticado
    
    Returns:
        SelectorOutput: Seletor adicionado
    """
    log.info(
        f"Usuário {user_info.get('email')} tentando adicionar seletor para loja {selector_data.store_id}"
    )
    if not selector_manager:
        raise ServiceUnavailableException("Selector Manager")
    try:
        new_selector_flat = selector_manager.add_selector(
            store_id=selector_data.store_id,
            selector_type=selector_data.type,
            selector_text=selector_data.selector,
            description=selector_data.description or "",
            active=selector_data.active,
        )
        if new_selector_flat:
            store_name = selector_manager.get_store_name(
                new_selector_flat["store_id"]
            )
            return SelectorOutput(
                id=new_selector_flat["id"],
                store=store_name,
                store_id=new_selector_flat["store_id"],
                type=new_selector_flat["type"],
                selector=new_selector_flat["selector"],
                description=new_selector_flat["description"],
                active=new_selector_flat["active"],
            )
        else:
            log.error(
                f"Falha ao adicionar seletor para o usuário {user_info.get('email')} Dados: {selector_data}"
            )
            return handle_exception(
                Exception("Falha ao adicionar seletor"),
                "Duplicado ou erro de salvamento?"
            )
    except Exception as e:
        log.exception(
            f"Erro ao adicionar seletor para o usuário {user_info.get('email')}"
        )
        return handle_exception(e, "Erro interno ao adicionar seletor")

@router.put(
    "/{selector_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    dependencies=[Depends(verify_supabase_session)],
)
async def update_selector_api(
    selector_id: int,
    selector_data: SelectorData,
    user_info: dict = Depends(verify_supabase_session),
):
    """
    Atualiza um seletor existente.
    
    Args:
        selector_id: ID do seletor
        selector_data: Dados do seletor
        user_info: Informações do usuário autenticado
    """
    log.info(
        f"Usuário {user_info.get('email')} tentando atualizar o seletor {selector_id}"
    )
    if not selector_manager:
        raise ServiceUnavailableException("Selector Manager")
    try:
        success = selector_manager.update_selector(
            selector_id=selector_id,
            store_id=selector_data.store_id,
            selector_type=selector_data.type,
            selector_text=selector_data.selector,
            description=selector_data.description or "",
            active=selector_data.active,
        )
        if not success:
            log.warning(
                f"Falha ao atualizar o seletor {selector_id} pelo usuário {user_info.get('email')}."
            )
            raise handle_exception(
                Exception(f"ID do Seletor {selector_id} não encontrado ou falha ao atualizar")
            )
        log.info(
            f"Seletor {selector_id} atualizado com sucesso pelo usuário {user_info.get('email')}"
        )
    except Exception as e:
        log.exception(
            f"Erro ao atualizar o seletor {selector_id} pelo usuário {user_info.get('email')}"
        )
        raise handle_exception(e, "Erro interno ao atualizar o seletor")

@router.delete(
    "/{selector_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    dependencies=[Depends(verify_supabase_session)],
)
async def delete_selector_api(
    selector_id: int, user_info: dict = Depends(verify_supabase_session)
):
    """
    Exclui um seletor.
    
    Args:
        selector_id: ID do seletor
        user_info: Informações do usuário autenticado
    """
    log.info(
        f"Usuário {user_info.get('email')} tentando excluir o seletor {selector_id}"
    )
    if not selector_manager:
        raise ServiceUnavailableException("Selector Manager")
    try:
        success = selector_manager.delete_selector(selector_id)
        if not success:
            log.warning(
                f"Falha ao excluir o seletor {selector_id} pelo usuário {user_info.get('email')}."
            )
            raise handle_exception(
                Exception(f"ID do Seletor {selector_id} não encontrado ou falha ao excluir")
            )
        log.info(
            f"Seletor {selector_id} excluído com sucesso pelo usuário {user_info.get('email')}"
        )
    except Exception as e:
        log.exception(
            f"Erro ao excluir o seletor {selector_id} pelo usuário {user_info.get('email')}"
        )
        raise handle_exception(e, "Erro interno ao excluir o seletor")
