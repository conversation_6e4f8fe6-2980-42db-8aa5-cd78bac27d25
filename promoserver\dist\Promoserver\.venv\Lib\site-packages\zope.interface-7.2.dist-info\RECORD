zope.interface-7.2-py3.12-nspkg.pth,sha256=zDNDwM8h45DoWcASl9nbs_zA6RqUsLEayDGITnaf1Uw,458
zope.interface-7.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
zope.interface-7.2.dist-info/LICENSE.txt,sha256=bHkPG2Oy1PmxiQVWnzhtYjAlM1wCWbZQ6AyenUetrS0,2114
zope.interface-7.2.dist-info/METADATA,sha256=X9C0QLi6b6GxcA4XMvbnoNuO1LNmwR6jTADdUT5tRS4,45577
zope.interface-7.2.dist-info/RECORD,,
zope.interface-7.2.dist-info/WHEEL,sha256=p2ZSKEh8afWXttZ398mr8-70gvkpUp1ipxCIV_GFr38,101
zope.interface-7.2.dist-info/namespace_packages.txt,sha256=QpUHvpO4wIuZDeEgKY8qZCtD-tAukB0fn_f6utzlb98,5
zope.interface-7.2.dist-info/top_level.txt,sha256=QpUHvpO4wIuZDeEgKY8qZCtD-tAukB0fn_f6utzlb98,5
zope/interface/__init__.py,sha256=e-cFZ9WVyxula99zvOO3DfvMMGA_Znfw3B3FaeG5JS4,3565
zope/interface/__pycache__/__init__.cpython-312.pyc,,
zope/interface/__pycache__/_compat.cpython-312.pyc,,
zope/interface/__pycache__/_flatten.cpython-312.pyc,,
zope/interface/__pycache__/adapter.cpython-312.pyc,,
zope/interface/__pycache__/advice.cpython-312.pyc,,
zope/interface/__pycache__/declarations.cpython-312.pyc,,
zope/interface/__pycache__/document.cpython-312.pyc,,
zope/interface/__pycache__/exceptions.cpython-312.pyc,,
zope/interface/__pycache__/interface.cpython-312.pyc,,
zope/interface/__pycache__/interfaces.cpython-312.pyc,,
zope/interface/__pycache__/registry.cpython-312.pyc,,
zope/interface/__pycache__/ro.cpython-312.pyc,,
zope/interface/__pycache__/verify.cpython-312.pyc,,
zope/interface/_compat.py,sha256=jFKOZQasUwWLY0ytB5ZcTe0OzZILpGRDo7MGK2Ni33o,4552
zope/interface/_flatten.py,sha256=Ij7iFkJvFoofx2g4xdSshhw66gVPI5xtWXayD5qjmNA,1095
zope/interface/_zope_interface_coptimizations.c,sha256=U6vZefzK-Q-Ezvvfgg8vivdp-usn_QcZ5ydqVhinI2M,73867
zope/interface/_zope_interface_coptimizations.cp312-win_amd64.pyd,sha256=9dp56dziVvJlXM0bpQ_vafol0wbfnnlJ31qQTQ5A66o,33280
zope/interface/adapter.py,sha256=AAnXrMv4yjRWwGC8SqsvQ_VzZXia3bAV0cHCz82ljcY,37695
zope/interface/advice.py,sha256=wp1NZCcndBu2OVNJvilPL-w1cNaS4ZNYePdWj8pSOb4,4039
zope/interface/common/__init__.py,sha256=chA1ocAiw4YGstx9jmu6HzH-8hlcGnxKur2QafNzG_U,10935
zope/interface/common/__pycache__/__init__.cpython-312.pyc,,
zope/interface/common/__pycache__/builtins.cpython-312.pyc,,
zope/interface/common/__pycache__/collections.cpython-312.pyc,,
zope/interface/common/__pycache__/idatetime.cpython-312.pyc,,
zope/interface/common/__pycache__/interfaces.cpython-312.pyc,,
zope/interface/common/__pycache__/io.cpython-312.pyc,,
zope/interface/common/__pycache__/mapping.cpython-312.pyc,,
zope/interface/common/__pycache__/numbers.cpython-312.pyc,,
zope/interface/common/__pycache__/sequence.cpython-312.pyc,,
zope/interface/common/builtins.py,sha256=RrI4WHUntoFxv3vAd2yY_wfBCUYpipwaPD9Ql-tjbi4,3259
zope/interface/common/collections.py,sha256=-rMgg9Z5b0EaO9MNLfacX18Yrl8qke6hxamYyCmr2hQ,7078
zope/interface/common/idatetime.py,sha256=S9p8iwjsZ8N9eOyFCUseCUVzNAhBFTY2egehp8w2oR0,21661
zope/interface/common/interfaces.py,sha256=YFQeKx_VcJLyBP9Nhb6Cw-CQb127ce67iXsBi6XHMEk,6189
zope/interface/common/io.py,sha256=Ua0Ev008IZbpjQj6H5NVdVFXbz0v3J_szga7TgKdWxU,1286
zope/interface/common/mapping.py,sha256=Ro5qTOOsLYYD8pbIrSxRdHQrYWE8gkF6RLdY35Z0hIc,4864
zope/interface/common/numbers.py,sha256=a0ykjvc-5dz_CEab0SezqN_0tCZk7JPtwOo-h4MCum0,1747
zope/interface/common/sequence.py,sha256=gm61MzWoSOZTXcioHeQ6qU0Ax5je5kFus-oA4PgC-TY,5726
zope/interface/common/tests/__init__.py,sha256=feY4_XACeHlXQXpGpWmt7D3z4h6JrLYse7xWY4e5KsA,5699
zope/interface/common/tests/__pycache__/__init__.cpython-312.pyc,,
zope/interface/common/tests/__pycache__/basemapping.cpython-312.pyc,,
zope/interface/common/tests/__pycache__/test_builtins.cpython-312.pyc,,
zope/interface/common/tests/__pycache__/test_collections.cpython-312.pyc,,
zope/interface/common/tests/__pycache__/test_idatetime.cpython-312.pyc,,
zope/interface/common/tests/__pycache__/test_import_interfaces.cpython-312.pyc,,
zope/interface/common/tests/__pycache__/test_io.cpython-312.pyc,,
zope/interface/common/tests/__pycache__/test_numbers.cpython-312.pyc,,
zope/interface/common/tests/basemapping.py,sha256=Vwi0F1Q2mKTabqOqj2QAA7t71eK2IgFxcEmSu98cK6c,3883
zope/interface/common/tests/test_builtins.py,sha256=remphfRzGIGe59-Xir0alehHrzAdT7l74USdK_S8rsc,1535
zope/interface/common/tests/test_collections.py,sha256=HcUw4h6XBixTdgO5wDelevZBl7-1zkeKdfKRodDXD7s,6213
zope/interface/common/tests/test_idatetime.py,sha256=q4HauTzO3FJRNuUQ0MA58A_vxq-bL1fhapSD5Zr1yjU,1971
zope/interface/common/tests/test_import_interfaces.py,sha256=jgxAOW58abJLcWIbnJgSaDRrRBgwxklK-RL68maNZT0,834
zope/interface/common/tests/test_io.py,sha256=vw2XbQUvYZrqhe3mVi1GplY2BjNd1plClBAzdhwF82I,1732
zope/interface/common/tests/test_numbers.py,sha256=_6hIrPUToK_egLYzw-FRVdnBIEz1I4CACaMtAo0ctB0,1437
zope/interface/declarations.py,sha256=wRaoqz5zqwQIM7RGlhdfGALuu_sf4lr1eUqFPhdL1t8,44731
zope/interface/document.py,sha256=kw7fkH2_pd-FQ61x6havR0z1THT5-o4_4vl3m3mcu-M,4272
zope/interface/exceptions.py,sha256=91IOj3TIyjXlTOL8feF14GEyAZnB7IbGD4xW3TSoBYc,8844
zope/interface/interface.py,sha256=w0U_7FjdNZboRyZmwzKG_ALZxUecX0rBcEapuX4lA-c,40955
zope/interface/interfaces.py,sha256=yQVXov3MmhS30a5PnTl-Fhc6cdKXQAormRhs_h4MFrw,51784
zope/interface/registry.py,sha256=uIIQ4IYVTGfyg6HyRat55PaWghrnu6o1BL8-e3GnEAA,26562
zope/interface/ro.py,sha256=0NTmzwi0joSWha34kB7quym_m53zPH_lsyfwsqHWfz4,25299
zope/interface/tests/__init__.py,sha256=-FWAz0pjCcu-g9uITMtGuL5wtMeXAWuIdRqbjJd6U6o,4372
zope/interface/tests/__pycache__/__init__.cpython-312.pyc,,
zope/interface/tests/__pycache__/advisory_testing.cpython-312.pyc,,
zope/interface/tests/__pycache__/dummy.cpython-312.pyc,,
zope/interface/tests/__pycache__/idummy.cpython-312.pyc,,
zope/interface/tests/__pycache__/m1.cpython-312.pyc,,
zope/interface/tests/__pycache__/odd.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_adapter.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_advice.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_compile_flags.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_declarations.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_document.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_element.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_exceptions.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_interface.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_interfaces.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_odd_declarations.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_registry.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_ro.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_sorting.cpython-312.pyc,,
zope/interface/tests/__pycache__/test_verify.cpython-312.pyc,,
zope/interface/tests/advisory_testing.py,sha256=g3xyQS21smCoqowecg_sEeivQrvGdyQMTBDgvwF6m1Q,929
zope/interface/tests/dummy.py,sha256=WFjrJK4-AUCujfqg17tkRtYTcFX4LiJtNsrjyw_wTx8,938
zope/interface/tests/idummy.py,sha256=JCWEgx_4xpnDUqyD_9ssMWOoS1zvfYHPimr43Zf5Ucs,914
zope/interface/tests/m1.py,sha256=_Misvz0v8zOUkS676bqZqEwU_Y7HwFDSM6GpjM8sKto,878
zope/interface/tests/odd.py,sha256=vivkFRZUG7hhhBZfIlZRVhpDrYjvdN6i0HwGr0Uog54,3097
zope/interface/tests/test_adapter.py,sha256=TnEfz6teWQ_GWj71Lczvfwi_H02m78r77uLcSmUGdG8,82823
zope/interface/tests/test_advice.py,sha256=KGO_-qN4iq67b_lEMDZ3cJYZquF5yVZErAXSuXiEo-M,6253
zope/interface/tests/test_compile_flags.py,sha256=ffwnFN2vX82Lof6PCpcJTtz22DsTrfVMjLm7qcfn1ls,1319
zope/interface/tests/test_declarations.py,sha256=KbdWBfNG4HnwZZ1dWAreygLLoyNYfHUPTWSQn52n-cM,85776
zope/interface/tests/test_document.py,sha256=zQeat-RirTDXpQ00Ikfj_2-FBHSVsjLpBWOoxN5CZDA,17767
zope/interface/tests/test_element.py,sha256=rVVLURdXNLejPqPUDImy6Wuoy1BGwOE7faV1e-CCRZM,1153
zope/interface/tests/test_exceptions.py,sha256=-aOqFpBNPe2o8UbDXz9THGDQFdjkBlUxtvMsXqw-01Y,6635
zope/interface/tests/test_interface.py,sha256=GRBU_Pn_wuyldmeU-glCyXoiOA7XIBhm8G_xrGy52lU,95588
zope/interface/tests/test_interfaces.py,sha256=Y0ITGkvMvuu_0uZZUB4boiiBeCcdAAUxbwjYL6fBbfE,4525
zope/interface/tests/test_odd_declarations.py,sha256=6PFpEf5OemuKDCUhakj_cS-v8Lmb6ZCm2xjxiWb1BX4,7961
zope/interface/tests/test_registry.py,sha256=cYm39HDPpHJCQ9kspQncFJdPSMAYi0gVMpjQ4M285gg,115990
zope/interface/tests/test_ro.py,sha256=EsPLM0BTt1B6EczQTDuwbjBJZ_CKMxWp8hvMc-O3Uu4,14952
zope/interface/tests/test_sorting.py,sha256=h9bgI95TXKVtqYSx7_06SwfxFzVFRHYqApzCKn2kKkU,2134
zope/interface/tests/test_verify.py,sha256=qxRizo4dzngbyebgoyBIoTSG4byEXgCZngTveqbfdx0,19856
zope/interface/verify.py,sha256=4p9XWQ5AW-F7aahFWJQjLFaMFsxGZWG5SJc_8niw1v4,7542
