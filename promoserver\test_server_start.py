#!/usr/bin/env python3
"""
Script de teste para verificar se o servidor pode ser iniciado corretamente.
"""

import os
import sys
import subprocess
from pathlib import Path

# Configurar caminhos
BASE_DIR = Path(__file__).parent.resolve()
VENV_PYTHON_WIN = BASE_DIR / ".venv" / "Scripts" / "python.exe"
API_MAIN_SCRIPT = BASE_DIR / "api_main.py"
SERVER_PORT = 8000

def get_uvicorn_command():
    """Constrói o comando uvicorn baseado no ambiente."""
    if os.name == "nt":
        # Windows: usar uvicorn.exe do venv
        uvicorn_exe = BASE_DIR / ".venv" / "Scripts" / "uvicorn.exe"
        if uvicorn_exe.exists():
            return [
                str(uvicorn_exe),
                f"{API_MAIN_SCRIPT.stem}:app",
                "--host", "0.0.0.0",
                "--port", str(SERVER_PORT),
                "--log-level", "debug",
                "--reload",
                "--reload-dir", str(BASE_DIR),
            ]
        else:
            # Fallback: usar python -m uvicorn
            return [
                str(VENV_PYTHON_WIN),
                "-m", "uvicorn",
                f"{API_MAIN_SCRIPT.stem}:app",
                "--host", "0.0.0.0", 
                "--port", str(SERVER_PORT),
                "--log-level", "debug",
                "--reload",
                "--reload-dir", str(BASE_DIR),
            ]
    else:
        # Linux/macOS
        return [
            "uvicorn",
            f"{API_MAIN_SCRIPT.stem}:app",
            "--host", "0.0.0.0",
            "--port", str(SERVER_PORT),
            "--log-level", "debug",
            "--reload",
            "--reload-dir", str(BASE_DIR),
        ]

def test_server_start():
    """Testa se o servidor pode ser iniciado."""
    print("=== Teste de Inicialização do Servidor ===")
    print(f"Diretório base: {BASE_DIR}")
    print(f"Python venv: {VENV_PYTHON_WIN}")
    print(f"Script API: {API_MAIN_SCRIPT}")
    
    # Verificar se os arquivos existem
    print("\n=== Verificação de Arquivos ===")
    print(f"Python venv existe: {VENV_PYTHON_WIN.exists()}")
    print(f"Script API existe: {API_MAIN_SCRIPT.exists()}")
    
    # Verificar uvicorn
    uvicorn_exe = BASE_DIR / ".venv" / "Scripts" / "uvicorn.exe"
    print(f"Uvicorn exe existe: {uvicorn_exe.exists()}")
    
    if not VENV_PYTHON_WIN.exists():
        print("ERRO: Ambiente virtual não encontrado!")
        return False
        
    if not API_MAIN_SCRIPT.exists():
        print("ERRO: Script api_main.py não encontrado!")
        return False
    
    # Construir comando
    command = get_uvicorn_command()
    print(f"\n=== Comando a ser executado ===")
    print(f"Comando: {' '.join(command)}")
    
    # Preparar ambiente
    env = os.environ.copy()
    env["PYTHONPATH"] = str(BASE_DIR)
    env["PYTHONUNBUFFERED"] = "1"
    env["PYTHONDONTWRITEBYTECODE"] = "1"
    
    print(f"PYTHONPATH: {env['PYTHONPATH']}")
    print(f"Diretório de trabalho: {BASE_DIR}")
    
    print("\n=== Tentando iniciar o servidor (será interrompido em 10 segundos) ===")
    
    try:
        # Iniciar processo
        process = subprocess.Popen(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding="utf-8",
            cwd=str(BASE_DIR),
            env=env,
        )
        
        print(f"Processo iniciado com PID: {process.pid}")
        
        # Aguardar alguns segundos para ver se há erros
        try:
            stdout, stderr = process.communicate(timeout=10)
            print("=== STDOUT ===")
            print(stdout)
            print("=== STDERR ===")
            print(stderr)
            
            if process.returncode == 0:
                print("✅ Servidor iniciado com sucesso!")
                return True
            else:
                print(f"❌ Servidor falhou com código: {process.returncode}")
                return False
                
        except subprocess.TimeoutExpired:
            print("✅ Servidor está rodando (timeout atingido - isso é esperado)")
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
            return True
            
    except FileNotFoundError as e:
        print(f"❌ Comando não encontrado: {e}")
        return False
    except Exception as e:
        print(f"❌ Erro ao iniciar servidor: {e}")
        return False

if __name__ == "__main__":
    success = test_server_start()
    sys.exit(0 if success else 1)
