from datetime import datetime


class ProdutoSupabaseGet:
    def __init__(
        self,
        id: str = "",
        criado_em: datetime = None,
        plataforma: str = "",
        url_afiliado: str = "",
        url_produto: str = "",  # Adicionado campo url_produto
        url_imagem: str = "",
        image_id: str = "",  # ID da imagem no Digital Ocean
        titulo: str = "",
        categoria: str = "",
        subcategoria: str = "",
        categoria_id: str = None,
        subcategoria_id: str = None, # Novo campo adicionado
        descricao: str = "",
        preco_atual: float = 0.0,
        preco_antigo: float = 0.0,
        preco_alternativo: float = 0.0,
        ativo: bool = True,
        cupom: str = "",
        menor_preco: bool = False,
        indicamos: bool = False,
        disparar_whatsapp: bool = False,
        grupo_whatsapp: str = "",
        frete: bool = False,
        isStory: bool = False,
        invalidProduct: bool = False,
    ):

        self.id = id
        self.criado_em = criado_em
        self.plataforma = plataforma
        self.url_afiliado = url_afiliado
        self.url_produto = url_produto  # Adicionado campo url_produto
        self.url_imagem = url_imagem
        self.image_id = image_id  # ID da imagem no Digital Ocean
        self.titulo = titulo
        self.categoria = categoria
        self.subcategoria = subcategoria
        self.categoria_id = categoria_id
        self.subcategoria_id = subcategoria_id # Novo campo adicionado
        self.descricao = descricao
        self.preco_atual = preco_atual
        self.preco_antigo = preco_antigo
        self.preco_alternativo = preco_alternativo
        self.ativo = ativo
        self.cupom = cupom
        self.menor_preco = menor_preco
        self.indicamos = indicamos
        self.disparar_whatsapp = disparar_whatsapp
        self.grupo_whatsapp = grupo_whatsapp
        self.frete = frete  # Corrigido: removido a tupla
        self.isStory = isStory
        self.invalidProduct = invalidProduct

    def to_dict(self):
        """Converte o objeto para um dicionário."""
        data = self.__dict__.copy()
        # Converte o datetime para string (ISO 8601) ao transformar em dicionário
        if isinstance(self.criado_em, datetime):
            data["criado_em"] = self.criado_em.isoformat()
        return data

    @staticmethod
    def from_dict(data):
        """Converte um dicionário em um objeto ProdutoSupabaseGet."""
        # Converte o campo criado_em para datetime, se necessário
        data_copy = data.copy()  # Criar uma cópia para não modificar o original

        # Tratar o campo criado_em
        if "criado_em" in data_copy and data_copy["criado_em"]:
            try:
                if isinstance(data_copy["criado_em"], str):
                    # Remover o Z e adicionar +00:00 para compatibilidade com fromisoformat
                    criado_em_str = data_copy["criado_em"].replace("Z", "+00:00")
                    data_copy["criado_em"] = datetime.fromisoformat(criado_em_str)
            except (ValueError, TypeError):
                # Se não conseguir converter, define como None
                data_copy["criado_em"] = None
        else:
            data_copy["criado_em"] = None

        # Garantir que todos os campos necessários estão presentes
        if "image_id" not in data_copy:
            data_copy["image_id"] = ""

        # Garantir que o ID seja uma string
        if "id" in data_copy and data_copy["id"] is not None:
            data_copy["id"] = str(data_copy["id"])

        return ProdutoSupabaseGet(**data_copy)

    @classmethod
    def model_validate(cls, data):
        """Método de compatibilidade com Pydantic v2 para converter um dicionário em um objeto ProdutoSupabaseGet."""
        return cls.from_dict(data)

    @classmethod
    def parse_obj(cls, data):
        """Método de compatibilidade com Pydantic v1 para converter um dicionário em um objeto ProdutoSupabaseGet."""
        return cls.from_dict(data)
