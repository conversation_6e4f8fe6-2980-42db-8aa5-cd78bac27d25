[Setup]
AppName=Promotor
AppVersion=1.0.0
AppPublisher=Promotor
AppPublisherURL=https://promobell.com.br
AppSupportURL=https://promobell.com.br
AppUpdatesURL=https://promobell.com.br
DefaultDirName={autopf}\Promotor
DefaultGroupName=Promotor
AllowNoIcons=yes
LicenseFile=
InfoBeforeFile=
InfoAfterFile=
OutputDir=installer
OutputBaseFilename=Promotor v1.0.0
SetupIconFile=promoserver\assets\icon-promoserver-blue.ico
UninstallDisplayIcon={app}\assets\icon-promoserver-blue.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern
WizardImageFile=
WizardSmallImageFile=
ArchitecturesAllowed=x64compatible
ArchitecturesInstallIn64BitMode=x64compatible
PrivilegesRequired=admin
MinVersion=6.2

[Languages]
Name: "brazilianportuguese"; MessagesFile: "compiler:Languages\BrazilianPortuguese.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1
Name: "autostart"; Description: "Iniciar Promoserver automaticamente com o Windows"; GroupDescription: "Opções de inicialização:"

[Files]
; Executável do Promoserver
Source: "promoserver\dist\Promoserver\Promoserver.exe"; DestDir: "{app}"; Flags: ignoreversion
; Pasta _internal do Promoserver (PyInstaller)
Source: "promoserver\dist\Promoserver\_internal\*"; DestDir: "{app}\_internal"; Flags: ignoreversion recursesubdirs createallsubdirs
; Pasta .venv do Promoserver
Source: "promoserver\dist\Promoserver\.venv\*"; DestDir: "{app}\.venv"; Flags: ignoreversion recursesubdirs createallsubdirs
; Pastas necessárias do Promoserver
Source: "promoserver\dist\Promoserver\components\*"; DestDir: "{app}\components"; Flags: ignoreversion recursesubdirs createallsubdirs
Source: "promoserver\dist\Promoserver\src\*"; DestDir: "{app}\src"; Flags: ignoreversion recursesubdirs createallsubdirs
Source: "promoserver\dist\Promoserver\assets\*"; DestDir: "{app}\assets"; Flags: ignoreversion recursesubdirs createallsubdirs
Source: "promoserver\dist\Promoserver\api_main.py"; DestDir: "{app}"; Flags: ignoreversion
; Executável do Promotor
Source: "promotor\build\windows\x64\runner\Release\promotor.exe"; DestDir: "{app}"; Flags: ignoreversion; DestName: "Promotor.exe"
; Ícones do Promotor
Source: "promotor\assets\icon-promotor-blue.ico"; DestDir: "{app}\assets"; Flags: ignoreversion
Source: "promotor\assets\icon-promotor-blue.png"; DestDir: "{app}\assets"; Flags: ignoreversion
; Arquivo de configuração
Source: "promoserver\.env.example"; DestDir: "{app}"; Flags: ignoreversion; DestName: ".env"

[Icons]
; Ícone principal do Promotor no menu iniciar
Name: "{group}\Promotor"; Filename: "{app}\Promotor.exe"; WorkingDir: "{app}"; IconFilename: "{app}\assets\icon-promotor-blue.ico"
; Ícone do Promoserver no menu iniciar (oculto por padrão)
Name: "{group}\Promoserver"; Filename: "{app}\Promoserver.exe"; WorkingDir: "{app}"; IconFilename: "{app}\assets\icon-promoserver-blue.ico"; Flags: excludefromshowinnewinstall
; Ícone de desinstalação com ícone personalizado
Name: "{group}\{cm:UninstallProgram,Promotor}"; Filename: "{uninstallexe}"; IconFilename: "{app}\assets\icon-promoserver-blue.ico"
; Ícone na área de trabalho (opcional) - Promotor
Name: "{autodesktop}\Promotor"; Filename: "{app}\Promotor.exe"; WorkingDir: "{app}"; IconFilename: "{app}\assets\icon-promotor-blue.ico"; Tasks: desktopicon
; Ícone na barra de inicialização rápida (opcional) - Promotor
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\Promotor"; Filename: "{app}\Promotor.exe"; WorkingDir: "{app}"; IconFilename: "{app}\assets\icon-promotor-blue.ico"; Tasks: quicklaunchicon

[Registry]
; Configurar auto-inicialização do Promoserver
Root: HKCU; Subkey: "Software\Microsoft\Windows\CurrentVersion\Run"; ValueType: string; ValueName: "Promoserver"; ValueData: """{app}\Promoserver.exe"""; Tasks: autostart
; Registrar aplicação no sistema
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\Uninstall\Promotor"; ValueType: string; ValueName: "DisplayName"; ValueData: "Promotor"
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\Uninstall\Promotor"; ValueType: string; ValueName: "DisplayIcon"; ValueData: "{app}\assets\icon-promoserver-blue.ico"
Root: HKLM; Subkey: "Software\Microsoft\Windows\CurrentVersion\Uninstall\Promotor"; ValueType: string; ValueName: "DisplayVersion"; ValueData: "1.0.0"

[Run]
; Iniciar o Promoserver após a instalação (minimizado na bandeja)
Filename: "{app}\Promoserver.exe"; Description: "{cm:LaunchProgram,Promoserver}"; Flags: nowait postinstall skipifsilent
; Opção para abrir o Promotor após a instalação
Filename: "{app}\Promotor.exe"; Description: "{cm:LaunchProgram,Promotor}"; Flags: nowait postinstall skipifsilent unchecked

[UninstallDelete]
; Limpar arquivos de configuração e cache
Type: filesandordirs; Name: "{localappdata}\Promotor"
Type: filesandordirs; Name: "{userappdata}\Promotor"
Type: filesandordirs; Name: "{app}\logs"
Type: filesandordirs; Name: "{app}\cache"
Type: filesandordirs; Name: "{app}\temp"
Type: files; Name: "{app}\.env"

[UninstallRun]
; Parar processos antes da desinstalação
Filename: "taskkill"; Parameters: "/F /IM Promoserver.exe"; Flags: runhidden
Filename: "taskkill"; Parameters: "/F /IM Promotor.exe"; Flags: runhidden

[Code]
var
  PromoserverRunning, PromotorRunning: Boolean;

function IsPromoserverRunning(): Boolean;
var
  ResultCode: Integer;
begin
  Result := Exec('tasklist', '/FI "IMAGENAME eq Promoserver.exe" | find /I "Promoserver.exe"', '', SW_HIDE, ewWaitUntilTerminated, ResultCode) and (ResultCode = 0);
end;

function IsPromotorRunning(): Boolean;
var
  ResultCode: Integer;
begin
  Result := Exec('tasklist', '/FI "IMAGENAME eq Promotor.exe" | find /I "Promotor.exe"', '', SW_HIDE, ewWaitUntilTerminated, ResultCode) and (ResultCode = 0);
end;

function InitializeSetup(): Boolean;
var
  ResultCode: Integer;
begin
  Result := True;
  PromoserverRunning := IsPromoserverRunning();
  PromotorRunning := IsPromotorRunning();

  if PromoserverRunning or PromotorRunning then
  begin
    if MsgBox('O Promotor está em execução. É necessário fechá-lo antes de continuar a instalação.' + #13#10 +
              'Deseja que o instalador feche automaticamente os processos?',
              mbConfirmation, MB_YESNO) = IDYES then
    begin
      if PromoserverRunning then
      begin
        Exec('taskkill', '/F /IM Promoserver.exe', '', SW_HIDE, ewWaitUntilTerminated, ResultCode);
        Sleep(2000);
      end;

      if PromotorRunning then
      begin
        Exec('taskkill', '/F /IM Promotor.exe', '', SW_HIDE, ewWaitUntilTerminated, ResultCode);
        Sleep(2000);
      end;
    end
    else
    begin
      Result := False;
      MsgBox('Instalação cancelada. Feche o Promotor manualmente e execute o instalador novamente.', mbError, MB_OK);
    end;
  end;
end;

procedure CurStepChanged(CurStep: TSetupStep);
var
  ResultCode: Integer;
begin
  if CurStep = ssPostInstall then
  begin
    Sleep(3000);

    if WizardIsTaskSelected('autostart') then
    begin
      RegWriteStringValue(HKEY_CURRENT_USER, 'Software\Microsoft\Windows\CurrentVersion\Run',
                         'Promoserver', '"' + ExpandConstant('{app}') + '\Promoserver.exe"');
    end;
  end;
end;

function InitializeUninstall(): Boolean;
var
  ResultCode: Integer;
begin
  Result := True;
  
  if IsPromoserverRunning() or IsPromotorRunning() then
  begin
    if MsgBox('O Promotor está em execução. É necessário fechá-lo antes de continuar a desinstalação.' + #13#10 +
              'Deseja que o desinstalador feche automaticamente os processos?',
              mbConfirmation, MB_YESNO) = IDYES then
    begin
      Exec('taskkill', '/F /IM Promoserver.exe', '', SW_HIDE, ewWaitUntilTerminated, ResultCode);
      Exec('taskkill', '/F /IM Promotor.exe', '', SW_HIDE, ewWaitUntilTerminated, ResultCode);
      Sleep(3000);
    end
    else
    begin
      Result := False;
      MsgBox('Desinstalação cancelada. Feche o Promotor manualmente e execute o desinstalador novamente.', mbError, MB_OK);
    end;
  end;
end;

procedure CurUninstallStepChanged(CurUninstallStep: TUninstallStep);
begin
  if CurUninstallStep = usPostUninstall then
  begin
    RegDeleteValue(HKEY_CURRENT_USER, 'Software\Microsoft\Windows\CurrentVersion\Run', 'Promoserver');
    DelTree(ExpandConstant('{localappdata}') + '\Promotor', True, True, True);
    DelTree(ExpandConstant('{userappdata}') + '\Promotor', True, True, True);
  end;
end;
