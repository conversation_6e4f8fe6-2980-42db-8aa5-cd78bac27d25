from .. import Provider as DateTimeProvider


class Provider(DateTimeProvider):
    DAY_NAMES = {
        "0": "<PERSON><PERSON><PERSON>",
        "1": "<PERSON><PERSON><PERSON>",
        "2": "<PERSON><PERSON><PERSON><PERSON>",
        "3": "<PERSON><PERSON><PERSON>",
        "4": "<PERSON><PERSON>",
        "5": "<PERSON><PERSON>",
        "6": "<PERSON><PERSON><PERSON><PERSON>",
    }

    MONTH_NAMES = {
        "01": "Yanvar",
        "02": "Fe<PERSON><PERSON>",
        "03": "Mart",
        "04": "Aprel",
        "05": "May",
        "06": "<PERSON><PERSON>",
        "07": "<PERSON>yu<PERSON>",
        "08": "Avgust",
        "09": "Sentabr",
        "10": "Oktabr",
        "11": "Noyabr",
        "12": "Dekabr",
    }

    def day_of_week(self) -> str:
        day = self.date("%w")
        return self.DAY_NAMES[day]

    def month_name(self) -> str:
        month = self.month()
        return self.MONTH_NAMES[month]
