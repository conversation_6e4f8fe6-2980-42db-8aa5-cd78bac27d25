import logging
import os
import threading
import re
import json
from typing import List, Dict, Optional, Tuple

logger = logging.getLogger(__name__)


class SelectorManager:
    _instance = None
    _lock = threading.Lock()

    def __new__(cls, *args, **kwargs):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(SelectorManager, cls).__new__(cls)
        return cls._instance

    def __init__(self, selector_dir: str = None):
        if hasattr(self, "_initialized") and self._initialized:
            return

        with self._lock:
            if hasattr(self, "_initialized") and self._initialized:
                return

            logger.info("Inicializando SelectorManager...")
            base_dir = os.path.dirname(
                os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            )
            default_dir = os.path.join(base_dir, "src", "data", "selectors")

            self.selector_dir = selector_dir or default_dir
            self.selectors_by_store: Dict[str, Dict[str, List[Dict]]] = {}
            self.all_selectors_flat: List[Dict] = []
            self.current_status = "Initializing"
            self.load_all_selectors()

            if not hasattr(self, "_initialized"):
                logger.error("SelectorManager falhou ao inicializar corretamente.")
                self._initialized = False

    def load_all_selectors(self):
        """Loads selectors from all JSON files in the selector directory."""
        logger.info(f"Carregando seletores do diretório: {self.selector_dir}")
        self.selectors_by_store = {}
        self.all_selectors_flat = []
        loaded_files = 0
        initialization_successful = False

        try:
            if not os.path.isdir(self.selector_dir):
                logger.error(
                    f"Diretório de seletores não encontrado: {self.selector_dir}"
                )
                self.current_status = f"Error: Directory not found {self.selector_dir}"
                self._initialized = False
                return

            for filename in os.listdir(self.selector_dir):
                if filename.endswith("_selectors.json"):
                    filepath = os.path.join(self.selector_dir, filename)
                    try:
                        with open(filepath, "r", encoding="utf-8") as f:
                            data = json.load(f)
                            store_id = data.get("store_id")
                            store_selectors = data.get("selectors", {})

                            if store_id and isinstance(store_selectors, dict):
                                self.selectors_by_store[store_id] = {}
                                for type_key, selectors_list in store_selectors.items():
                                    valid_selectors_for_type = []
                                    if isinstance(selectors_list, list):
                                        for selector_data in selectors_list:

                                            if (
                                                isinstance(selector_data, dict)
                                                and "id" in selector_data
                                                and "selector" in selector_data
                                            ):
                                                flat_selector = {
                                                    **selector_data,
                                                    "store": store_id,
                                                    "type": type_key,
                                                }
                                                flat_selector.setdefault(
                                                    "description", ""
                                                )
                                                flat_selector.setdefault("active", True)

                                                self.all_selectors_flat.append(
                                                    flat_selector
                                                )
                                                valid_selectors_for_type.append(
                                                    selector_data
                                                )
                                            else:
                                                logger.warning(
                                                    f"Invalid selector format in {filename} for type {type_key}: {selector_data}"
                                                )
                                    else:
                                        logger.warning(
                                            f"Selectors for type '{type_key}' in {filename} is not a list: {selectors_list}"
                                        )

                                    if valid_selectors_for_type:
                                        self.selectors_by_store[store_id][
                                            type_key
                                        ] = valid_selectors_for_type

                                logger.info(
                                    f"Loaded selectors for store '{store_id}' from {filename}"
                                )
                                loaded_files += 1
                            else:
                                logger.warning(
                                    f"Skipping file {filename}: Missing 'store_id' or 'selectors' (must be a dict)."
                                )
                    except json.JSONDecodeError as e:
                        logger.error(f"Error decoding JSON from {filepath}: {e}")
                    except Exception as e:
                        logger.error(f"Error loading selectors from {filepath}: {e}")

            logger.info(f"Finished loading selectors. {loaded_files} files processed.")
            self.all_selectors_flat.sort(key=lambda x: x.get("id", float("inf")))
            self.current_status = f"Loaded {len(self.all_selectors_flat)} selectors from {loaded_files} files."
            initialization_successful = True

        except Exception as e:
            logger.exception(f"Failed to load selectors: {e}")
            self.current_status = f"Error loading selectors: {e}"
            initialization_successful = False
        finally:
            self._initialized = initialization_successful

    def save_selectors(self, store_id: str):
        """Saves the selectors for a specific store back to its JSON file."""
        logger.info(f"Attempting to save selectors for store: {store_id}")
        if store_id not in self.selectors_by_store:
            logger.error(
                f"Cannot save. Store '{store_id}' not found in loaded selectors."
            )
            return False

        filename = f"{store_id}_selectors.json"
        filepath = os.path.join(self.selector_dir, filename)

        store_name = store_id.capitalize()
        for sel in self.all_selectors_flat:
            if sel.get("store") == store_id and "store_name" in sel:
                store_name = sel["store_name"]
                break

        data_to_save = {
            "store_id": store_id,
            "store_name": store_name,
            "selectors": self.selectors_by_store[store_id],
        }

        try:
            os.makedirs(self.selector_dir, exist_ok=True)
            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(data_to_save, f, indent=4, ensure_ascii=False)
            logger.info(f"Successfully saved selectors for '{store_id}' to {filepath}")
            self.current_status = f"Saved selectors for {store_id}"
            return True
        except Exception as e:
            logger.exception(
                f"Error saving selectors for '{store_id}' to {filepath}: {e}"
            )
            self.current_status = f"Error saving selectors for {store_id}"
            return False

    def get_active_selectors(self, store_id: str, selector_type: str) -> List[Dict]:
        """Returns a list of active selector dictionaries for a given store and type."""
        store_data = self.selectors_by_store.get(store_id, {})
        selectors_list = store_data.get(selector_type, [])
        active_selectors = [
            s for s in selectors_list if isinstance(s, dict) and s.get("active", False)
        ]
        return active_selectors

    def get_all_selectors_for_ui(self) -> List[Tuple]:
        """Returns all selectors as a list of tuples for UI/API compatibility."""
        ui_list = []
        for selector in self.all_selectors_flat:
            ui_list.append(
                (
                    selector.get("id", -1),
                    selector.get("store", "unknown"),
                    selector.get("type", "unknown"),
                    selector.get("selector", ""),
                    selector.get("description", ""),
                    selector.get("active", False),
                    None,
                )
            )
        return ui_list

    def find_selector_by_id(self, selector_id: int) -> Optional[Dict]:
        """Finds a selector dictionary in the flat list by its ID."""
        for selector in self.all_selectors_flat:
            if selector.get("id") == selector_id:
                return selector
        return None

    def add_selector(
        self,
        store_id: str,
        selector_type: str,
        selector_text: str,
        description: str,
        active: bool,
    ) -> Optional[Dict]:
        """Adds a new selector. Returns the flat dict of the new selector or None on failure."""
        with self._lock:
            logger.info(
                f"Adding selector: Store={store_id}, Type={selector_type}, Selector={selector_text}"
            )
            if store_id not in self.selectors_by_store:
                self.selectors_by_store[store_id] = {}

            if selector_type not in self.selectors_by_store[store_id]:
                self.selectors_by_store[store_id][selector_type] = []

            for existing in self.selectors_by_store[store_id][selector_type]:
                if existing.get("selector") == selector_text:
                    logger.warning(
                        f"Selector '{selector_text}' already exists for {store_id}/{selector_type}."
                    )
                    return None

            max_id = max((s.get("id", 0) for s in self.all_selectors_flat), default=0)
            new_id = max_id + 1

            # Se o novo seletor vai ser ativo, desativar todos os outros do mesmo tipo
            if active:
                for sel in self.selectors_by_store[store_id][selector_type]:
                    if sel.get("active", False):
                        logger.info(f"Desativando seletor ID {sel.get('id')} para ativar o novo seletor")
                        sel["active"] = False

            new_nested_selector = {
                "id": new_id,
                "selector": selector_text,
                "description": description,
                "active": active,
            }
            self.selectors_by_store[store_id][selector_type].append(new_nested_selector)

            new_flat_selector = {
                **new_nested_selector,
                "store": store_id,
                "type": selector_type,
            }
            self.all_selectors_flat.append(new_flat_selector)
            self.all_selectors_flat.sort(key=lambda x: x.get("id", float("inf")))

            logger.info(f"Selector added with ID {new_id}. Attempting to save file.")
            if self.save_selectors(store_id):
                self.current_status = f"Selector ID {new_id} added for {store_id}."
                return new_flat_selector
            else:
                logger.error(
                    "Failed to save selectors file after adding. Rolling back."
                )
                try:
                    self.selectors_by_store[store_id][selector_type].pop()
                    self.all_selectors_flat = [
                        s for s in self.all_selectors_flat if s.get("id") != new_id
                    ]
                except IndexError:
                    pass
                self.current_status = (
                    f"Failed to save after adding selector for {store_id}."
                )
                return None

    def update_selector(
        self,
        selector_id: int,
        store_id: str,
        selector_type: str,
        selector_text: str,
        description: str,
        active: bool,
    ) -> bool:
        """Updates an existing selector by its ID."""
        with self._lock:
            logger.info(f"Updating selector ID: {selector_id}")
            original_flat_selector = self.find_selector_by_id(selector_id)
            if not original_flat_selector:
                logger.error(f"Selector ID {selector_id} not found for update.")
                return False

            original_store = original_flat_selector.get("store")
            original_type = original_flat_selector.get("type")

            nested_updated = False
            if original_store == store_id and original_type == selector_type:
                if (
                    original_store in self.selectors_by_store
                    and original_type in self.selectors_by_store[original_store]
                ):
                    # Se estamos ativando este seletor, desativar todos os outros do mesmo tipo
                    if active:
                        for sel in self.selectors_by_store[original_store][original_type]:
                            if sel.get("id") != selector_id and sel.get("active", False):
                                logger.info(f"Desativando seletor ID {sel.get('id')} para ativar o seletor ID {selector_id}")
                                sel["active"] = False

                    # Atualizar o seletor atual
                    for sel in self.selectors_by_store[original_store][original_type]:
                        if sel.get("id") == selector_id:
                            sel["selector"] = selector_text
                            sel["description"] = description
                            sel["active"] = active
                            nested_updated = True
                            break
            else:
                removed_from_old = False
                if (
                    original_store in self.selectors_by_store
                    and original_type in self.selectors_by_store[original_store]
                ):
                    old_list = self.selectors_by_store[original_store][original_type]
                    self.selectors_by_store[original_store][original_type] = [
                        s for s in old_list if s.get("id") != selector_id
                    ]
                    removed_from_old = len(
                        self.selectors_by_store[original_store][original_type]
                    ) < len(old_list)

                    if not removed_from_old:
                        logger.warning(
                            f"Selector ID {selector_id} was not found or not removed from original location {original_store}/{original_type} during update."
                        )
                    else:
                        logger.debug(
                            f"Selector ID {selector_id} removed from old location {original_store}/{original_type}."
                        )
                elif original_store is not None and original_type is not None:
                    logger.warning(
                        f"Original location {original_store}/{original_type} not found during update for ID {selector_id}."
                    )

                if store_id not in self.selectors_by_store:
                    self.selectors_by_store[store_id] = {}
                if selector_type not in self.selectors_by_store[store_id]:
                    self.selectors_by_store[store_id][selector_type] = []

                new_nested_data = {
                    "id": selector_id,
                    "selector": selector_text,
                    "description": description,
                    "active": active,
                }
                self.selectors_by_store[store_id][selector_type].append(new_nested_data)
                nested_updated = True

            if not nested_updated:
                logger.error(
                    f"Failed to update selector ID {selector_id} in nested structure."
                )
                return False

            for i, sel in enumerate(self.all_selectors_flat):
                if sel.get("id") == selector_id:
                    self.all_selectors_flat[i] = {
                        "id": selector_id,
                        "store": store_id,
                        "type": selector_type,
                        "selector": selector_text,
                        "description": description,
                        "active": active,
                    }
                    break

            files_to_save = {store_id}
            if store_id != original_store:
                files_to_save.add(original_store)

            save_success = all(self.save_selectors(store) for store in files_to_save)

            if save_success:
                self.current_status = f"Selector ID {selector_id} updated."
                logger.info(f"Selector ID {selector_id} updated successfully.")
            else:
                self.current_status = (
                    f"Error saving files after updating selector {selector_id}."
                )

            return save_success

    def delete_selector(self, selector_id: int) -> bool:
        """Removes a selector by its ID."""
        with self._lock:
            logger.info(f"Deleting selector ID: {selector_id}")
            flat_selector = self.find_selector_by_id(selector_id)
            if not flat_selector:
                logger.error(f"Selector ID {selector_id} not found for deletion.")
                return False

            store_id = flat_selector.get("store")
            selector_type = flat_selector.get("type")

            removed_from_nested = False
            if (
                store_id in self.selectors_by_store
                and selector_type in self.selectors_by_store[store_id]
            ):
                original_len = len(self.selectors_by_store[store_id][selector_type])
                self.selectors_by_store[store_id][selector_type] = [
                    s
                    for s in self.selectors_by_store[store_id][selector_type]
                    if s.get("id") != selector_id
                ]
                removed_from_nested = (
                    len(self.selectors_by_store[store_id][selector_type]) < original_len
                )
                if not removed_from_nested:
                    logger.warning(
                        f"Did not find ID {selector_id} in nested {store_id}/{selector_type}"
                    )

            original_flat_len = len(self.all_selectors_flat)
            self.all_selectors_flat = [
                s for s in self.all_selectors_flat if s.get("id") != selector_id
            ]
            removed_from_flat = len(self.all_selectors_flat) < original_flat_len
            if not removed_from_flat:
                logger.warning(f"Did not find ID {selector_id} in flat list")

            if not removed_from_nested and not removed_from_flat:
                logger.error(
                    f"Selector ID {selector_id} was not found in any structure."
                )
                return False

            if self.save_selectors(store_id):
                self.current_status = (
                    f"Selector ID {selector_id} deleted from {store_id}."
                )
                logger.info(f"Selector ID {selector_id} deleted successfully.")
                return True
            else:
                logger.error(
                    f"Failed to save selectors file for {store_id} after deleting ID {selector_id}."
                )
                self.current_status = (
                    f"Error saving file after deleting selector {selector_id}."
                )
                return False

    def get_status(self):
        with self._lock:
            return self.current_status

    def get_available_stores(self) -> List[str]:
        return sorted(list(self.selectors_by_store.keys()))

    def get_available_types(self, store_id: Optional[str] = None) -> List[str]:
        types = set()
        if store_id and store_id != "Todas":
            if store_id in self.selectors_by_store:
                types.update(self.selectors_by_store[store_id].keys())
        else:  # Todos os tipos de todas as lojas
            for store_data in self.selectors_by_store.values():
                types.update(store_data.keys())
        return sorted(list(types))

    def get_selectors_by_type(self, store_id: str, selector_type: str) -> List[Dict]:
        """Returns all selectors of a specific type for a store."""
        if (
            store_id in self.selectors_by_store
            and selector_type in self.selectors_by_store[store_id]
        ):
            return self.selectors_by_store[store_id][selector_type]
        return []

    def clean_product_url(self, url: str) -> str:
        """
        Limpa a URL do produto, removendo parâmetros de tracking e convertendo links de redirecionamento
        """
        if not url:
            return ""

        # Se for um link de redirecionamento
        if "click1.mercadolivre.com.br/mclics/clicks/external" in url or "click.mercadolivre.com.br" in url:
            try:
                # Extrai o ID do produto do parâmetro wid
                wid_match = re.search(r'wid=([^&]+)', url)
                if wid_match:
                    product_id = wid_match.group(1)
                    # Constrói a URL direta do produto
                    return f"https://www.mercadolivre.com.br/p/{product_id}"
            except Exception as e:
                logger.error(f"Erro ao processar URL de redirecionamento: {e}")
                return url

        # Para URLs diretas, remove parâmetros desnecessários
        try:
            base_url = url.split('#')[0]
            if "/p/MLB" in base_url:
                return base_url
        except Exception as e:
            logger.error(f"Erro ao limpar URL: {e}")

        return url

    # Método para uso interno
    def _dummy_method(self):
        """Método vazio para manter a estrutura do código"""

    def get_store_name(self, store_id: str) -> str:
        """
        Retorna o nome formatado da loja com base no ID da loja.

        Args:
            store_id: ID da loja (ex: 'mercadolivre')

        Returns:
            Nome formatado da loja (ex: 'Mercado Livre')
        """
        store_names = {
            "mercadolivre": "Mercado Livre",
            "magalu": "Magazine Luiza",
            "amazon": "Amazon",
            "americanas": "Americanas",
            "casasbahia": "Casas Bahia",
            "extra": "Extra",
            "pontofrio": "Ponto Frio",
            "submarino": "Submarino",
            "shoptime": "Shoptime",
            "kabum": "KaBuM",
            "aliexpress": "AliExpress",
            "shopee": "Shopee",
            "netshoes": "Netshoes",
            "centauro": "Centauro",
            "dafiti": "Dafiti",
            "zattini": "Zattini",
            "fastshop": "Fast Shop",
            "girafa": "Girafa",
            "carrefour": "Carrefour",
            "pichau": "Pichau",
            "terabyteshop": "Terabyte Shop",
            "dell": "Dell",
            "samsung": "Samsung",
            "apple": "Apple",
            "xiaomi": "Xiaomi",
            "motorola": "Motorola",
            "lg": "LG",
            "sony": "Sony",
            "philips": "Philips",
            "brastemp": "Brastemp",
            "consul": "Consul",
            "electrolux": "Electrolux",
            "multilaser": "Multilaser",
            "positivo": "Positivo",
            "lenovo": "Lenovo",
            "hp": "HP",
            "acer": "Acer",
            "asus": "Asus",
            "msi": "MSI",
            "gigabyte": "Gigabyte",
            "intel": "Intel",
            "amd": "AMD",
            "nvidia": "NVIDIA",
            "corsair": "Corsair",
            "hyperx": "HyperX",
            "logitech": "Logitech",
            "razer": "Razer",
            "steelseries": "SteelSeries",
            "microsoft": "Microsoft",
            "sony": "Sony",
            "nintendo": "Nintendo",
            "xbox": "Xbox",
            "playstation": "PlayStation",
            "switch": "Nintendo Switch",
            "wii": "Nintendo Wii",
            "ps4": "PlayStation 4",
            "ps5": "PlayStation 5",
            "xboxone": "Xbox One",
            "xboxseriesx": "Xbox Series X",
            "xboxseriess": "Xbox Series S",
        }

        # Retorna o nome formatado ou capitaliza o ID da loja se não estiver no dicionário
        return store_names.get(store_id, store_id.capitalize())
